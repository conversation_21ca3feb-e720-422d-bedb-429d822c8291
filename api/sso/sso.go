// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package sso

import (
	"context"

	"mlops/api/sso/v1"
)

type ISsoV1 interface {
	SsoLogin(ctx context.Context, req *v1.SsoLoginReq) (res *v1.SsoLoginRes, err error)
}
