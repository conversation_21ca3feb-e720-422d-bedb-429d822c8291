package v1

import (
	"github.com/gogf/gf/v2/frame/g"
	"mlops/tools/common"
)

type OpenApiTokenListReq struct {
	g.Meta `path:"/openapi-token/list" tags:"OpenApiToken" method:"get" summary:"list openapi token" role:"admin,consoleAdmin"`
	common.ListReq
}
type OpenApiTokenListRes struct {
	common.ListRes
}

type OpenApiCreateTokenReq struct {
	g.Meta `path:"/openapi-token/create" tags:"OpenApiToken" method:"post" summary:"create openapi token" role:"admin,consoleAdmin"`
	Name   string `json:"name" v:"required# Require name"`
	Desc   string `json:"desc" v:"required# Require desc"`
}

type OpenApiCreateTokenRes struct {
}

type OpenApiDeleteTokenReq struct {
	g.Meta `path:"/openapi-token/delete/:pid" tags:"OpenApiToken" method:"delete" summary:"delete openapi token" role:"admin,consoleAdmin"`
}
type OpenApiDeleteTokenRes struct {
}
