// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package openapi_token

import (
	"context"

	"mlops/api/openapi_token/v1"
)

type IOpenapiTokenV1 interface {
	OpenApiTokenList(ctx context.Context, req *v1.OpenApiTokenListReq) (res *v1.OpenApiTokenListRes, err error)
	OpenApiCreateToken(ctx context.Context, req *v1.OpenApiCreateTokenReq) (res *v1.OpenApiCreateTokenRes, err error)
	OpenApiDeleteToken(ctx context.Context, req *v1.OpenApiDeleteTokenReq) (res *v1.OpenApiDeleteTokenRes, err error)
}
