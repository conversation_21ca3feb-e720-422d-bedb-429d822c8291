// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package traintask

import (
	"context"

	"mlops/api/traintask/v1"
)

type ITraintaskV1 interface {
	ListTrainTask(ctx context.Context, req *v1.ListTrainTaskReq) (res *v1.ListTrainTaskRes, err error)
	GetTrainTask(ctx context.Context, req *v1.GetTrainTaskReq) (res *v1.GetTrainTaskRes, err error)
	CreateTrainTask(ctx context.Context, req *v1.CreateTrainTaskReq) (res *v1.CreateTrainTaskRes, err error)
	UpdateTrainTask(ctx context.Context, req *v1.UpdateTrainTaskReq) (res *v1.UpdateTrainTaskRes, err error)
	DeleteTrainTask(ctx context.Context, req *v1.DeleteTrainTaskReq) (res *v1.DeleteTrainTaskRes, err error)
	TriggerTrainTask(ctx context.Context, req *v1.TriggerTrainTaskReq) (res *v1.TriggerTrainTaskRes, err error)
	ListConfigmapWithClusterNamespace(ctx context.Context, req *v1.ListConfigmapWithClusterNamespaceReq) (res *v1.ListConfigmapWithClusterNamespaceRes, err error)
	ListSecretWithClusterNamespace(ctx context.Context, req *v1.ListSecretWithClusterNamespaceReq) (res *v1.ListSecretWithClusterNamespaceRes, err error)
	ListPvcWithClusterNamespace(ctx context.Context, req *v1.ListPvcWithClusterNamespaceReq) (res *v1.ListPvcWithClusterNamespaceRes, err error)
}
