package v1

import (
	"github.com/gogf/gf/v2/frame/g"
	"mlops/internal/model/dto"
)

type OpenApiTReq struct {
	g.Meta `path:"/test" tags:"OpenApi" method:"get" summary:"test openapi"`
}

type OpenApiTRes struct {
}

// req
type ListTeamQuotaReq struct {
	g.Meta `path:"/team/quota/list" method:"get" tags:"OpenApi" sm:"list team quota"`
}

type ListTeamQuotaRes struct {
	TeamQuotas []*dto.TeamGpuQuota `json:"teamQuota"`
}
