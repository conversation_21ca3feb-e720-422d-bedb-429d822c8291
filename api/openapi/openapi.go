// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package openapi

import (
	"context"

	"mlops/api/openapi/v1"
)

type IOpenapiV1 interface {
	OpenApiT(ctx context.Context, req *v1.OpenApiTReq) (res *v1.OpenApiTRes, err error)
	ListTeamQuota(ctx context.Context, req *v1.ListTeamQuotaReq) (res *v1.ListTeamQuotaRes, err error)
}
