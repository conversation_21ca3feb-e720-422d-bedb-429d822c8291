package v1

import (
	"mlops/internal/model/dto"

	"github.com/gogf/gf/v2/frame/g"
)

type ListGpuReq struct {
	g.Meta `path:"/resource/gpu/list" method:"get" tags:"Resource" sm:"list gpu"`
}

type ListGpuRes struct {
	GpuList []*dto.GpuDetail `json:"gpuList"`
}

type CreateGpuListReq struct {
	g.Meta  `path:"/resource/gpu/create" tags:"Resource" method:"post" summary:"create gpu list"`
	GpuList dto.GpuDetail `json:"gpuDetail"`
}

type CreateGpuListRes struct {
}

type ListClusterReq struct {
	g.Meta `path:"/resource/cluster/list" method:"get" tags:"Resource" sm:"list cluster"`
}

type ListClusterRes struct {
	ClusterList []string `json:"clusterList"`
}

type ListNamespaceReq struct {
	g.Meta  `path:"/resource/namespace/list" method:"get" tags:"Resource" sm:"list namespace"`
	Cluster string `json:"cluster" v:"required#cluster is required"`
}

type ListNamespaceRes struct {
	NamespaceList []string `json:"namespaceList"`
}

type ListGpuOverviewReq struct {
	g.Meta `path:"/resource/gpu-overview/list" method:"get" tags:"Resource" sm:"overview gpu quota"`
}

type ListGpuOverviewRes struct {
	GpuOverview []*dto.GpuOverview `json:"gpuOverview"`
}

type ListTeamOverviewReq struct {
	g.Meta `path:"/resource/team-overview/list" method:"get" tags:"Resource" sm:"overview team quota"`
	TeamId string `p:"teamId"`
}

type ListTeamOverviewRes struct {
	TeamOverview []*dto.TeamOverview `json:"teamOverview"`
}

type ListGpuMonitorReq struct {
	g.Meta `path:"/resource/gpu-monitor/list" method:"get" tags:"Resource" sm:"monitor gpu quota"`
	TeamId int `json:"teamId"`
}

type ListGpuMonitorRes struct {
	Running                     []*dto.GpuQuotaFloat `json:"running"`
	Pending                     []*dto.GpuQuotaFloat `json:"pending"`
	Idle                        []*dto.GpuQuotaFloat `json:"idle"`
	RunningTrainTask            []*dto.GpuQuotaFloat `json:"runningTrainTask"`
	RunningOnlineDevelopment    []*dto.GpuQuotaFloat `json:"runningOnlineDevelopment"`
	EstimatedPendingTimeSeconds uint                 `json:"estimatedPendingTimeSeconds"`
}
