package v1

import (
	"github.com/gogf/gf/v2/frame/g"
	"mlops/internal/model/dto"
)

type ListGpuReq struct {
	g.Meta `path:"/resource/gpu/list" method:"get" tags:"Resource" sm:"list gpu"`
}

type ListGpuRes struct {
	GpuList []*dto.GpuDetail `json:"gpuList"`
}

type CreateGpuListReq struct {
	g.Meta  `path:"/resource/gpu/create" tags:"Resource" method:"post" summary:"create gpu list"`
	GpuList dto.GpuDetail `json:"gpuDetail"`
}

type CreateGpuListRes struct {
}

type ListClusterReq struct {
	g.Meta `path:"/resource/cluster/list" method:"get" tags:"Resource" sm:"list cluster"`
}

type ListClusterRes struct {
	ClusterList []string `json:"clusterList"`
}

type ListNamespaceReq struct {
	g.Meta  `path:"/resource/namespace/list" method:"get" tags:"Resource" sm:"list namespace"`
	Cluster string `json:"cluster" v:"required#cluster is required"`
}

type ListNamespaceRes struct {
	NamespaceList []string `json:"namespaceList"`
}
