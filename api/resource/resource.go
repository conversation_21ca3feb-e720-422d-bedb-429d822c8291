// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package resource

import (
	"context"

	"mlops/api/resource/v1"
)

type IResourceV1 interface {
	ListGpu(ctx context.Context, req *v1.ListGpuReq) (res *v1.ListGpuRes, err error)
	CreateGpuList(ctx context.Context, req *v1.CreateGpuListReq) (res *v1.CreateGpuListRes, err error)
	ListCluster(ctx context.Context, req *v1.ListClusterReq) (res *v1.ListClusterRes, err error)
	ListNamespace(ctx context.Context, req *v1.ListNamespaceReq) (res *v1.ListNamespaceRes, err error)
	ListGpuOverview(ctx context.Context, req *v1.ListGpuOverviewReq) (res *v1.ListGpuOverviewRes, err error)
	ListTeamOverview(ctx context.Context, req *v1.ListTeamOverviewReq) (res *v1.ListTeamOverviewRes, err error)
	ListGpuMonitor(ctx context.Context, req *v1.ListGpuMonitorReq) (res *v1.ListGpuMonitorRes, err error)
}
