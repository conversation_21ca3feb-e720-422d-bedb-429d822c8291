package v1

import (
	"mlops/internal/model/dto"

	"github.com/gogf/gf/v2/frame/g"
)

type ListTrainTaskExecutionReq struct {
	g.Meta `path:"/traintaskexecution/list" method:"get" tags:"TrainTaskExecution" summary:"list train task execution"`
	dto.TrainTaskExecutionListInput
}

type ListTrainTaskExecutionRes struct {
	dto.TrainTaskExecutionListOutput
}

type GetTrainTaskExecutionReq struct {
	g.Meta `path:"/traintaskexecution/:id" method:"get" tags:"TrainTaskExecution" summary:"get train task execution"`
}

type GetTrainTaskExecutionRes struct {
	Data interface{} `json:"data"`
}

type InterruptTrainTaskExecutionReq struct {
	g.Meta `path:"/traintaskexecution/interrupt/:id" method:"post" tags:"TrainTaskExecution" summary:"interrupt train task execution"`
}

type InterruptTrainTaskExecutionRes struct {
}

type GetCustomTrainTaskExecutionMonitorUrlReq struct {
	g.Meta `path:"/traintaskexecution/monitor-url/:id" method:"get" tags:"TrainTaskExecution" summary:"get custom train task execution monitor-url"`
}

type GetCustomTrainTaskExecutionMonitorUrlRes struct {
	Url string `json:"url"`
}

type UpdateTrainTaskExecutionPriorityReq struct {
	g.Meta   `path:"/traintaskexecution/priority/:id" method:"patch" tags:"TrainTaskExecution" summary:"update train task execution priority"`
	Priority string `json:"priority"`
}

type UpdateTrainTaskExecutionPriorityRes struct {
}
