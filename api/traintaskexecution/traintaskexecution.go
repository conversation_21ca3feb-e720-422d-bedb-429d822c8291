// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package traintaskexecution

import (
	"context"

	"mlops/api/traintaskexecution/v1"
)

type ITraintaskexecutionV1 interface {
	ListTrainTaskExecution(ctx context.Context, req *v1.ListTrainTaskExecutionReq) (res *v1.ListTrainTaskExecutionRes, err error)
	GetTrainTaskExecution(ctx context.Context, req *v1.GetTrainTaskExecutionReq) (res *v1.GetTrainTaskExecutionRes, err error)
	InterruptTrainTaskExecution(ctx context.Context, req *v1.InterruptTrainTaskExecutionReq) (res *v1.InterruptTrainTaskExecutionRes, err error)
	GetCustomTrainTaskExecutionMonitorUrl(ctx context.Context, req *v1.GetCustomTrainTaskExecutionMonitorUrlReq) (res *v1.GetCustomTrainTaskExecutionMonitorUrlRes, err error)
	UpdateTrainTaskExecutionPriority(ctx context.Context, req *v1.UpdateTrainTaskExecutionPriorityReq) (res *v1.UpdateTrainTaskExecutionPriorityRes, err error)
}
