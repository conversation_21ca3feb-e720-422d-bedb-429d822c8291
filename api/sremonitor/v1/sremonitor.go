package v1

import (
	"github.com/gogf/gf/v2/frame/g"
	"mlops/internal/model/dto"
)

// QueryGetReq GET查询请求
type QueryGetReq struct {
	g.Meta  `path:"/sre-monitor/query" method:"get" tags:"SreMonitor" sm:"query prometheus metrics via GET"`
	Query   string `json:"query" v:"required#PromQL query is required"`
	Time    string `json:"time"`
	Timeout string `json:"timeout"`
}

// QueryGetRes GET查询响应
type QueryGetRes struct {
	*dto.SreMonitorResponse
}

// QueryPostReq POST查询请求
type QueryPostReq struct {
	g.Meta  `path:"/sre-monitor/query" method:"post" tags:"SreMonitor" sm:"query prometheus metrics via POST"`
	Query   string `json:"query" v:"required#PromQL query is required"`
	Time    string `json:"time"`
	Timeout string `json:"timeout"`
}

// QueryPostRes POST查询响应
type QueryPostRes struct {
	*dto.SreMonitorResponse
}

// QueryRangeGetReq GET范围查询请求
type QueryRangeGetReq struct {
	g.Meta  `path:"/sre-monitor/query_range" method:"get" tags:"SreMonitor" sm:"query prometheus metrics range via GET"`
	Query   string `json:"query" v:"required#PromQL query is required"`
	Start   string `json:"start" v:"required#start time is required"`
	End     string `json:"end" v:"required#end time is required"`
	Step    string `json:"step" v:"required#step is required"`
	Timeout string `json:"timeout"`
}

// QueryRangeGetRes GET范围查询响应
type QueryRangeGetRes struct {
	*dto.SreMonitorResponse
}

// QueryRangePostReq POST范围查询请求
type QueryRangePostReq struct {
	g.Meta  `path:"/sre-monitor/query_range" method:"post" tags:"SreMonitor" sm:"query prometheus metrics range via POST"`
	Query   string `json:"query" v:"required#PromQL query is required"`
	Start   string `json:"start" v:"required#start time is required"`
	End     string `json:"end" v:"required#end time is required"`
	Step    string `json:"step" v:"required#step is required"`
	Timeout string `json:"timeout"`
}

// QueryRangePostRes POST范围查询响应
type QueryRangePostRes struct {
	*dto.SreMonitorResponse
}
