// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package sremonitor

import (
	"context"

	"mlops/api/sremonitor/v1"
)

type ISremonitorV1 interface {
	QueryGet(ctx context.Context, req *v1.QueryGetReq) (res *v1.QueryGetRes, err error)
	QueryPost(ctx context.Context, req *v1.QueryPostReq) (res *v1.QueryPostRes, err error)
	QueryRangeGet(ctx context.Context, req *v1.QueryRangeGetReq) (res *v1.QueryRangeGetRes, err error)
	QueryRangePost(ctx context.Context, req *v1.QueryRangePostReq) (res *v1.QueryRangePostRes, err error)
}
