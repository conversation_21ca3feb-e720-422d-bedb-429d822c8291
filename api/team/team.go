// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package team

import (
	"context"

	"mlops/api/team/v1"
)

type ITeamV1 interface {
	ListClusterNamespace(ctx context.Context, req *v1.ListClusterNamespaceReq) (res *v1.ListClusterNamespaceRes, err error)
	ListTeam(ctx context.Context, req *v1.ListTeamReq) (res *v1.ListTeamRes, err error)
	ListTeamUser(ctx context.Context, req *v1.ListTeamUserReq) (res *v1.ListTeamUserRes, err error)
	CreateClusterNamespace(ctx context.Context, req *v1.CreateClusterNamespaceReq) (res *v1.CreateClusterNamespaceRes, err error)
	DeleteClusterNamespace(ctx context.Context, req *v1.DeleteClusterNamespaceReq) (res *v1.DeleteClusterNamespaceRes, err error)
	ListGpuQuota(ctx context.Context, req *v1.ListGpuQuotaReq) (res *v1.ListGpuQuotaRes, err error)
	CreateGpuQuota(ctx context.Context, req *v1.CreateGpuQuotaReq) (res *v1.CreateGpuQuotaRes, err error)
	UpdateGpuQuota(ctx context.Context, req *v1.UpdateGpuQuotaReq) (res *v1.UpdateGpuQuotaRes, err error)
	ListTeamApp(ctx context.Context, req *v1.ListTeamAppReq) (res *v1.ListTeamAppRes, err error)
	DeleteGpuQuota(ctx context.Context, req *v1.DeleteGpuQuotaReq) (res *v1.DeleteGpuQuotaRes, err error)
}
