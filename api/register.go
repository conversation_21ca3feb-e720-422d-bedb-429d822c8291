package api

import (
	"context"
	settingv1 "mlops/api/setting/v1"
	userv1 "mlops/api/user/v1"
	"mlops/internal/service"
)

func ApiRegister(ctx context.Context) {

	service.PlatFormAuth().ApiRegister(ctx,
		// admin, consoleAdmin
		settingv1.ListSettingReq{},
		settingv1.GetSettingReq{},
		settingv1.CreateSettingReq{},
		settingv1.UpdateSettingReq{},
		settingv1.DeleteSettingReq{},

		userv1.ListUserReq{},
		userv1.GetUserProfileReq{},
		userv1.SyncTeamReq{},
		userv1.SetAdminReq{},
		userv1.SetConsoleAdminReq{},

		// add you to be auth api here
	)
}
