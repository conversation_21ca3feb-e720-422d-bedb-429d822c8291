// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package onlinedevelopment

import (
	"context"

	"mlops/api/onlinedevelopment/v1"
)

type IOnlinedevelopmentV1 interface {
	ListOnlineDevelopment(ctx context.Context, req *v1.ListOnlineDevelopmentReq) (res *v1.ListOnlineDevelopmentRes, err error)
	GetOnlineDevelopment(ctx context.Context, req *v1.GetOnlineDevelopmentReq) (res *v1.GetOnlineDevelopmentRes, err error)
	CreateOnlineDevelopment(ctx context.Context, req *v1.CreateOnlineDevelopmentReq) (res *v1.CreateOnlineDevelopmentRes, err error)
	UpdateOnlineDevelopment(ctx context.Context, req *v1.UpdateOnlineDevelopmentReq) (res *v1.UpdateOnlineDevelopmentRes, err error)
	DeleteOnlineDevelopment(ctx context.Context, req *v1.DeleteOnlineDevelopmentReq) (res *v1.DeleteOnlineDevelopmentRes, err error)
	StartOnlineDevelopment(ctx context.Context, req *v1.StartOnlineDevelopmentReq) (res *v1.StartOnlineDevelopmentRes, err error)
	StopOnlineDevelopment(ctx context.Context, req *v1.StopOnlineDevelopmentReq) (res *v1.StopOnlineDevelopmentRes, err error)
	ListOnlineDevelopmentImage(ctx context.Context, req *v1.ListOnlineDevelopmentImageReq) (res *v1.ListOnlineDevelopmentImageRes, err error)
}
