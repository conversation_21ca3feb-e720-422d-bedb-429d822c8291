// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package setting

import (
	"context"

	"mlops/api/setting/v1"
)

type ISettingV1 interface {
	ListSetting(ctx context.Context, req *v1.ListSettingReq) (res *v1.ListSettingRes, err error)
	GetSetting(ctx context.Context, req *v1.GetSettingReq) (res *v1.GetSettingRes, err error)
	CreateSetting(ctx context.Context, req *v1.CreateSettingReq) (res *v1.CreateSettingRes, err error)
	UpdateSetting(ctx context.Context, req *v1.UpdateSettingReq) (res *v1.UpdateSettingRes, err error)
	DeleteSetting(ctx context.Context, req *v1.DeleteSettingReq) (res *v1.DeleteSettingRes, err error)
}
