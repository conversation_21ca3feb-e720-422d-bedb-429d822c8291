package v1

import (
	"github.com/gogf/gf/v2/frame/g"
	"mlops/tools/common"
)

// 分页setting
type ListSettingReq struct {
	g.Meta `path:"/setting/list" method:"get" tags:"Setting" sm:"list setting" role:"admin,consoleAdmin"`
	common.ListReq
}

type ListSettingRes struct {
	*common.ListRes
}

// 根据pid查setting
type GetSettingReq struct {
	g.Meta `path:"/setting/:pid" method:"get" tags:"Setting" sm:"get setting" role:"admin,consoleAdmin"`
}

type GetSettingRes struct {
	Data interface{} `json:"data"`
}

type CreateSettingReq struct {
	g.Meta   `path:"/setting/create" method:"post" tags:"Setting" sm:"create setting" role:"admin,consoleAdmin"`
	Key      string `json:"key"`
	Value    string `json:"value"`
	Desc     string `json:"desc"`
	Category string `json:"category"`
}

type CreateSettingRes struct {
}

type UpdateSettingReq struct {
	g.Meta   `path:"/setting/update/:pid" method:"patch" tags:"Setting" sm:"update setting" role:"admin,consoleAdmin"`
	Value    string `json:"value"`
	Desc     string `json:"desc"`
	Category string `json:"category"`
}

type UpdateSettingRes struct {
}

type DeleteSettingReq struct {
	g.Meta `path:"/setting/delete/:pid" method:"delete" tags:"Setting" sm:"delete setting" role:"admin,consoleAdmin"`
}

type DeleteSettingRes struct {
}
