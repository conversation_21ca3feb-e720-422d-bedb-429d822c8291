package v1

import (
	"github.com/gogf/gf/v2/frame/g"
	"time"
)

type AuthLoginReq struct {
	g.Meta   `path:"/auth/login" tags:"JwtAuth" method:"post" summary:"user login"`
	Username string `json:"username" v:"required#用户名不能为空"`
	Password string `json:"password" v:"required#密码不能为空"`
}
type AuthLoginRes struct {
	Token  string    `json:"token"`
	Expire time.Time `json:"expire"`
}

type AuthLogoutReq struct {
	g.Meta `path:"/auth/logout" tags:"JwtAuth" method:"post" summary:"user logout"`
}
type AuthLogoutRes struct {
}
