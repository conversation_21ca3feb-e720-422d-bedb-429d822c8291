package strategies

import (
	v1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/api/resource"
)

const (
	KEY_QGPU_CORE = "tke.cloud.tencent.com/qgpu-core"
	KEY_QGPU_MEM  = "tke.cloud.tencent.com/qgpu-memory"
	KEY_ENI_IP    = "tke.cloud.tencent.com/eni-ip"
)

type TencentStrategy struct{}

func (s *TencentStrategy) Convert(list map[v1.ResourceName]resource.Quantity) {
	if list == nil {
		return
	}
	if v, ok := list[OAM_GPU_CORE]; ok {
		list[KEY_QGPU_CORE] = v.DeepCopy()
		delete(list, OAM_GPU_CORE)
	}
	if v, ok := list[OAM_GPU_MEM]; ok {
		list[KEY_QGPU_MEM] = v.DeepCopy()
		delete(list, OAM_GPU_MEM)
	}
	if v, ok := list[OAM_ENI_IP]; ok {
		list[KEY_ENI_IP] = v.DeepCopy()
		delete(list, OAM_ENI_IP)
	}
}
