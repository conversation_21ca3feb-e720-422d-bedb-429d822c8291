package strategies

import (
	v1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/api/resource"
)

const (
	KEY_ALI_GPU_CORE = "aliyun.com/gpu-core.percentage"
	KEY_ALI_GPU_MEM  = "aliyun.com/gpu-mem"
)

type AliyunStrategy struct{}

func (s *AliyunStrategy) Convert(list map[v1.ResourceName]resource.Quantity) {
	if list == nil {
		return
	}
	if v, ok := list[OAM_GPU_CORE]; ok {
		if v.Value()%100 == 0 {
			list[KEY_NVIDIA_GPU_CNT] = *resource.NewMilliQuantity(v.MilliValue()/100, resource.DecimalSI)
		} else {
			list[KEY_ALI_GPU_CORE] = v.DeepCopy()
		}
		delete(list, OAM_GPU_CORE)
	}
	if v, ok := list[OAM_GPU_MEM]; ok {
		if _, ok := list[KEY_NVIDIA_GPU_CNT]; !ok {
			list[KEY_ALI_GPU_MEM] = v.DeepCopy()
		}
		delete(list, OAM_GPU_MEM)
	}
}
