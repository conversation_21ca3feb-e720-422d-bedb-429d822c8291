package strategies

import (
	v1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/api/resource"
)

const (
	KEY_HS_GPU_CORE = "vke.volcengine.com/mgpu-core"
	KEY_HS_GPU_MEM  = "vke.volcengine.com/mgpu-memory"
	KEY_HS_ENI_IP   = "vke.volcengine.com/eni-ip"
)

type VolcengineStrategy struct{}

func (s *VolcengineStrategy) Convert(list map[v1.ResourceName]resource.Quantity) {
	if list == nil {
		return
	}
	if v, ok := list[OAM_GPU_CORE]; ok {
		list[KEY_HS_GPU_CORE] = v.DeepCopy()
		delete(list, OAM_GPU_CORE)
	}
	if v, ok := list[OAM_GPU_MEM]; ok {
		list[KEY_HS_GPU_MEM] = *resource.NewMilliQuantity(v.MilliValue()*1024, resource.DecimalSI)
		delete(list, OAM_GPU_MEM)
	}
	if v, ok := list[OAM_ENI_IP]; ok {
		list[KEY_HS_ENI_IP] = v.DeepCopy()
		delete(list, OAM_ENI_IP)
	}
}
