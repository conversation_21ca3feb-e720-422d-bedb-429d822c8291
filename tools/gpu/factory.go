package gpu

import (
	"sync"
	"mlops/tools/gpu/strategies"
)

var (
	strategyRegistry = make(map[string]GPUConversionStrategy)
	registryLock     sync.RWMutex
)

// RegisterStrategy 注册集群策略
func RegisterStrategy(clusterType string, strategy GPUConversionStrategy) {
	registryLock.Lock()
	defer registryLock.Unlock()
	strategyRegistry[clusterType] = strategy
}

// GetStrategy 获取集群策略（工厂方法）
func GetStrategy(clusterType string) GPUConversionStrategy {
	registryLock.RLock()
	defer registryLock.RUnlock()
	return strategyRegistry[clusterType] // 返回 nil 表示不支持该集群
}

// 初始化时注册默认策略
func init() {
	RegisterStrategy("aliyun", &strategies.AliyunStrategy{})
	RegisterStrategy("tencent", &strategies.TencentStrategy{})
	RegisterStrategy("huawei", &strategies.HuaweiStrategy{})
	RegisterStrategy("volcengine", &strategies.VolcengineStrategy{})
}
