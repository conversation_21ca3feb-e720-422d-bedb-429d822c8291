package client

import (
	"context"
	corev1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/api/resource"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	kueuev1beta1 "sigs.k8s.io/kueue/apis/kueue/v1beta1"
)

// ExampleCreateClusterQueue 创建 ClusterQueue 的示例
func ExampleCreateClusterQueue(ctx context.Context, clusterName string) error {
	clusterQueue := &kueuev1beta1.ClusterQueue{
		ObjectMeta: metav1.ObjectMeta{
			Name: "example-cluster-queue",
		},
		Spec: kueuev1beta1.ClusterQueueSpec{
			Cohort: "example-cohort",
			NamespaceSelector: &metav1.LabelSelector{
				MatchLabels: map[string]string{
					"team": "ml-team",
				},
			},
			ResourceGroups: []kueuev1beta1.ResourceGroup{
				{
					CoveredResources: []corev1.ResourceName{
						"cpu",
						"memory",
						"nvidia.com/gpu",
					},
					Flavors: []kueuev1beta1.FlavorQuotas{
						{
							Name: "gpu-flavor",
							Resources: []kueuev1beta1.ResourceQuota{
								{
									Name:         "cpu",
									NominalQuota: resource.MustParse("100"),
								},
								{
									Name:         "memory",
									NominalQuota: resource.MustParse("100Gi"),
								},
								{
									Name:         "nvidia.com/gpu",
									NominalQuota: resource.MustParse("10"),
								},
							},
						},
					},
				},
			},
		},
	}

	return CreateClusterQueue(ctx, clusterName, clusterQueue)
}

// ExampleCreateLocalQueue 创建 LocalQueue 的示例
func ExampleCreateLocalQueue(ctx context.Context, clusterName, namespace string) error {
	localQueue := &kueuev1beta1.LocalQueue{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "example-local-queue",
			Namespace: namespace,
		},
		Spec: kueuev1beta1.LocalQueueSpec{
			ClusterQueue: "example-cluster-queue",
		},
	}

	return CreateLocalQueue(ctx, clusterName, localQueue)
}

// ExampleCreateResourceFlavor 创建 ResourceFlavor 的示例
func ExampleCreateResourceFlavor(ctx context.Context, clusterName string) error {
	resourceFlavor := &kueuev1beta1.ResourceFlavor{
		ObjectMeta: metav1.ObjectMeta{
			Name: "gpu-flavor",
		},
		Spec: kueuev1beta1.ResourceFlavorSpec{
			NodeLabels: map[string]string{
				"gpu-type": "nvidia-v100",
				"zone":     "us-west-1a",
			},
			Tolerations: []corev1.Toleration{
				{
					Key:      "gpu-node",
					Operator: corev1.TolerationOpEqual,
					Value:    "true",
					Effect:   corev1.TaintEffectNoSchedule,
				},
			},
		},
	}

	return CreateResourceFlavor(ctx, clusterName, resourceFlavor)
}

// ExampleCreateWorkloadPriorityClass 创建 WorkloadPriorityClass 的示例
func ExampleCreateWorkloadPriorityClass(ctx context.Context, clusterName string) error {
	workloadPriorityClass := &kueuev1beta1.WorkloadPriorityClass{
		ObjectMeta: metav1.ObjectMeta{
			Name: "high-priority",
		},
		Value:       1000,
		Description: "High priority workloads for production training jobs",
	}

	return CreateWorkloadPriorityClass(ctx, clusterName, workloadPriorityClass)
}

// ExampleListAndGetResources 列出和获取资源的示例
func ExampleListAndGetResources(ctx context.Context, clusterName, namespace string) error {
	// 列出所有 ClusterQueues
	clusterQueues, err := ListClusterQueue(ctx, clusterName)
	if err != nil {
		return err
	}

	// 获取特定的 ClusterQueue
	if len(clusterQueues) > 0 {
		clusterQueue, err := GetClusterQueue(ctx, clusterName, clusterQueues[0].Name)
		if err != nil {
			return err
		}
		_ = clusterQueue // 使用获取到的 ClusterQueue
	}

	// 列出指定命名空间的 LocalQueues
	localQueues, err := ListLocalQueue(ctx, clusterName, namespace)
	if err != nil {
		return err
	}

	// 获取特定的 LocalQueue
	if len(localQueues) > 0 {
		localQueue, err := GetLocalQueue(ctx, clusterName, namespace, localQueues[0].Name)
		if err != nil {
			return err
		}
		_ = localQueue // 使用获取到的 LocalQueue
	}

	// 列出所有 ResourceFlavors
	resourceFlavors, err := ListResourceFlavor(ctx, clusterName)
	if err != nil {
		return err
	}

	// 获取特定的 ResourceFlavor
	if len(resourceFlavors) > 0 {
		resourceFlavor, err := GetResourceFlavor(ctx, clusterName, resourceFlavors[0].Name)
		if err != nil {
			return err
		}
		_ = resourceFlavor // 使用获取到的 ResourceFlavor
	}

	// 列出所有 WorkloadPriorityClasses
	workloadPriorityClasses, err := ListWorkloadPriorityClass(ctx, clusterName)
	if err != nil {
		return err
	}

	// 获取特定的 WorkloadPriorityClass
	if len(workloadPriorityClasses) > 0 {
		workloadPriorityClass, err := GetWorkloadPriorityClass(ctx, clusterName, workloadPriorityClasses[0].Name)
		if err != nil {
			return err
		}
		_ = workloadPriorityClass // 使用获取到的 WorkloadPriorityClass
	}

	return nil
}

// ExampleUpdateResources 更新资源的示例
func ExampleUpdateResources(ctx context.Context, clusterName, namespace string) error {
	// 获取现有的 ClusterQueue 并更新
	clusterQueue, err := GetClusterQueue(ctx, clusterName, "example-cluster-queue")
	if err != nil {
		return err
	}

	// 修改 ClusterQueue 的配置
	clusterQueue.Spec.Cohort = "updated-cohort"

	// 更新 ClusterQueue
	err = UpdateClusterQueue(ctx, clusterName, clusterQueue)
	if err != nil {
		return err
	}

	// 类似地更新其他资源...

	return nil
}

// ExampleDeleteResources 删除资源的示例
func ExampleDeleteResources(ctx context.Context, clusterName, namespace string) error {
	// 删除 LocalQueue
	err := DeleteLocalQueue(ctx, clusterName, namespace, "example-local-queue")
	if err != nil {
		return err
	}

	// 删除 ClusterQueue
	err = DeleteClusterQueue(ctx, clusterName, "example-cluster-queue")
	if err != nil {
		return err
	}

	// 删除 ResourceFlavor
	err = DeleteResourceFlavor(ctx, clusterName, "gpu-flavor")
	if err != nil {
		return err
	}

	// 删除 WorkloadPriorityClass
	err = DeleteWorkloadPriorityClass(ctx, clusterName, "high-priority")
	if err != nil {
		return err
	}

	return nil
}
