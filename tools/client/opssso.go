package client

import (
	"context"
	"fmt"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gcfg"
	"gitlab.ttyuyin.com/yunwei-infra/tt-cloud-constack-sdk/pkg/config"
	"gitlab.ttyuyin.com/yunwei-infra/tt-cloud-constack-sdk/pkg/log"
	"gitlab.ttyuyin.com/yunwei-infra/tt-cloud-constack-sdk/pkg/resources"
	opssso_api "gitlab.ttyuyin.com/yunwei-infra/tt-cloud-constack-sdk/pkg/resources/opssso"
	"net/url"
)

var (
	Opssso *opssso_api.Opssso
)

func init() {
	token, _ := g.Cfg().Get(context.Background(), "api.cmdb.authorization")
	ssoConfig := g.Cfg("sso-config").GetAdapter().(*gcfg.AdapterFile)
	ssoConfig.SetFileName("sso-config.yaml")
	validate_api, err := ssoConfig.Get(context.TODO(), "data.validate_api")
	if err != nil {
		log.L.WithName("opssso.init").Error(context.TODO(), "error:%s", err.Error())
	}

	// 解析 URL
	parsedURL, err := url.Parse(validate_api.(string))
	if err != nil {
		panic(err)
	}

	Opssso = NewOpsSsoClient(context.Background(), token.String(), fmt.Sprintf("%s://%s", parsedURL.Scheme, parsedURL.Host))
}

func NewOpsSsoClient(ctx context.Context, token string, hosts ...string) *opssso_api.Opssso {
	cfg := config.NewConfig(ctx, token)

	cfg.CurrenBaseOn = config.BaseOnK8s
	cfg.Host = ""
	cmdbApiJsonRpc := resources.NewResourceController[*opssso_api.Opssso, opssso_api.Opssso](cfg)

	if len(hosts) != 0 {
		host := hosts[0]
		cmdbApiJsonRpc.OpsSsoHttpClient.SetHost(host)
	}
	return cmdbApiJsonRpc
}
