package client

import (
	"context"
	"fmt"
	"github.com/gogf/gf/v2/frame/g"
	"gitlab.ttyuyin.com/yunwei-infra/tt-cloud-constack-sdk/pkg/log"
	"mlops/tools/client/httpclient"
)

var _ Service = (*ConstackHttpClient)(nil)

var (
	ConstackHttpC *ConstackHttpClient
)

const (
	ListPodPageJumpUrl = "/api/v1/openapi/k8s/resource/pod/list/page-jump"
	ListClusterUrl     = "/api/v1/openapi/permission/cluster/list"
	ListNamespaceUrl   = "/api/v1/openapi/k8s/namespace/list"
)

func init() {
	token, _ := g.Cfg().Get(context.Background(), "api.constack.authorization")
	host, _ := g.Cfg().Get(context.Background(), "api.constack.host")
	ConstackHttpC = NewClient(host.String(), token.String())
}

type ConstackHttpClient struct {
	Host    string
	Token   string
	session httpclient.Session
}

func NewClient(host, token string) *ConstackHttpClient {
	session := httpclient.NewSession(&httpclient.SessionOption{})
	headers := map[string]string{
		"Content-Type": "application/json",
		"X-TOKEN":      token,
	}
	session.SetHeaders(headers)
	client := &ConstackHttpClient{
		Host:    host,
		Token:   token,
		session: session,
	}
	return client
}

// Service _
type Service interface {
	// PageJumpPodList 跳转牵星pod列表，直接返回URL
	PageJumpPodList(context.Context, *PageJumpPodListReq) (string, error)
	// ListCluster 获取集群列表，直接返回集群名称数组
	ListCluster(context.Context) ([]string, error)
	// ListNamespace 获取命名空间列表，直接返回命名空间数组
	ListNamespace(context.Context, string) ([]string, error)
}

type PageJumpPodListReq struct {
	ClusterName string `json:"clusterName"`
	Namespace   string `json:"namespace"`
	PodName     string `json:"name"`
	Username    string `json:"username"`
}

// 内部响应结构，用于解析API返回的完整响应
type pageJumpPodListInternalResp struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
	Data    struct {
		Data struct {
			Url string `json:"url"`
		} `json:"data"`
	} `json:"data"`
}

func (c *ConstackHttpClient) PageJumpPodList(ctx context.Context, req *PageJumpPodListReq) (string, error) {
	httpReq, err := httpclient.NewRequest(fmt.Sprintf("%s"+ListPodPageJumpUrl, c.Host), httpclient.RequestWithJson{Data: req})
	if err != nil {
		log.L.WithName("PageJumpPodList").Warningf(ctx, "new request error:%s", err.Error())
		return "", err
	}
	httpResp, err := c.session.Get(context.Background(), httpReq)
	if err != nil {
		log.L.WithName("PageJumpPodList").Warningf(ctx, "get response error:%s", err.Error())
		return "", err
	}

	var resp pageJumpPodListInternalResp
	if err = httpResp.JsonToStruct(&resp); err != nil {
		log.L.WithName("PageJumpPodList").Warningf(ctx, "unmarshal response error:%s", err.Error())
		return "", err
	}

	// 检查响应状态码
	if resp.Code != 0 {
		log.L.WithName("PageJumpPodList").Warningf(ctx, "API error - code: %d, message: %s, data: %+v", resp.Code, resp.Message, resp.Data)
		return "", fmt.Errorf("API error: %s", resp.Message)
	}

	return resp.Data.Data.Url, nil
}

// 内部响应结构，用于解析集群列表API返回
type listClusterInternalResp struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
	Data    struct {
		Data []string `json:"data"`
	} `json:"data"`
}

func (c *ConstackHttpClient) ListCluster(ctx context.Context) ([]string, error) {
	httpReq, err := httpclient.NewRequest(fmt.Sprintf("%s"+ListClusterUrl, c.Host))
	if err != nil {
		log.L.WithName("ListCluster").Warningf(ctx, "new request error:%s", err.Error())
		return nil, err
	}
	httpResp, err := c.session.Post(context.Background(), httpReq)
	if err != nil {
		log.L.WithName("ListCluster").Warningf(ctx, "get response error:%s", err.Error())
		return nil, err
	}

	var resp listClusterInternalResp
	if err = httpResp.JsonToStruct(&resp); err != nil {
		log.L.WithName("ListCluster").Warningf(ctx, "unmarshal response error:%s", err.Error())
		return nil, err
	}

	// 检查响应状态码
	if resp.Code != 0 {
		log.L.WithName("ListCluster").Warningf(ctx, "API error - code: %d, message: %s, data: %+v", resp.Code, resp.Message, resp.Data)
		return nil, fmt.Errorf("API error: %s", resp.Message)
	}

	return resp.Data.Data, nil
}

type ListNamespaceReq struct {
	ClusterName string `json:"cluster_name"`
}

// 内部响应结构，用于解析命名空间列表API返回
type listNamespaceInternalResp struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
	Data    struct {
		Namespaces []string `json:"data"`
	} `json:"data"`
}

func (c *ConstackHttpClient) ListNamespace(ctx context.Context, clusterName string) ([]string, error) {
	httpReq, err := httpclient.NewRequest(fmt.Sprintf("%s"+ListNamespaceUrl, c.Host), httpclient.RequestWithJson{Data: ListNamespaceReq{clusterName}})
	if err != nil {
		log.L.WithName("ListNamespace").Warningf(ctx, "new request error:%s", err.Error())
		return nil, err
	}
	httpResp, err := c.session.Post(context.Background(), httpReq)
	if err != nil {
		log.L.WithName("ListNamespace").Warningf(ctx, "get response error:%s", err.Error())
		return nil, err
	}

	var resp listNamespaceInternalResp
	if err = httpResp.JsonToStruct(&resp); err != nil {
		log.L.WithName("ListNamespace").Warningf(ctx, "unmarshal response error:%s", err.Error())
		return nil, err
	}

	// 检查响应状态码
	if resp.Code != 0 {
		log.L.WithName("ListNamespace").Warningf(ctx, "API error - code: %d, message: %s, data: %+v", resp.Code, resp.Message, resp.Data)
		return nil, fmt.Errorf("API error: %s", resp.Message)
	}

	return resp.Data.Namespaces, nil
}
