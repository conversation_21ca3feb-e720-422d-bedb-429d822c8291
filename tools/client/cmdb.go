package client

import (
	"context"
	"github.com/gogf/gf/v2/frame/g"
	"gitlab.ttyuyin.com/yunwei-infra/tt-cloud-constack-sdk/pkg/config"
	"gitlab.ttyuyin.com/yunwei-infra/tt-cloud-constack-sdk/pkg/resources"
	cmdb_api "gitlab.ttyuyin.com/yunwei-infra/tt-cloud-constack-sdk/pkg/resources/cmdb/api"
)

var (
	CmdbApiJsonRpc *cmdb_api.JsonRpc
)

func init() {
	token, _ := g.Cfg().Get(context.Background(), "api.cmdb.authorization")
	CmdbApiJsonRpc = NewCmdbApiJsonRpcClient(context.Background(), token.String())
}

func NewCmdbApiJsonRpcClient(ctx context.Context, token string) *cmdb_api.JsonRpc {
	cfg := config.NewConfig(ctx, token)

	cfg.CurrenBaseOn = config.BaseOnK8s

	cmdbApiJsonRpc := resources.NewResourceController[*cmdb_api.JsonRpc, cmdb_api.JsonRpc](cfg)

	return cmdbApiJsonRpc
}
