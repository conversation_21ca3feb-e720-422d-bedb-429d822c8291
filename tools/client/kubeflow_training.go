package client

import (
	"context"
	"encoding/json"
	"fmt"

	"gitlab.ttyuyin.com/yunwei-infra/tt-cloud-constack-sdk/pkg/log"
	constack_openapi "gitlab.ttyuyin.com/yunwei-infra/tt-cloud-constack-sdk/pkg/resources/constack/api.v1/openapi"
	"k8s.io/apimachinery/pkg/runtime"
	trainingv1 "github.com/kubeflow/training-operator/pkg/apis/kubeflow.org/v1"
)

// Kubeflow Training Operator API group and version constants
const (
	TrainingGroup   = "kubeflow.org"
	TrainingVersion = "v1"
)

// Kubeflow Training resource kinds
const (
	TFJobKind      = "tfjobs"
	PyTorchJobKind = "pytorchjobs"
	MPIJobKind     = "mpijobs"
)

// convertTrainingResource converts a Kubernetes object to a map for Training resources
func convertTrainingResource(object runtime.Object) (map[string]interface{}, error) {
	m := make(map[string]interface{})
	marshal, _ := json.Marshal(object)
	err := json.Unmarshal(marshal, &m)

	return m, err
}

// TFJob CRUD operations

// CreateTFJob creates a TFJob resource
func CreateTFJob(ctx context.Context, clusterName string, tfJob *trainingv1.TFJob) (err error) {
	m, err := convertTrainingResource(tfJob)
	if err != nil {
		log.L.WithName("CreateTFJob").Errorf(ctx, "convert tfjob error:%s", err.Error())
		return
	}
	_, err = ConstackResourceAny.CreateWithClusterName(clusterName, m)
	if err != nil {
		log.L.WithName("CreateTFJob").Errorf(ctx, "create tfjob error:%s", err.Error())
		return err
	}

	return nil
}

// GetTFJob gets a TFJob by name and namespace
func GetTFJob(ctx context.Context, clusterName, namespace, name string) (tfJob *trainingv1.TFJob, err error) {
	result, err := ConstackResourceAny.GetWithClusterName(clusterName, namespace,
		TrainingGroup, TrainingVersion, TFJobKind, name)
	if err != nil {
		log.L.WithName("GetTFJob").Errorf(ctx, "get tfjob error:%s", err.Error())
		return nil, err
	}

	tfJobs := []*trainingv1.TFJob{}
	marshal, _ := json.Marshal(result)
	err = json.Unmarshal(marshal, &tfJobs)
	if err != nil {
		log.L.WithName("GetTFJob").Errorf(ctx, "unmarshal tfjob error:%s", err.Error())
		return nil, err
	}
	if len(tfJobs) > 0 {
		return tfJobs[0], nil
	}
	return nil, fmt.Errorf("not found tfjob: %s", name)
}

// ListTFJob lists TFJobs in a namespace with optional label selectors
func ListTFJob(ctx context.Context, clusterName, namespace string, labelSelectors []constack_openapi.LabelSelector) (tfJobs []trainingv1.TFJob, err error) {
	result, err := ConstackResourceAny.ListV2WithClusterName(clusterName, namespace,
		TrainingGroup, TrainingVersion, TFJobKind,
		labelSelectors, []constack_openapi.FieldSelector{})
	if err != nil {
		log.L.WithName("ListTFJob").Errorf(ctx, "list tfjob error:%s", err.Error())
		return nil, err
	}

	tfJobs = make([]trainingv1.TFJob, 0)
	marshal, _ := json.Marshal(result)
	err = json.Unmarshal(marshal, &tfJobs)
	if err != nil {
		log.L.WithName("ListTFJob").Errorf(ctx, "unmarshal tfjob error:%s", err.Error())
		return nil, err
	}

	return
}

// UpdateTFJob updates a TFJob
func UpdateTFJob(ctx context.Context, clusterName string, tfJob *trainingv1.TFJob) (err error) {
	m, err := convertTrainingResource(tfJob)
	if err != nil {
		log.L.WithName("UpdateTFJob").Errorf(ctx, "convert tfjob error:%s", err.Error())
		return
	}
	err = ConstackResourceAny.UpdateWithClusterName(clusterName, m)
	if err != nil {
		log.L.WithName("UpdateTFJob").Errorf(ctx, "update tfjob error:%s", err.Error())
		return err
	}

	return nil
}

// DeleteTFJob deletes a TFJob by name and namespace
func DeleteTFJob(ctx context.Context, clusterName, namespace, name string) (err error) {
	_, err = ConstackResourceAny.DeleteWithClusterName(clusterName, namespace,
		TrainingGroup, TrainingVersion, TFJobKind, name)
	if err != nil {
		log.L.WithName("DeleteTFJob").Errorf(ctx, "delete tfjob error:%s", err.Error())
		return err
	}

	return nil
}

// PyTorchJob CRUD operations

// CreatePyTorchJob creates a PyTorchJob resource
func CreatePyTorchJob(ctx context.Context, clusterName string, pyTorchJob *trainingv1.PyTorchJob) (err error) {
	m, err := convertTrainingResource(pyTorchJob)
	if err != nil {
		log.L.WithName("CreatePyTorchJob").Errorf(ctx, "convert pytorchjob error:%s", err.Error())
		return
	}
	_, err = ConstackResourceAny.CreateWithClusterName(clusterName, m)
	if err != nil {
		log.L.WithName("CreatePyTorchJob").Errorf(ctx, "create pytorchjob error:%s", err.Error())
		return err
	}

	return nil
}

// GetPyTorchJob gets a PyTorchJob by name and namespace
func GetPyTorchJob(ctx context.Context, clusterName, namespace, name string) (pyTorchJob *trainingv1.PyTorchJob, err error) {
	result, err := ConstackResourceAny.GetWithClusterName(clusterName, namespace,
		TrainingGroup, TrainingVersion, PyTorchJobKind, name)
	if err != nil {
		log.L.WithName("GetPyTorchJob").Errorf(ctx, "get pytorchjob error:%s", err.Error())
		return nil, err
	}

	pyTorchJobs := []*trainingv1.PyTorchJob{}
	marshal, _ := json.Marshal(result)
	err = json.Unmarshal(marshal, &pyTorchJobs)
	if err != nil {
		log.L.WithName("GetPyTorchJob").Errorf(ctx, "unmarshal pytorchjob error:%s", err.Error())
		return nil, err
	}
	if len(pyTorchJobs) > 0 {
		return pyTorchJobs[0], nil
	}
	return nil, fmt.Errorf("not found pytorchjob: %s", name)
}

// ListPyTorchJob lists PyTorchJobs in a namespace with optional label selectors
func ListPyTorchJob(ctx context.Context, clusterName, namespace string, labelSelectors []constack_openapi.LabelSelector) (pyTorchJobs []trainingv1.PyTorchJob, err error) {
	result, err := ConstackResourceAny.ListV2WithClusterName(clusterName, namespace,
		TrainingGroup, TrainingVersion, PyTorchJobKind,
		labelSelectors, []constack_openapi.FieldSelector{})
	if err != nil {
		log.L.WithName("ListPyTorchJob").Errorf(ctx, "list pytorchjob error:%s", err.Error())
		return nil, err
	}

	pyTorchJobs = make([]trainingv1.PyTorchJob, 0)
	marshal, _ := json.Marshal(result)
	err = json.Unmarshal(marshal, &pyTorchJobs)
	if err != nil {
		log.L.WithName("ListPyTorchJob").Errorf(ctx, "unmarshal pytorchjob error:%s", err.Error())
		return nil, err
	}

	return
}

// UpdatePyTorchJob updates a PyTorchJob
func UpdatePyTorchJob(ctx context.Context, clusterName string, pyTorchJob *trainingv1.PyTorchJob) (err error) {
	m, err := convertTrainingResource(pyTorchJob)
	if err != nil {
		log.L.WithName("UpdatePyTorchJob").Errorf(ctx, "convert pytorchjob error:%s", err.Error())
		return
	}
	err = ConstackResourceAny.UpdateWithClusterName(clusterName, m)
	if err != nil {
		log.L.WithName("UpdatePyTorchJob").Errorf(ctx, "update pytorchjob error:%s", err.Error())
		return err
	}

	return nil
}

// MPIJob CRUD operations

// CreateMPIJob creates a MPIJob resource
func CreateMPIJob(ctx context.Context, clusterName string, mpiJob *trainingv1.MPIJob) (err error) {
	m, err := convertTrainingResource(mpiJob)
	if err != nil {
		log.L.WithName("CreateMPIJob").Errorf(ctx, "convert mpijob error:%s", err.Error())
		return
	}
	_, err = ConstackResourceAny.CreateWithClusterName(clusterName, m)
	if err != nil {
		log.L.WithName("CreateMPIJob").Errorf(ctx, "create mpijob error:%s", err.Error())
		return err
	}

	return nil
}

// GetMPIJob gets a MPIJob by name and namespace
func GetMPIJob(ctx context.Context, clusterName, namespace, name string) (mpiJob *trainingv1.MPIJob, err error) {
	result, err := ConstackResourceAny.GetWithClusterName(clusterName, namespace,
		TrainingGroup, TrainingVersion, MPIJobKind, name)
	if err != nil {
		log.L.WithName("GetMPIJob").Errorf(ctx, "get mpijob error:%s", err.Error())
		return nil, err
	}

	mpiJobs := []*trainingv1.MPIJob{}
	marshal, _ := json.Marshal(result)
	err = json.Unmarshal(marshal, &mpiJobs)
	if err != nil {
		log.L.WithName("GetMPIJob").Errorf(ctx, "unmarshal mpijob error:%s", err.Error())
		return nil, err
	}
	if len(mpiJobs) > 0 {
		return mpiJobs[0], nil
	}
	return nil, fmt.Errorf("not found mpijob: %s", name)
}

// ListMPIJob lists MPIJobs in a namespace with optional label selectors
func ListMPIJob(ctx context.Context, clusterName, namespace string, labelSelectors []constack_openapi.LabelSelector) (mpiJobs []trainingv1.MPIJob, err error) {
	result, err := ConstackResourceAny.ListV2WithClusterName(clusterName, namespace,
		TrainingGroup, TrainingVersion, MPIJobKind,
		labelSelectors, []constack_openapi.FieldSelector{})
	if err != nil {
		log.L.WithName("ListMPIJob").Errorf(ctx, "list mpijob error:%s", err.Error())
		return nil, err
	}

	mpiJobs = make([]trainingv1.MPIJob, 0)
	marshal, _ := json.Marshal(result)
	err = json.Unmarshal(marshal, &mpiJobs)
	if err != nil {
		log.L.WithName("ListMPIJob").Errorf(ctx, "unmarshal mpijob error:%s", err.Error())
		return nil, err
	}

	return
}

// UpdateMPIJob updates a MPIJob
func UpdateMPIJob(ctx context.Context, clusterName string, mpiJob *trainingv1.MPIJob) (err error) {
	m, err := convertTrainingResource(mpiJob)
	if err != nil {
		log.L.WithName("UpdateMPIJob").Errorf(ctx, "convert mpijob error:%s", err.Error())
		return
	}
	err = ConstackResourceAny.UpdateWithClusterName(clusterName, m)
	if err != nil {
		log.L.WithName("UpdateMPIJob").Errorf(ctx, "update mpijob error:%s", err.Error())
		return err
	}

	return nil
}

// DeleteMPIJob deletes a MPIJob by name and namespace
func DeleteMPIJob(ctx context.Context, clusterName, namespace, name string) (err error) {
	_, err = ConstackResourceAny.DeleteWithClusterName(clusterName, namespace,
		TrainingGroup, TrainingVersion, MPIJobKind, name)
	if err != nil {
		log.L.WithName("DeleteMPIJob").Errorf(ctx, "delete mpijob error:%s", err.Error())
		return err
	}

	return nil
}

// DeletePyTorchJob deletes a PyTorchJob by name and namespace
func DeletePyTorchJob(ctx context.Context, clusterName, namespace, name string) (err error) {
	_, err = ConstackResourceAny.DeleteWithClusterName(clusterName, namespace,
		TrainingGroup, TrainingVersion, PyTorchJobKind, name)
	if err != nil {
		log.L.WithName("DeletePyTorchJob").Errorf(ctx, "delete pytorchjob error:%s", err.Error())
		return err
	}

	return nil
}
