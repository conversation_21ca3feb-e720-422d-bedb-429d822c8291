package client

import (
	"context"

	"github.com/gogf/gf/v2/frame/g"
	"gitlab.ttyuyin.com/yunwei-infra/tt-cloud-constack-sdk/pkg/config"
	"gitlab.ttyuyin.com/yunwei-infra/tt-cloud-constack-sdk/pkg/resources"
	cicd_app_api_v1 "gitlab.ttyuyin.com/yunwei-infra/tt-cloud-constack-sdk/pkg/resources/cicd/b.api.v1/app"
	cicd_sso_api_v1 "gitlab.ttyuyin.com/yunwei-infra/tt-cloud-constack-sdk/pkg/resources/cicd/b.api.v1/sso"
)

var (
	CicdAppProject *cicd_app_api_v1.Project
	CicdSsoSvc     *cicd_sso_api_v1.TokenSvc
)

func init() {
	token, _ := g.Cfg().Get(context.Background(), "api.cicd.authorization")
	CicdAppProject = NewCicdAppProjectClient(context.Background(), token.String())
	CicdSsoSvc = NewCicdSsoClient(context.Background(), token.String())
}

func NewCicdAppProjectClient(ctx context.Context, token string) *cicd_app_api_v1.Project {
	cfg := config.NewConfig(ctx, token)

	cfg.CurrenBaseOn = config.BaseOnK8s

	cicdAppProject := resources.NewResourceController[*cicd_app_api_v1.Project, cicd_app_api_v1.Project](cfg)

	return cicdAppProject
}

func NewCicdSsoClient(ctx context.Context, token string) *cicd_sso_api_v1.TokenSvc {
	cfg := config.NewConfig(ctx, token)

	cfg.CurrenBaseOn = config.BaseOnK8s

	cicdSsoSvc := resources.NewResourceController[*cicd_sso_api_v1.TokenSvc, cicd_sso_api_v1.TokenSvc](cfg)

	return cicdSsoSvc
}
