package client

import (
	"context"
	"crypto/tls"
	"encoding/json"
	"fmt"
	"net/http"
	"time"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/net/gclient"
	"gitlab.ttyuyin.com/yunwei-infra/tt-cloud-constack-sdk/pkg/log"
	visibilityv1beta1 "sigs.k8s.io/kueue/apis/visibility/v1beta1"
)

var (
	K8sApiProxy *K8sApiProxyClient
)

// K8sApiProxyClient K8s API代理客户端
type K8sApiProxyClient struct {
	baseURL string
	token   string
	client  *gclient.Client
}

// PendingWorkloadsRequest 获取待处理工作负载请求参数
type PendingWorkloadsRequest struct {
	ClusterName      string `json:"clusterName" v:"required#cluster name is required"`
	ClusterQueueName string `json:"clusterQueueName" v:"required#cluster queue name is required"`
}

func init() {
	token, _ := g.Cfg().Get(context.Background(), "api.apiproxy.authorization")
	baseURL := "https://apiproxy.ttyuyin.com:8001"
	K8sApiProxy = NewK8sApiProxyClient(baseURL, token.String())
}

// NewK8sApiProxyClient 创建K8s API代理客户端
func NewK8sApiProxyClient(baseURL, token string) *K8sApiProxyClient {
	client := gclient.New()
	client.SetHeader("User-Agent", "MLOps-Platform/k8s-apiproxy")
	client.SetHeader("Authorization", fmt.Sprintf("Bearer %s", token))
	client.SetTimeout(30 * time.Second)

	// 设置跳过SSL验证（因为使用了--insecure）
	client.SetTLSConfig(&tls.Config{InsecureSkipVerify: true})

	return &K8sApiProxyClient{
		baseURL: baseURL,
		token:   token,
		client:  client,
	}
}

// GetPendingWorkloadsSummary 获取指定ClusterQueue的待处理工作负载摘要
func (c *K8sApiProxyClient) GetPendingWorkloadsSummary(ctx context.Context, req *PendingWorkloadsRequest) (*visibilityv1beta1.PendingWorkloadsSummary, error) {
	// 构建请求URL
	requestURL := fmt.Sprintf("%s/proxy/k8s/%s/apis/visibility.kueue.x-k8s.io/v1beta1/clusterqueues/%s/pendingworkloads",
		c.baseURL, req.ClusterName, req.ClusterQueueName)

	log.L.WithName("K8sApiProxy.GetPendingWorkloadsSummary").Infof(ctx, "请求URL: %s", requestURL)

	// 发送GET请求
	response := c.client.GetContent(ctx, requestURL)
	if response == "" {
		return nil, fmt.Errorf("获取响应失败")
	}

	log.L.WithName("K8sApiProxy.GetPendingWorkloadsSummary").Debugf(ctx, "响应内容: %s", response)

	// 解析响应
	var result visibilityv1beta1.PendingWorkloadsSummary
	if err := json.Unmarshal([]byte(response), &result); err != nil {
		log.L.WithName("K8sApiProxy.GetPendingWorkloadsSummary").Errorf(ctx, "解析响应失败: %s", err.Error())
		return nil, fmt.Errorf("解析响应失败: %s", err.Error())
	}

	return &result, nil
}

// GetPendingWorkloadsSummaryWithHTTPClient 使用标准HTTP客户端获取待处理工作负载摘要
func (c *K8sApiProxyClient) GetPendingWorkloadsSummaryWithHTTPClient(ctx context.Context, req *PendingWorkloadsRequest) (*visibilityv1beta1.PendingWorkloadsSummary, error) {
	// 构建请求URL
	requestURL := fmt.Sprintf("%s/proxy/k8s/%s/apis/visibility.kueue.x-k8s.io/v1beta1/clusterqueues/%s/pendingworkloads",
		c.baseURL, req.ClusterName, req.ClusterQueueName)

	log.L.WithName("K8sApiProxy.GetPendingWorkloadsSummaryWithHTTPClient").Infof(ctx, "请求URL: %s", requestURL)

	// 创建HTTP请求
	httpReq, err := http.NewRequestWithContext(ctx, "GET", requestURL, nil)
	if err != nil {
		return nil, fmt.Errorf("创建HTTP请求失败: %s", err.Error())
	}

	// 设置请求头
	httpReq.Header.Set("Authorization", fmt.Sprintf("Bearer %s", c.token))
	httpReq.Header.Set("User-Agent", "MLOps-Platform/k8s-apiproxy")

	// 创建HTTP客户端（跳过SSL验证）
	httpClient := &http.Client{
		Timeout: 30 * time.Second,
		Transport: &http.Transport{
			TLSClientConfig: &tls.Config{InsecureSkipVerify: true},
		},
	}

	// 发送请求
	resp, err := httpClient.Do(httpReq)
	if err != nil {
		log.L.WithName("K8sApiProxy.GetPendingWorkloadsSummaryWithHTTPClient").Errorf(ctx, "发送HTTP请求失败: %s", err.Error())
		return nil, fmt.Errorf("发送HTTP请求失败: %s", err.Error())
	}
	defer resp.Body.Close()

	// 检查响应状态码
	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("HTTP请求失败，状态码: %d", resp.StatusCode)
	}

	// 解析响应
	var result visibilityv1beta1.PendingWorkloadsSummary
	if err := json.NewDecoder(resp.Body).Decode(&result); err != nil {
		log.L.WithName("K8sApiProxy.GetPendingWorkloadsSummaryWithHTTPClient").Errorf(ctx, "解析响应失败: %s", err.Error())
		return nil, fmt.Errorf("解析响应失败: %s", err.Error())
	}

	return &result, nil
}

// GetPendingWorkloadsItems 获取待处理工作负载列表
func (c *K8sApiProxyClient) GetPendingWorkloadsItems(ctx context.Context, req *PendingWorkloadsRequest) ([]visibilityv1beta1.PendingWorkload, error) {
	summary, err := c.GetPendingWorkloadsSummary(ctx, req)
	if err != nil {
		return nil, err
	}

	return summary.Items, nil
}
