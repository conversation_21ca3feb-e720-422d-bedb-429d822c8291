package client

import (
	"context"
	"encoding/json"
	"fmt"
	"net/url"
	"strings"
	"time"

	"github.com/gogf/gf/v2/net/gclient"
	"gitlab.ttyuyin.com/yunwei-infra/tt-cloud-constack-sdk/pkg/log"
)

var (
	SreMonitor *SreMonitorClient
)

// SreMonitorClient SRE监控API客户端
type SreMonitorClient struct {
	baseURL string
	client  *gclient.Client
}

// PrometheusResponse Prometheus API响应结构
type PrometheusResponse struct {
	Status    string      `json:"status"`
	Data      interface{} `json:"data,omitempty"`
	ErrorType string      `json:"errorType,omitempty"`
	Error     string      `json:"error,omitempty"`
	Warnings  []string    `json:"warnings,omitempty"`
}

// QueryData 查询数据结构
type QueryData struct {
	ResultType string        `json:"resultType"`
	Result     []QueryResult `json:"result"`
}

// QueryResult 查询结果
type QueryResult struct {
	Metric map[string]string `json:"metric"`
	Value  []interface{}     `json:"value"`
}

// RangeQueryResult 范围查询结果
type RangeQueryResult struct {
	Metric map[string]string `json:"metric"`
	Values [][]interface{}   `json:"values"`
}

// RangeQueryData 范围查询数据结构
type RangeQueryData struct {
	ResultType string             `json:"resultType"`
	Result     []RangeQueryResult `json:"result"`
}

// QueryRequest 查询请求参数
type QueryRequest struct {
	Query   string `json:"query" v:"required#PromQL query is required"`
	Time    string `json:"time,omitempty"`
	Timeout string `json:"timeout,omitempty"`
}

// RangeQueryRequest 范围查询请求参数
type RangeQueryRequest struct {
	Query   string `json:"query" v:"required#PromQL query is required"`
	Start   string `json:"start" v:"required#start time is required"`
	End     string `json:"end" v:"required#end time is required"`
	Step    string `json:"step" v:"required#step is required"`
	Timeout string `json:"timeout,omitempty"`
}

func init() {
	baseURL := "http://yw-hw-bj-sre-monitor-api.ttyuyin.com"
	SreMonitor = NewSreMonitorClient(baseURL)
}

// NewSreMonitorClient 创建SRE监控客户端
func NewSreMonitorClient(baseURL string) *SreMonitorClient {
	client := gclient.New()
	client.SetHeader("User-Agent", "SRE-Platform/mlops-promql")
	client.SetTimeout(60 * time.Second)

	return &SreMonitorClient{
		baseURL: baseURL,
		client:  client,
	}
}

// QueryGet 使用GET方法查询
func (c *SreMonitorClient) QueryGet(ctx context.Context, req *QueryRequest) (*PrometheusResponse, error) {
	params := url.Values{}
	params.Set("query", req.Query)
	if req.Time != "" {
		params.Set("time", req.Time)
	}
	if req.Timeout != "" {
		params.Set("timeout", req.Timeout)
	}

	queryURL := fmt.Sprintf("%s/api/v1/query?%s", c.baseURL, params.Encode())
	
	log.L.WithName("SreMonitor.QueryGet").Infof(ctx, "请求URL: %s", queryURL)

	response := c.client.GetContent(ctx, queryURL)
	if response == "" {
		return nil, fmt.Errorf("获取响应失败")
	}

	var result PrometheusResponse
	if err := json.Unmarshal([]byte(response), &result); err != nil {
		log.L.WithName("SreMonitor.QueryGet").Errorf(ctx, "解析响应失败: %s", err.Error())
		return nil, fmt.Errorf("解析响应失败: %s", err.Error())
	}

	if result.Status == "error" {
		return &result, fmt.Errorf("查询失败: %s", result.Error)
	}

	return &result, nil
}

// QueryPost 使用POST方法查询
func (c *SreMonitorClient) QueryPost(ctx context.Context, req *QueryRequest) (*PrometheusResponse, error) {
	data := url.Values{}
	data.Set("query", req.Query)
	if req.Time != "" {
		data.Set("time", req.Time)
	}
	if req.Timeout != "" {
		data.Set("timeout", req.Timeout)
	}

	queryURL := fmt.Sprintf("%s/api/v1/query", c.baseURL)
	
	log.L.WithName("SreMonitor.QueryPost").Infof(ctx, "请求URL: %s, 参数: %s", queryURL, data.Encode())

	response := c.client.PostContent(ctx, queryURL, strings.NewReader(data.Encode()))
	if response == "" {
		return nil, fmt.Errorf("获取响应失败")
	}

	var result PrometheusResponse
	if err := json.Unmarshal([]byte(response), &result); err != nil {
		log.L.WithName("SreMonitor.QueryPost").Errorf(ctx, "解析响应失败: %s", err.Error())
		return nil, fmt.Errorf("解析响应失败: %s", err.Error())
	}

	if result.Status == "error" {
		return &result, fmt.Errorf("查询失败: %s", result.Error)
	}

	return &result, nil
}

// QueryRangeGet 使用GET方法进行范围查询
func (c *SreMonitorClient) QueryRangeGet(ctx context.Context, req *RangeQueryRequest) (*PrometheusResponse, error) {
	params := url.Values{}
	params.Set("query", req.Query)
	params.Set("start", req.Start)
	params.Set("end", req.End)
	params.Set("step", req.Step)
	if req.Timeout != "" {
		params.Set("timeout", req.Timeout)
	}

	queryURL := fmt.Sprintf("%s/api/v1/query_range?%s", c.baseURL, params.Encode())

	log.L.WithName("SreMonitor.QueryRangeGet").Infof(ctx, "请求URL: %s", queryURL)

	response := c.client.GetContent(ctx, queryURL)
	if response == "" {
		return nil, fmt.Errorf("获取响应失败")
	}

	var result PrometheusResponse
	if err := json.Unmarshal([]byte(response), &result); err != nil {
		log.L.WithName("SreMonitor.QueryRangeGet").Errorf(ctx, "解析响应失败: %s", err.Error())
		return nil, fmt.Errorf("解析响应失败: %s", err.Error())
	}

	if result.Status == "error" {
		return &result, fmt.Errorf("查询失败: %s", result.Error)
	}

	return &result, nil
}

// QueryRangePost 使用POST方法进行范围查询
func (c *SreMonitorClient) QueryRangePost(ctx context.Context, req *RangeQueryRequest) (*PrometheusResponse, error) {
	data := url.Values{}
	data.Set("query", req.Query)
	data.Set("start", req.Start)
	data.Set("end", req.End)
	data.Set("step", req.Step)
	if req.Timeout != "" {
		data.Set("timeout", req.Timeout)
	}

	queryURL := fmt.Sprintf("%s/api/v1/query_range", c.baseURL)

	log.L.WithName("SreMonitor.QueryRangePost").Infof(ctx, "请求URL: %s, 参数: %s", queryURL, data.Encode())

	response := c.client.PostContent(ctx, queryURL, strings.NewReader(data.Encode()))
	if response == "" {
		return nil, fmt.Errorf("获取响应失败")
	}

	var result PrometheusResponse
	if err := json.Unmarshal([]byte(response), &result); err != nil {
		log.L.WithName("SreMonitor.QueryRangePost").Errorf(ctx, "解析响应失败: %s", err.Error())
		return nil, fmt.Errorf("解析响应失败: %s", err.Error())
	}

	if result.Status == "error" {
		return &result, fmt.Errorf("查询失败: %s", result.Error)
	}

	return &result, nil
}

// ParseQueryData 解析查询数据
func (c *SreMonitorClient) ParseQueryData(response *PrometheusResponse) (*QueryData, error) {
	if response.Data == nil {
		return nil, fmt.Errorf("响应数据为空")
	}

	dataBytes, err := json.Marshal(response.Data)
	if err != nil {
		return nil, fmt.Errorf("序列化数据失败: %s", err.Error())
	}

	var queryData QueryData
	if err := json.Unmarshal(dataBytes, &queryData); err != nil {
		return nil, fmt.Errorf("解析查询数据失败: %s", err.Error())
	}

	return &queryData, nil
}

// ParseRangeQueryData 解析范围查询数据
func (c *SreMonitorClient) ParseRangeQueryData(response *PrometheusResponse) (*RangeQueryData, error) {
	if response.Data == nil {
		return nil, fmt.Errorf("响应数据为空")
	}

	dataBytes, err := json.Marshal(response.Data)
	if err != nil {
		return nil, fmt.Errorf("序列化数据失败: %s", err.Error())
	}

	var rangeQueryData RangeQueryData
	if err := json.Unmarshal(dataBytes, &rangeQueryData); err != nil {
		return nil, fmt.Errorf("解析范围查询数据失败: %s", err.Error())
	}

	return &rangeQueryData, nil
}
