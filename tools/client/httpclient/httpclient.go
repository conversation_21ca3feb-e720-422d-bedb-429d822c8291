package httpclient

import (
	"bytes"
	"crypto/tls"
	"encoding/json"
	"encoding/xml"
	"fmt"
	"io/ioutil"
	"net/http"
	"net/url"
	"strings"
	"time"
)

var _ Requester = (*Client)(nil)

type Client struct {
	options *Options
	headers map[string]string
	payload []byte
	params  map[string]string
}

func NewClient(payload []byte, params map[string]string, headers ...map[string]string) *Client {
	options := &Options{
		InsecureSkipVerify: true,
		Timeout:            30 * time.Second,
	}
	var defaultHeader map[string]string
	if len(headers) > 0 {
		defaultHeader = headers[0]
	}
	client := &Client{
		options: options,
		headers: defaultHeader,
		payload: payload,
		params:  params,
	}
	return client
}

type Requester interface {
	DoGet(url string, value interface{}, opts ...Option) error
	DoPost(url string, value interface{}, opts ...Option) error
	DoPut(url string, value interface{}, opts ...Option) error
	DoDelete(url string, value interface{}, opts ...Option) error
	DoPatch(url string, value interface{}, opts ...Option) error
}

func Copy(data []byte, value interface{}) error {
	if len(data) > 0 && value != nil {
		err := json.Unmarshal(data, value)
		if err != nil {
			return fmt.Errorf("反序列化结果错误: %w", err)
		}
	}
	return nil
}

func (c *Client) Do(url, method string, value interface{}) error {
	data, err := c.DoReq(url, method)
	if err != nil {
		return err
	}

	if err := Copy(data, value); err != nil {
		return err
	}
	return nil
}

func (c *Client) DoGet(url string, value interface{}, opts ...Option) error {
	for _, opt := range opts {
		opt(c.options)
	}
	if err := c.Do(url, http.MethodGet, value); err != nil {
		return fmt.Errorf("HTTP GET请求发生错误: %v", err)
	}
	return nil
}

func (c *Client) DoPost(url string, value interface{}, opts ...Option) error {
	for _, opt := range opts {
		opt(c.options)
	}
	if err := c.Do(url, http.MethodPost, value); err != nil {
		return fmt.Errorf("HTTP POST请求发生错误: %v", err)
	}
	return nil
}

func (c *Client) DoPut(url string, value interface{}, opts ...Option) error {
	for _, opt := range opts {
		opt(c.options)
	}
	if err := c.Do(url, http.MethodPut, value); err != nil {
		return fmt.Errorf("HTTP PUT请求发生错误: %v", err)
	}
	return nil
}

func (c *Client) DoDelete(url string, value interface{}, opts ...Option) error {
	for _, opt := range opts {
		opt(c.options)
	}
	if err := c.Do(url, http.MethodDelete, value); err != nil {
		return fmt.Errorf("HTTP DELETE请求发生错误: %v", err)
	}
	return nil
}

func (c *Client) DoPatch(url string, value interface{}, opts ...Option) error {
	for _, opt := range opts {
		opt(c.options)
	}
	if err := c.Do(url, http.MethodPatch, value); err != nil {
		return fmt.Errorf("HTTP PATCH请求发生错误: %v", err)
	}
	return nil
}

func NewGET(url string, auth, params, header map[string]string, body interface{}) *ReqRequire {
	newHeader := map[string]string{}
	if header == nil {
		newHeader["Content-Type"] = "application/json; charset=utf-8"
		newHeader["Connection"] = "keep-alive"
	} else {
		for k, v := range header {
			newHeader[k] = v
		}
	}
	call := &ReqRequire{
		Method: "GET",
		URL:    url,
		Auth:   auth,
		Params: params,
		Header: newHeader,
		Body:   body,
	}
	return call
}

// New一个POST请求

func NewPOST(url string, auth, params, header map[string]string, body interface{}) *ReqRequire {
	newHeader := map[string]string{}
	if header == nil {
		newHeader["Content-Type"] = "application/json; charset=utf-8"
		newHeader["Connection"] = "keep-alive"
	} else {
		for k, v := range header {
			newHeader[k] = v
		}
	}
	call := &ReqRequire{
		Method: "POST",
		URL:    url,
		Auth:   auth,
		Params: params,
		Header: newHeader,
		Body:   body,
	}
	return call
}

type ReqRequire struct {
	Method string `default:"GET"`
	URL    string `default:"http://localhost:80/"`
	Auth   map[string]string
	Params map[string]string
	Header map[string]string
	Body   interface{}
}

func (r *ReqRequire) MakeRequest() (*http.Response, error) {
	//请求对象
	client := http.Client{
		Timeout:   time.Duration(500) * time.Second,
		Transport: &http.Transport{TLSClientConfig: &tls.Config{InsecureSkipVerify: true}}, //从容器内部访问https，需要CA证书
	}
	var method string
	if method = r.Method; strings.EqualFold(r.Method, "") {
		method = "GET"
	}
	if strings.EqualFold(r.URL, "") {
		return nil, fmt.Errorf("请求url不能为空")
	}
	var b []byte
	if r.Body != nil {
		b, _ = json.Marshal(r.Body)
	} else {
		b = nil
	}
	//request对象
	req, err := getReqPointer(method, r.URL, b)
	if req == nil {
		return nil, fmt.Errorf("获取Request对象失败: %w", err)
	}
	//设置请求验证
	if r.Auth != nil {
		for key, element := range r.Auth {
			req.SetBasicAuth(key, element)
			break
		}
	}
	//请求参数
	if r.Params != nil {
		q := url.Values{}
		for key, element := range r.Params {
			q.Set(key, element)
		}
		req.URL.RawQuery = q.Encode()
	}
	//请求头
	if r.Header != nil {
		for key, element := range r.Header {
			req.Header.Set(key, element)
		}
	}
	//发送请求
	call, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("发送出错: " + err.Error())
	}
	return call, nil
}

func getReqPointer(m, u string, b []byte) (*http.Request, error) {
	if b != nil {
		if req, err := http.NewRequest(m, u, bytes.NewBuffer(b)); err != nil {
			return nil, err
		} else {
			return req, nil
		}
	} else {
		if req, err := http.NewRequest(m, u, nil); err != nil {
			return nil, err
		} else {
			return req, nil
		}
	}
}

func RespAsStruct(r *http.Response, parseType string, responseStruct interface{}) error {
	if errorText := r.Header.Get("X-Error"); !strings.EqualFold(errorText, "") {
		//关闭请求
		defer r.Body.Close()
		return fmt.Errorf("响应错误: %s", errorText)
	}
	if r.StatusCode == http.StatusOK {
		defer r.Body.Close()
		bodyBytes, err := ioutil.ReadAll(r.Body)
		if err != nil {
			return fmt.Errorf("读响应体为byte[]出错: %w", err)
		}
		if strings.EqualFold(parseType, "json") {
			if err := json.Unmarshal(bodyBytes, responseStruct); err != nil {
				return fmt.Errorf("反序列化json响应体到接收结构体时出错: %w", err)
			}
			return nil
		} else if strings.EqualFold(parseType, "xml") {
			xmlBytes := bytes.Replace(bodyBytes, []byte("<?xml version=\"1.1\" encoding=\"UTF-8\"?>\n"), []byte("<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n"), 1)
			if err := xml.Unmarshal(xmlBytes, responseStruct); err != nil {
				return fmt.Errorf("反序列化xml响应体到接收结构体时出错: %w", err)
			}
			return nil
		} else {
			return fmt.Errorf("接收响应类型出错")
		}
	} else {
		defer r.Body.Close()
		return fmt.Errorf("响应状态不OK: %v", r.StatusCode)
	}
}

func RespAsStrMap(r *http.Response, responseMap *map[string]interface{}) error {
	if errorText := r.Header.Get("X-Error"); !strings.EqualFold(errorText, "") {
		//关闭请求
		defer r.Body.Close()
		return fmt.Errorf("响应错误：%s", errorText)
	}
	if r.StatusCode == http.StatusOK {
		resp, errResp := ioutil.ReadAll(r.Body)
		defer r.Body.Close()
		if errResp != nil {
			return errResp
		}
		err := json.Unmarshal(resp, responseMap)
		if err != nil {
			return err
		}
		return nil
	} else {
		defer r.Body.Close()
		return fmt.Errorf("响应状态不OK：%v", r.StatusCode)
	}
}

func RespAsArrMap(r *http.Response, responseMap *[]interface{}) error {
	if errorText := r.Header.Get("X-Error"); !strings.EqualFold(errorText, "") {
		//关闭请求
		defer r.Body.Close()
		return fmt.Errorf("响应错误：%s", errorText)
	}
	if r.StatusCode == http.StatusOK {
		resp, errResp := ioutil.ReadAll(r.Body)
		defer r.Body.Close()
		if errResp != nil {
			return errResp
		}
		err := json.Unmarshal(resp, responseMap)
		if err != nil {
			return err
		}
		return nil
	} else {
		defer r.Body.Close()
		return fmt.Errorf("响应状态不OK：%v", r.StatusCode)
	}
}

func RespAsStr(r *http.Response, responseStruct *string) error {
	if errorText := r.Header.Get("X-Error"); !strings.EqualFold(errorText, "") {
		//关闭请求
		defer r.Body.Close()
		return fmt.Errorf("响应错误：%s", errorText)
	}
	if r.StatusCode == http.StatusOK || r.StatusCode == http.StatusCreated {
		defer r.Body.Close()
		bodyBytes, err := ioutil.ReadAll(r.Body)
		if err != nil {
			return fmt.Errorf("读响应体为byte[]出错：%w", err)
		}
		if len(bodyBytes) > 0 {
			*responseStruct = string(bodyBytes)
		}
		return nil
	} else {
		defer r.Body.Close()
		return fmt.Errorf("响应状态不OK：%v", r.StatusCode)
	}
}

func (c *Client) NewRequest(reqUrl, method string) (*http.Request, error) {
	if c.params != nil {
		params := url.Values{}
		for key, val := range c.params {
			params.Add(key, val)
		}
		queryString := params.Encode()
		if queryString != "" {
			reqUrl = reqUrl + "?" + queryString
		}
	}
	buf := new(bytes.Buffer)
	if len(c.payload) > 0 {
		buf = bytes.NewBuffer(c.payload)
	}
	req, err := http.NewRequest(method, reqUrl, buf)
	if err != nil {
		return nil, err
	}
	defer func() {
		if req.Body != nil {
			_ = req.Body.Close()
		}
	}()
	if len(c.headers) > 0 {
		for key, val := range c.headers {
			req.Header.Add(key, val)
		}
	}

	return req, nil
}

func (c *Client) DoReq(url, method string) ([]byte, error) {
	req, err := c.NewRequest(url, method)
	if err != nil {
		return nil, err
	}
	tr := &http.Transport{
		TLSClientConfig: &tls.Config{
			//解决x509: certificate signed by unknown authority
			InsecureSkipVerify: c.options.InsecureSkipVerify,
		},
	}
	client := &http.Client{
		Timeout:   c.options.Timeout,
		Transport: tr,
	}
	resp, err := client.Do(req)
	if err != nil {
		return nil, err
	}
	defer func() {
		if resp.Body != nil {
			_ = resp.Body.Close()
		}
	}()
	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}
	return body, nil
}
