package client

import (
	"context"
	"encoding/json"
	"fmt"

	"gitlab.ttyuyin.com/yunwei-infra/tt-cloud-constack-sdk/pkg/log"
	constack_openapi "gitlab.ttyuyin.com/yunwei-infra/tt-cloud-constack-sdk/pkg/resources/constack/api.v1/openapi"
	"k8s.io/apimachinery/pkg/runtime"
	kueuev1alpha1 "sigs.k8s.io/kueue/apis/kueue/v1alpha1"
	kueuev1beta1 "sigs.k8s.io/kueue/apis/kueue/v1beta1"
)

// Kueue API group and version constants
const (
	KueueGroup        = "kueue.x-k8s.io"
	KueueVersion      = "v1beta1"
	KueueAlphaVersion = "v1alpha1"
)

// Kueue resource kinds
const (
	ClusterQueueKind          = "clusterqueues"
	LocalQueueKind            = "localqueues"
	ResourceFlavorKind        = "resourceflavors"
	WorkloadPriorityClassKind = "workloadpriorityclasses"
	CohortKind                = "cohorts"
	WorkloadKind              = "workloads"
)

// convertKueueResource converts a Kubernetes object to a map for Kueue resources
func convertKueueResource(object runtime.Object) (map[string]interface{}, error) {
	m := make(map[string]interface{})
	marshal, _ := json.Marshal(object)
	err := json.Unmarshal(marshal, &m)

	return m, err
}

// ClusterQueue CRUD operations

// CreateClusterQueue creates a ClusterQueue resource
func CreateClusterQueue(ctx context.Context, clusterName string, clusterQueue *kueuev1beta1.ClusterQueue) (err error) {
	m, err := convertKueueResource(clusterQueue)
	if err != nil {
		log.L.WithName("CreateClusterQueue").Errorf(ctx, "convert clusterqueue error:%s", err.Error())
		return
	}
	_, err = ConstackResourceAny.CreateWithClusterName(clusterName, m)
	if err != nil {
		log.L.WithName("CreateClusterQueue").Errorf(ctx, "create clusterqueue error:%s", err.Error())
		return err
	}

	return nil
}

// GetClusterQueue gets a ClusterQueue by name
func GetClusterQueue(ctx context.Context, clusterName, name string) (clusterQueue *kueuev1beta1.ClusterQueue, err error) {
	// ClusterQueue is cluster-scoped, so namespace is empty
	result, err := ConstackResourceAny.GetWithClusterName(clusterName, "",
		KueueGroup, KueueVersion, ClusterQueueKind, name)
	if err != nil {
		log.L.WithName("GetClusterQueue").Errorf(ctx, "get clusterqueue error:%s", err.Error())
		return nil, err
	}

	clusterQueues := []*kueuev1beta1.ClusterQueue{}
	marshal, _ := json.Marshal(result)
	err = json.Unmarshal(marshal, &clusterQueues)
	if err != nil {
		log.L.WithName("GetClusterQueue").Errorf(ctx, "unmarshal clusterqueue error:%s", err.Error())
		return nil, err
	}
	if len(clusterQueues) > 0 {
		return clusterQueues[0], nil
	}
	return nil, fmt.Errorf("not found clusterqueue: %s", name)
}

// ListClusterQueue lists ClusterQueues with optional label selectors
func ListClusterQueue(ctx context.Context, clusterName string, labelSelectors []constack_openapi.LabelSelector) (clusterQueues []kueuev1beta1.ClusterQueue, err error) {
	// ClusterQueue is cluster-scoped, so namespace is empty
	result, err := ConstackResourceAny.ListV2WithClusterName(clusterName, "",
		KueueGroup, KueueVersion, ClusterQueueKind,
		labelSelectors, []constack_openapi.FieldSelector{})
	if err != nil {
		log.L.WithName("ListClusterQueue").Errorf(ctx, "list clusterqueue error:%s", err.Error())
		return nil, err
	}

	clusterQueues = make([]kueuev1beta1.ClusterQueue, 0)
	marshal, _ := json.Marshal(result)
	err = json.Unmarshal(marshal, &clusterQueues)
	if err != nil {
		log.L.WithName("ListClusterQueue").Errorf(ctx, "unmarshal clusterqueue error:%s", err.Error())
		return nil, err
	}

	return
}

// UpdateClusterQueue updates a ClusterQueue
func UpdateClusterQueue(ctx context.Context, clusterName string, clusterQueue *kueuev1beta1.ClusterQueue) (err error) {
	m, err := convertKueueResource(clusterQueue)
	if err != nil {
		log.L.WithName("UpdateClusterQueue").Errorf(ctx, "convert clusterqueue error:%s", err.Error())
		return
	}
	err = ConstackResourceAny.UpdateWithClusterName(clusterName, m)
	if err != nil {
		log.L.WithName("UpdateClusterQueue").Errorf(ctx, "update clusterqueue error:%s", err.Error())
		return err
	}

	return nil
}

// DeleteClusterQueue deletes a ClusterQueue by name
func DeleteClusterQueue(ctx context.Context, clusterName, name string) (err error) {
	// ClusterQueue is cluster-scoped, so namespace is empty
	_, err = ConstackResourceAny.DeleteWithClusterName(clusterName, "",
		KueueGroup, KueueVersion, ClusterQueueKind, name)
	if err != nil {
		log.L.WithName("DeleteClusterQueue").Errorf(ctx, "delete clusterqueue error:%s", err.Error())
		return err
	}

	return nil
}

// LocalQueue CRUD operations

// CreateLocalQueue creates a LocalQueue resource
func CreateLocalQueue(ctx context.Context, clusterName string, localQueue *kueuev1beta1.LocalQueue) (err error) {
	m, err := convertKueueResource(localQueue)
	if err != nil {
		log.L.WithName("CreateLocalQueue").Errorf(ctx, "convert localqueue error:%s", err.Error())
		return
	}
	_, err = ConstackResourceAny.CreateWithClusterName(clusterName, m)
	if err != nil {
		log.L.WithName("CreateLocalQueue").Errorf(ctx, "create localqueue error:%s", err.Error())
		return err
	}

	return nil
}

// GetLocalQueue gets a LocalQueue by name and namespace
func GetLocalQueue(ctx context.Context, clusterName, namespace, name string) (localQueue *kueuev1beta1.LocalQueue, err error) {
	result, err := ConstackResourceAny.GetWithClusterName(clusterName, namespace,
		KueueGroup, KueueVersion, LocalQueueKind, name)
	if err != nil {
		log.L.WithName("GetLocalQueue").Errorf(ctx, "get localqueue error:%s", err.Error())
		return nil, err
	}

	localQueues := []*kueuev1beta1.LocalQueue{}
	marshal, _ := json.Marshal(result)
	err = json.Unmarshal(marshal, &localQueues)
	if err != nil {
		log.L.WithName("GetLocalQueue").Errorf(ctx, "unmarshal localqueue error:%s", err.Error())
		return nil, err
	}
	if len(localQueues) > 0 {
		return localQueues[0], nil
	}
	return nil, fmt.Errorf("not found localqueue:%s", name)
}

// ListLocalQueue lists LocalQueues in a namespace with optional label selectors
func ListLocalQueue(ctx context.Context, clusterName, namespace string, labelSelectors []constack_openapi.LabelSelector) (localQueues []kueuev1beta1.LocalQueue, err error) {
	result, err := ConstackResourceAny.ListV2WithClusterName(clusterName, namespace,
		KueueGroup, KueueVersion, LocalQueueKind,
		labelSelectors, []constack_openapi.FieldSelector{})
	if err != nil {
		log.L.WithName("ListLocalQueue").Errorf(ctx, "list localqueue error:%s", err.Error())
		return nil, err
	}

	localQueues = make([]kueuev1beta1.LocalQueue, 0)
	marshal, _ := json.Marshal(result)
	err = json.Unmarshal(marshal, &localQueues)
	if err != nil {
		log.L.WithName("ListLocalQueue").Errorf(ctx, "unmarshal localqueue error:%s", err.Error())
		return nil, err
	}

	return
}

// UpdateLocalQueue updates a LocalQueue
func UpdateLocalQueue(ctx context.Context, clusterName string, localQueue *kueuev1beta1.LocalQueue) (err error) {
	m, err := convertKueueResource(localQueue)
	if err != nil {
		log.L.WithName("UpdateLocalQueue").Errorf(ctx, "convert localqueue error:%s", err.Error())
		return
	}
	err = ConstackResourceAny.UpdateWithClusterName(clusterName, m)
	if err != nil {
		log.L.WithName("UpdateLocalQueue").Errorf(ctx, "update localqueue error:%s", err.Error())
		return err
	}

	return nil
}

// DeleteLocalQueue deletes a LocalQueue by name and namespace
func DeleteLocalQueue(ctx context.Context, clusterName, namespace, name string) (err error) {
	_, err = ConstackResourceAny.DeleteWithClusterName(clusterName, namespace,
		KueueGroup, KueueVersion, LocalQueueKind, name)
	if err != nil {
		log.L.WithName("DeleteLocalQueue").Errorf(ctx, "delete localqueue error:%s", err.Error())
		return err
	}

	return nil
}

// ResourceFlavor CRUD operations

// CreateResourceFlavor creates a ResourceFlavor resource
func CreateResourceFlavor(ctx context.Context, clusterName string, resourceFlavor *kueuev1beta1.ResourceFlavor) (err error) {
	m, err := convertKueueResource(resourceFlavor)
	if err != nil {
		log.L.WithName("CreateResourceFlavor").Errorf(ctx, "convert resourceflavor error:%s", err.Error())
		return
	}
	_, err = ConstackResourceAny.CreateWithClusterName(clusterName, m)
	if err != nil {
		log.L.WithName("CreateResourceFlavor").Errorf(ctx, "create resourceflavor error:%s", err.Error())
		return err
	}

	return nil
}

// GetResourceFlavor gets a ResourceFlavor by name
func GetResourceFlavor(ctx context.Context, clusterName, name string) (resourceFlavor *kueuev1beta1.ResourceFlavor, err error) {
	// ResourceFlavor is cluster-scoped, so namespace is empty
	result, err := ConstackResourceAny.GetWithClusterName(clusterName, "",
		KueueGroup, KueueVersion, ResourceFlavorKind, name)
	if err != nil {
		log.L.WithName("GetResourceFlavor").Errorf(ctx, "get resourceflavor error:%s", err.Error())
		return nil, err
	}

	resourceFlavors := []*kueuev1beta1.ResourceFlavor{}
	marshal, _ := json.Marshal(result)
	err = json.Unmarshal(marshal, &resourceFlavors)
	if err != nil {
		log.L.WithName("GetResourceFlavor").Errorf(ctx, "unmarshal resourceflavor error:%s", err.Error())
		return nil, err
	}
	if len(resourceFlavors) > 0 {
		return resourceFlavors[0], nil
	}
	return nil, fmt.Errorf("not found resourceflavor:%s", name)
}

// ListResourceFlavor lists all ResourceFlavors
func ListResourceFlavor(ctx context.Context, clusterName string) (resourceFlavors []kueuev1beta1.ResourceFlavor, err error) {
	// ResourceFlavor is cluster-scoped, so namespace is empty
	result, err := ConstackResourceAny.ListV2WithClusterName(clusterName, "",
		KueueGroup, KueueVersion, ResourceFlavorKind,
		[]constack_openapi.LabelSelector{}, []constack_openapi.FieldSelector{})
	if err != nil {
		log.L.WithName("ListResourceFlavor").Errorf(ctx, "list resourceflavor error:%s", err.Error())
		return nil, err
	}

	resourceFlavors = make([]kueuev1beta1.ResourceFlavor, 0)
	marshal, _ := json.Marshal(result)
	err = json.Unmarshal(marshal, &resourceFlavors)
	if err != nil {
		log.L.WithName("ListResourceFlavor").Errorf(ctx, "unmarshal resourceflavor error:%s", err.Error())
		return nil, err
	}

	return
}

// UpdateResourceFlavor updates a ResourceFlavor
func UpdateResourceFlavor(ctx context.Context, clusterName string, resourceFlavor *kueuev1beta1.ResourceFlavor) (err error) {
	m, err := convertKueueResource(resourceFlavor)
	if err != nil {
		log.L.WithName("UpdateResourceFlavor").Errorf(ctx, "convert resourceflavor error:%s", err.Error())
		return
	}
	err = ConstackResourceAny.UpdateWithClusterName(clusterName, m)
	if err != nil {
		log.L.WithName("UpdateResourceFlavor").Errorf(ctx, "update resourceflavor error:%s", err.Error())
		return err
	}

	return nil
}

// DeleteResourceFlavor deletes a ResourceFlavor by name
func DeleteResourceFlavor(ctx context.Context, clusterName, name string) (err error) {
	// ResourceFlavor is cluster-scoped, so namespace is empty
	_, err = ConstackResourceAny.DeleteWithClusterName(clusterName, "",
		KueueGroup, KueueVersion, ResourceFlavorKind, name)
	if err != nil {
		log.L.WithName("DeleteResourceFlavor").Errorf(ctx, "delete resourceflavor error:%s", err.Error())
		return err
	}

	return nil
}

// WorkloadPriorityClass CRUD operations

// CreateWorkloadPriorityClass creates a WorkloadPriorityClass resource
func CreateWorkloadPriorityClass(ctx context.Context, clusterName string, workloadPriorityClass *kueuev1beta1.WorkloadPriorityClass) (err error) {
	m, err := convertKueueResource(workloadPriorityClass)
	if err != nil {
		log.L.WithName("CreateWorkloadPriorityClass").Errorf(ctx, "convert workloadpriorityclass error:%s", err.Error())
		return
	}
	_, err = ConstackResourceAny.CreateWithClusterName(clusterName, m)
	if err != nil {
		log.L.WithName("CreateWorkloadPriorityClass").Errorf(ctx, "create workloadpriorityclass error:%s", err.Error())
		return err
	}

	return nil
}

// GetWorkloadPriorityClass gets a WorkloadPriorityClass by name
func GetWorkloadPriorityClass(ctx context.Context, clusterName, name string) (workloadPriorityClass *kueuev1beta1.WorkloadPriorityClass, err error) {
	// WorkloadPriorityClass is cluster-scoped, so namespace is empty
	result, err := ConstackResourceAny.GetWithClusterName(clusterName, "",
		KueueGroup, KueueVersion, WorkloadPriorityClassKind, name)
	if err != nil {
		log.L.WithName("GetWorkloadPriorityClass").Errorf(ctx, "get workloadpriorityclass error:%s", err.Error())
		return nil, err
	}

	workloadPriorityClasses := []*kueuev1beta1.WorkloadPriorityClass{}
	marshal, _ := json.Marshal(result)
	err = json.Unmarshal(marshal, &workloadPriorityClasses)
	if err != nil {
		log.L.WithName("GetWorkloadPriorityClass").Errorf(ctx, "unmarshal workloadpriorityclass error:%s", err.Error())
		return nil, err
	}
	if len(workloadPriorityClasses) > 0 {
		return workloadPriorityClasses[0], nil
	}
	return nil, fmt.Errorf("not found workloadpriorityclass:%s", name)
}

// ListWorkloadPriorityClass lists all WorkloadPriorityClasses
func ListWorkloadPriorityClass(ctx context.Context, clusterName string) (workloadPriorityClasses []kueuev1beta1.WorkloadPriorityClass, err error) {
	// WorkloadPriorityClass is cluster-scoped, so namespace is empty
	result, err := ConstackResourceAny.ListV2WithClusterName(clusterName, "",
		KueueGroup, KueueVersion, WorkloadPriorityClassKind,
		[]constack_openapi.LabelSelector{}, []constack_openapi.FieldSelector{})
	if err != nil {
		log.L.WithName("ListWorkloadPriorityClass").Errorf(ctx, "list workloadpriorityclass error:%s", err.Error())
		return nil, err
	}

	workloadPriorityClasses = make([]kueuev1beta1.WorkloadPriorityClass, 0)
	marshal, _ := json.Marshal(result)
	err = json.Unmarshal(marshal, &workloadPriorityClasses)
	if err != nil {
		log.L.WithName("ListWorkloadPriorityClass").Errorf(ctx, "unmarshal workloadpriorityclass error:%s", err.Error())
		return nil, err
	}

	return
}

// UpdateWorkloadPriorityClass updates a WorkloadPriorityClass
func UpdateWorkloadPriorityClass(ctx context.Context, clusterName string, workloadPriorityClass *kueuev1beta1.WorkloadPriorityClass) (err error) {
	m, err := convertKueueResource(workloadPriorityClass)
	if err != nil {
		log.L.WithName("UpdateWorkloadPriorityClass").Errorf(ctx, "convert workloadpriorityclass error:%s", err.Error())
		return
	}
	err = ConstackResourceAny.UpdateWithClusterName(clusterName, m)
	if err != nil {
		log.L.WithName("UpdateWorkloadPriorityClass").Errorf(ctx, "update workloadpriorityclass error:%s", err.Error())
		return err
	}

	return nil
}

// DeleteWorkloadPriorityClass deletes a WorkloadPriorityClass by name
func DeleteWorkloadPriorityClass(ctx context.Context, clusterName, name string) (err error) {
	// WorkloadPriorityClass is cluster-scoped, so namespace is empty
	_, err = ConstackResourceAny.DeleteWithClusterName(clusterName, "",
		KueueGroup, KueueVersion, WorkloadPriorityClassKind, name)
	if err != nil {
		log.L.WithName("DeleteWorkloadPriorityClass").Errorf(ctx, "delete workloadpriorityclass error:%s", err.Error())
		return err
	}

	return nil
}

// Cohort CRUD operations

// CreateCohort creates a Cohort resource
func CreateCohort(ctx context.Context, clusterName string, cohort *kueuev1alpha1.Cohort) (err error) {
	m, err := convertKueueResource(cohort)
	if err != nil {
		log.L.WithName("CreateCohort").Errorf(ctx, "convert cohort error:%s", err.Error())
		return
	}
	_, err = ConstackResourceAny.CreateWithClusterName(clusterName, m)
	if err != nil {
		log.L.WithName("CreateCohort").Errorf(ctx, "create cohort error:%s", err.Error())
		return err
	}

	return nil
}

// GetCohort gets a Cohort by name
func GetCohort(ctx context.Context, clusterName, name string) (cohort *kueuev1alpha1.Cohort, err error) {
	// Cohort is cluster-scoped, so namespace is empty
	result, err := ConstackResourceAny.GetWithClusterName(clusterName, "",
		KueueGroup, KueueAlphaVersion, CohortKind, name)
	if err != nil {
		log.L.WithName("GetCohort").Errorf(ctx, "get cohort error:%s", err.Error())
		return nil, err
	}

	cohorts := []*kueuev1alpha1.Cohort{}
	marshal, _ := json.Marshal(result)
	err = json.Unmarshal(marshal, &cohorts)
	if err != nil {
		log.L.WithName("GetCohort").Errorf(ctx, "unmarshal cohort error:%s", err.Error())
		return nil, err
	}
	if len(cohorts) > 0 {
		return cohorts[0], nil
	}
	return nil, fmt.Errorf("not found cohort:%s", name)
}

// ListCohort lists all Cohorts
func ListCohort(ctx context.Context, clusterName string) (cohorts []kueuev1alpha1.Cohort, err error) {
	// Cohort is cluster-scoped, so namespace is empty
	result, err := ConstackResourceAny.ListV2WithClusterName(clusterName, "",
		KueueGroup, KueueAlphaVersion, CohortKind,
		[]constack_openapi.LabelSelector{}, []constack_openapi.FieldSelector{})
	if err != nil {
		log.L.WithName("ListCohort").Errorf(ctx, "list cohort error:%s", err.Error())
		return nil, err
	}

	cohorts = make([]kueuev1alpha1.Cohort, 0)
	marshal, _ := json.Marshal(result)
	err = json.Unmarshal(marshal, &cohorts)
	if err != nil {
		log.L.WithName("ListCohort").Errorf(ctx, "unmarshal cohort error:%s", err.Error())
		return nil, err
	}

	return
}

// UpdateCohort updates a Cohort
func UpdateCohort(ctx context.Context, clusterName string, cohort *kueuev1alpha1.Cohort) (err error) {
	m, err := convertKueueResource(cohort)
	if err != nil {
		log.L.WithName("UpdateCohort").Errorf(ctx, "convert cohort error:%s", err.Error())
		return
	}
	err = ConstackResourceAny.UpdateWithClusterName(clusterName, m)
	if err != nil {
		log.L.WithName("UpdateCohort").Errorf(ctx, "update cohort error:%s", err.Error())
		return err
	}

	return nil
}

// DeleteCohort deletes a Cohort by name
func DeleteCohort(ctx context.Context, clusterName, name string) (err error) {
	// Cohort is cluster-scoped, so namespace is empty
	_, err = ConstackResourceAny.DeleteWithClusterName(clusterName, "",
		KueueGroup, KueueAlphaVersion, CohortKind, name)
	if err != nil {
		log.L.WithName("DeleteCohort").Errorf(ctx, "delete cohort error:%s", err.Error())
		return err
	}

	return nil
}

// Workload CRUD operations

// CreateWorkload creates a Workload resource
func CreateWorkload(ctx context.Context, clusterName string, workload *kueuev1beta1.Workload) (err error) {
	m, err := convertKueueResource(workload)
	if err != nil {
		log.L.WithName("CreateWorkload").Errorf(ctx, "convert workload error:%s", err.Error())
		return
	}
	_, err = ConstackResourceAny.CreateWithClusterName(clusterName, m)
	if err != nil {
		log.L.WithName("CreateWorkload").Errorf(ctx, "create workload error:%s", err.Error())
		return err
	}

	return nil
}

// GetWorkload gets a Workload by name and namespace
func GetWorkload(ctx context.Context, clusterName, namespace, name string) (workload *kueuev1beta1.Workload, err error) {
	result, err := ConstackResourceAny.GetWithClusterName(clusterName, namespace,
		KueueGroup, KueueVersion, WorkloadKind, name)
	if err != nil {
		log.L.WithName("GetWorkload").Errorf(ctx, "get workload error:%s", err.Error())
		return nil, err
	}

	workloads := []*kueuev1beta1.Workload{}
	marshal, _ := json.Marshal(result)
	err = json.Unmarshal(marshal, &workloads)
	if err != nil {
		log.L.WithName("GetWorkload").Errorf(ctx, "unmarshal workload error:%s", err.Error())
		return nil, err
	}
	if len(workloads) > 0 {
		return workloads[0], nil
	}
	return nil, fmt.Errorf("not found workload:%s", name)
}

// ListWorkload lists Workloads with label selectors
func ListWorkload(ctx context.Context, clusterName, namespace string, labelSelectors []constack_openapi.LabelSelector) (workloads []kueuev1beta1.Workload, err error) {
	result, err := ConstackResourceAny.ListV2WithClusterName(clusterName, namespace,
		KueueGroup, KueueVersion, WorkloadKind,
		labelSelectors, []constack_openapi.FieldSelector{})
	if err != nil {
		log.L.WithName("ListWorkload").Errorf(ctx, "list workload error:%s", err.Error())
		return nil, err
	}

	workloads = make([]kueuev1beta1.Workload, 0)
	marshal, _ := json.Marshal(result)
	err = json.Unmarshal(marshal, &workloads)
	if err != nil {
		log.L.WithName("ListWorkload").Errorf(ctx, "unmarshal workload error:%s", err.Error())
		return nil, err
	}

	return
}

// UpdateWorkload updates a Workload
func UpdateWorkload(ctx context.Context, clusterName string, workload *kueuev1beta1.Workload) (err error) {
	m, err := convertKueueResource(workload)
	if err != nil {
		log.L.WithName("UpdateWorkload").Errorf(ctx, "convert workload error:%s", err.Error())
		return
	}
	err = ConstackResourceAny.UpdateWithClusterName(clusterName, m)
	if err != nil {
		log.L.WithName("UpdateWorkload").Errorf(ctx, "update workload error:%s", err.Error())
		return err
	}

	return nil
}

// DeleteWorkload deletes a Workload by name and namespace
func DeleteWorkload(ctx context.Context, clusterName, namespace, name string) (err error) {
	_, err = ConstackResourceAny.DeleteWithClusterName(clusterName, namespace,
		KueueGroup, KueueVersion, WorkloadKind, name)
	if err != nil {
		log.L.WithName("DeleteWorkload").Errorf(ctx, "delete workload error:%s", err.Error())
		return err
	}

	return nil
}
