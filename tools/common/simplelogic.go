package common

import (
	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

type DataRes struct {
	Data interface{} `json:"data"`
}

type EmptyRes struct {
}

type ListPageRes struct {
	Total int         `json:"total"`
	Page  int         `json:"page"`
	Size  int         `json:"size"`
	List  interface{} `json:"list"`
}

type ListReq struct {
	Page       int    `json:"page" d:"1"`        // 分页号码
	Size       int    `json:"size" d:"10"`       // 分页数量
	SortType   string `json:"sortType" d:"desc"` // asc | desc
	SortField  string `json:"sortField" d:"id"`  // 排序字段
	SearchType string `json:"searchType" d:"or"` // or | and
	FilterType string `json:"filterType" d:"or"` // or | and
}

type ListRes struct {
	List  gdb.Result `json:"list" description:"列表"`
	Page  int        `json:"page" description:"分页码"`
	Size  int        `json:"size" description:"分页数量"`
	Total int        `json:"total" description:"数据总数"`
}

type FilterListReq struct {
	g.Meta `path:"/simple-logic/filter-list" tags:"SimpleLogic" method:"get" summary:"simple login filter list"`
	Field  string `json:"field" description:"filter field"`
	Table  string `json:"table" description:"filter table"`
}

type FilterListRes struct {
	List gdb.Result `json:"list" description:"列表"`
}
