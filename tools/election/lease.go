package election

import (
	"context"
	"os"
	"time"

	"gitlab.ttyuyin.com/yunwei-infra/tt-cloud-constack-sdk/pkg/log"
	"k8s.io/client-go/kubernetes"
	"k8s.io/client-go/rest"
	"k8s.io/client-go/tools/leaderelection"
	"k8s.io/client-go/tools/leaderelection/resourcelock"

	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
)

type LeaseElection struct {
	LeaderElector *leaderelection.LeaderElector
	ctx           context.Context
}

// 申明实现 Election 接口
var _ Election = (*LeaseElection)(nil)

func NewLeaseElection(ctx context.Context) *LeaseElection {

	// 创建Kubernetes客户端
	config, err := rest.InClusterConfig()
	if err != nil {
		log.L.WithName("Election.Run").Errorf(ctx, "in cluster config error:%s", err.<PERSON>rror())
	}
	clientset, err := kubernetes.NewForConfig(config)
	if err != nil {
		log.L.With<PERSON>ame("Election.Run").Errorf(ctx, "new for config error:%s", err.Error())
	}

	// 配置Lease锁
	leaseLock := &resourcelock.LeaseLock{
		LeaseMeta: metav1.ObjectMeta{
			Name:      "mlops-lease",
			Namespace: "infra",
		},
		Client: clientset.CoordinationV1(),
		LockConfig: resourcelock.ResourceLockConfig{
			Identity: os.Getenv("POD_NAME"), // 使用Pod名称作为唯一标识
		},
	}

	// 配置选举参数
	leaderElectionConfig := leaderelection.LeaderElectionConfig{
		Lock:          leaseLock,
		LeaseDuration: 15 * time.Second, // 租约持续时间
		RenewDeadline: 10 * time.Second, // Leader续约超时时间
		RetryPeriod:   2 * time.Second,  // 重试间隔
		Callbacks: leaderelection.LeaderCallbacks{
			OnStartedLeading: func(ctx context.Context) {
				// 成为Leader后执行的逻辑
				log.L.WithName("Election.Run").Info(ctx, "Started leading")
			},
			OnStoppedLeading: func() {
				// 失去Leader身份后执行的逻辑
				log.L.WithName("Election.Run").Info(ctx, "Stopped leading")
			},
			OnNewLeader: func(identity string) {
				// 当新Leader产生时的回调
				if identity == os.Getenv("POD_NAME") {
					return
				}
				log.L.WithName("Election.Run").Infof(ctx, "New leader elected: %s", identity)
			},
		},
	}

	// 创建选举器并开始选举
	leaderElector, err := leaderelection.NewLeaderElector(leaderElectionConfig)
	if err != nil {
		log.L.WithName("Election.Run").Errorf(ctx, "new leader elector error:%s", err.Error())
	}

	return &LeaseElection{LeaderElector: leaderElector, ctx: ctx}
}

func (e *LeaseElection) Start() {
	e.LeaderElector.Run(e.ctx)
}

func (e *LeaseElection) Stop() {
}

func (e *LeaseElection) IsMaster() bool {
	return e.LeaderElector.IsLeader()
}
