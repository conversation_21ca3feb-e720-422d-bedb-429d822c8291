package election

import (
	"context"
	"os"
	"os/signal"
	"sync"
	"syscall"
	"time"

	"github.com/gogf/gf/v2/database/gredis"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/util/grand"

	_ "github.com/gogf/gf/contrib/nosql/redis/v2"
	"gitlab.ttyuyin.com/yunwei-infra/tt-cloud-constack-sdk/pkg/log"
)

const (
	lockKey          = "mlops:master:lock" // 分布式锁的键
	lockExpiration   = 5 * time.Second     // 锁的过期时间
	electionInterval = 3 * time.Second     // 选举尝试间隔
	renewalInterval  = 2 * time.Second     // 续租间隔
)

type RedisElection struct {
	ID          string        // 节点唯一标识
	redis       *gredis.Redis // GoFrame Redis客户端
	isMaster    bool          // 当前是否是主节点
	stopChan    chan struct{} // 停止信号
	renewTicker *time.Ticker  // 续租定时器
	mu          sync.Mutex    // 保护状态的锁
	ctx         context.Context
}

// 申明实现 Election 接口
var _ Election = (*RedisElection)(nil)

func NewRedisElection(ctx context.Context) *RedisElection {
	return &RedisElection{
		ID:       grand.S(8, false),
		redis:    g.Redis(),
		stopChan: make(chan struct{}),
		ctx:      ctx,
	}
}

func (n *RedisElection) Start() {
	log.L.WithName("tryElect").Infof(n.ctx, "节点 %s 启动", n.ID)
	// 启动选举协程
	go n.runElectionLoop()

	// 处理停止信号
	go n.handleSignals()
}

func (n *RedisElection) runElectionLoop() {
	ticker := time.NewTicker(electionInterval)
	defer ticker.Stop()

	for {
		select {
		case <-n.stopChan:
			n.releaseMaster()
			return
		case <-ticker.C:
			n.tryElect()
		}
	}
}

func (n *RedisElection) tryElect() {
	// 尝试获取分布式锁 (SETNX)
	result, err := n.redis.Do(n.ctx, "SET", lockKey, n.ID, "NX", "EX", lockExpiration.Seconds())
	if err != nil {
		log.L.WithName("tryElect").Warningf(n.ctx, "选举失败: %v", err)
		return
	}

	if result.String() == "OK" {
		n.acquireMaster()
	} else {
		// 检查当前持有者是否是自己（续租）
		currentHolder, err := n.redis.Do(n.ctx, "GET", lockKey)
		if err != nil {
			log.L.WithName("tryElect").Warningf(n.ctx, "检查锁失败: %v", err)
			return
		}

		if currentHolder.String() == n.ID {
			n.redis.Do(n.ctx, "EXPIRE", lockKey, lockExpiration.Seconds())
		}
	}
}

func (n *RedisElection) acquireMaster() {
	n.mu.Lock()
	defer n.mu.Unlock()

	if n.isMaster {
		return
	}

	log.L.WithName("tryElect").Infof(n.ctx, "✅ 节点 %s 成为主节点", n.ID)
	n.isMaster = true

	// 启动续租协程
	n.renewTicker = time.NewTicker(renewalInterval)
	go n.renewLease()
}

func (n *RedisElection) renewLease() {
	for {
		select {
		case <-n.stopChan:
			return
		case <-n.renewTicker.C:
			// 使用Lua脚本保证原子性续租操作
			script := `
			if redis.call("GET", KEYS[1]) == ARGV[1] then
				return redis.call("EXPIRE", KEYS[1], ARGV[2])
			else
				return 0
			end`

			expireSec := int(lockExpiration / time.Second)
			res, err := n.redis.Do(n.ctx, "EVAL", script, 1, lockKey, n.ID, expireSec)
			if err != nil || res.Int() == 0 {
				log.L.WithName("tryElect").Warningf(n.ctx, "⚠️ 续租失败，主节点状态丢失")
				n.loseMaster()
				return
			}
		}
	}
}

func (n *RedisElection) loseMaster() {
	n.mu.Lock()
	defer n.mu.Unlock()

	if !n.isMaster {
		return
	}

	log.L.WithName("tryElect").Warningf(n.ctx, "❌ 节点 %s 失去主节点状态", n.ID)
	n.isMaster = false
	if n.renewTicker != nil {
		n.renewTicker.Stop()
	}
}

func (n *RedisElection) releaseMaster() {
	n.mu.Lock()
	defer n.mu.Unlock()

	if n.isMaster {
		// 只删除自己的锁
		script := `
		if redis.call("GET", KEYS[1]) == ARGV[1] then
			return redis.call("DEL", KEYS[1])
		else
			return 0
		end`
		n.redis.Do(n.ctx, "EVAL", script, 1, lockKey, n.ID)

		log.L.WithName("tryElect").Infof(n.ctx, "🗑️ 节点 %s 主动释放主节点锁", n.ID)
		n.isMaster = false
		if n.renewTicker != nil {
			n.renewTicker.Stop()
		}
	}
}

func (n *RedisElection) IsMaster() bool {
	n.mu.Lock()
	defer n.mu.Unlock()
	return n.isMaster
}

func (n *RedisElection) Stop() {
	close(n.stopChan)
	log.L.WithName("tryElect").Infof(n.ctx, "节点 %s 已停止", n.ID)
}

func (n *RedisElection) handleSignals() {
	sigCh := make(chan os.Signal, 1)
	signal.Notify(sigCh, syscall.SIGINT, syscall.SIGTERM)
	<-sigCh
	n.Stop()
}
