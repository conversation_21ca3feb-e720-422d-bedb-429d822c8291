package tools

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"github.com/gogf/gf/v2/container/gvar"
	"github.com/gogf/gf/v2/frame/g"
)

// 对参数进行默认值返回
func DefaultKeyWordParams[D any](defVal D, params ...D) D {
	if len(params) == 0 {
		return defVal
	}
	return params[0]
}

func ConfigGet(pattern string, def ...interface{}) *gvar.Var {
	return g.Cfg().MustGet(context.TODO(), pattern, def...)
}

func TimestampToTime(ts int) time.Time {
	return time.Unix(int64(ts), 0)
}

func TimeAddSub(t time.Time, addOrSub string) (time.Time, error) {
	dur, err := time.ParseDuration(addOrSub)
	if err != nil {
		return t, err
	}

	return t.Add(dur), nil
}

// CoverStrikeThrough2Point 由于某些无法避免的情况需把中划线转成点
func CoverStrikeThrough2Point(value string) string {
	if strings.Contains(value, "-") {
		return strings.Replace(value, "-", ".", -1)
	}
	return value
}

// CovertSlice2SQLFormat 转换数组元素 ["a", "b"] -> 'a','b'
func CovertSlice2SQLFormat(slice []string) string {
	var res string = ""
	for _, s := range slice {
		if res == "" {
			res = fmt.Sprintf("'%s'", s)
			continue
		}
		res = res + "," + fmt.Sprintf("'%s'", s)
	}
	return res
}

// RedisSetCache 方便设置redis缓存, 会为cacheVal 进行json化 慎用
func RedisSetCache(ctx context.Context, cacheKey string, cacheVal interface{}, expireSecs int) error {

	_, err := g.Redis().Do(ctx, "SET", cacheKey, cacheVal, "EX", expireSecs)
	if err != nil {
		return err
	}
	return nil
}

// RedisSetCacheWithoutEx 和 RedisSetCache 差不多只是不带过期时间
func RedisSetCacheWithoutEx(ctx context.Context, cacheKey string, cacheVal interface{}) error {
	_, err := g.Redis().Do(ctx, "SET", cacheKey, cacheVal)
	if err != nil {
		return err
	}
	return nil
}

// RedisGetCache 帮助判断是否为空的情况，若给出result结构体则直接帮助转换；
func RedisGetCache(ctx context.Context, cacheKey string, result ...interface{}) (*gvar.Var, error) {
	val, err := g.Redis().Do(ctx, "GET", cacheKey)
	if err != nil {
		return nil, err
	}

	if val.IsNil() {
		return nil, fmt.Errorf("%s is nil.", cacheKey)
	}

	if len(result) != 0 {
		if err := json.Unmarshal(val.Bytes(), result[0]); err != nil {
			return nil, err
		}
	}
	return val, nil
}
