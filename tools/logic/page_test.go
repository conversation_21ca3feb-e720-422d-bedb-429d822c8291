package logic

import (
	"fmt"
	"testing"
)

func Test_Filter(t *testing.T) {
	page := NewPager[int]([]int{1, 2, 3, 4, 5, 6, 7, 8, 9, 10})
	list := page.Offset(1).Limit(3).Filter(func(item interface{}) KeepItem {
		if *item.(*int)%2 == 1 {
			return true
		}
		return false
	}).List()
	fmt.Println(list)
}

func Test_Main(t *testing.T) {
	//page := NewPager[string]([]string{"1", "2", "3", "4", "5", "6", "7", "8", "9", "10"})
	//list := page.Offset(1).Limit(4).ListMultiClusterSearch()
	//if slices.Contains(list, "9") && slices.Contains(list, "10") {
	//	t.Log(list)
	//} else {
	//	t.Error("unexpect list")
	//}
	//t.Logf("%+v \n", page.Offset(3).Limit(4).Output())

	pageInt := NewPager[int]([]int{1, 2, 3, 4, 5, 6, 7, 8, 9, 10})
	t.Log(pageInt.Offset(2).Limit(4).List())
}
