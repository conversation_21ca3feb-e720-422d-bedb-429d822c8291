package logic

import (
	"context"
	"fmt"
	"github.com/gogf/gf/v2/text/gstr"
	"gitlab.ttyuyin.com/yunwei-infra/tt-cloud-constack-sdk/pkg/log"
	"gitlab.ttyuyin.com/yunwei-infra/tt-cloud-constack-sdk/pkg/utils"
	"mlops/internal/consts"
	"mlops/tools"
	apiv1 "mlops/tools/common"
	"strconv"
	"strings"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// s_ 开头为search 字段，各字段关系为or
func ModelSearch(ctx context.Context, model *gdb.Model, orAnd ...string) *gdb.Model {
	const (
		sPrefix = "s_"
	)

	rel := utils.DefaultKeyWordParams[string](consts.WhereOr, orAnd...)
	r := g.RequestFromCtx(ctx)
	qm := r.GetQueryMap()
	log.L.WithName("ModelSearch").Info(ctx, "ListSearch query map: ", fmt.Sprintf("%+v", qm))

	for k, v := range qm {
		if strings.HasPrefix(k, sPrefix) {
			pureK := strings.TrimLeft(k, sPrefix)
			if rel == consts.WhereOr {
				model = model.WhereOrLike(pureK, fmt.Sprintf("%%%v%%", v))
			} else {
				model = model.WhereLike(pureK, fmt.Sprintf("%%%v%%", v))
			}
		}
	}
	return model
}

// f_ 开头为 filter 字段，各字段关系为and
func ModalFilter(ctx context.Context, model *gdb.Model, orAnd ...string) *gdb.Model {
	const (
		sPrefix = "f_"
	)

	rel := tools.DefaultKeyWordParams[string](consts.WhereOr, orAnd...)
	r := g.RequestFromCtx(ctx)
	qm := r.GetQueryMap()
	log.L.WithName("ModalFilter").Info(ctx, "ListFilter query map: ", fmt.Sprintf("%+v", qm))

	isFilter := false
	filterModel := g.DB().Model(
		"? as sm",
		model.Fields("*"),
	)

	for k, v := range qm {
		if strings.HasPrefix(k, sPrefix) {
			isFilter = true
			pureK := strings.TrimLeft(k, sPrefix)
			if rel == consts.WhereOr {
				filterModel.WhereOr(fmt.Sprintf("`%s`=\"%s\"", pureK, v))
			} else {
				filterModel.Where(fmt.Sprintf("`%s`=\"%s\"", pureK, v))
			}
		}
	}

	if isFilter {
		return filterModel
	}

	return filterModel
}

func ModelDatetimeRange(ctx context.Context, model *gdb.Model) *gdb.Model {
	const (
		datetimePrefix = "d_"
	)
	r := g.RequestFromCtx(ctx)
	qm := r.GetQueryMap()
	log.L.WithName("ModelDatetimeRange").Info(ctx, "ListDatetime query map: ", fmt.Sprintf("%+v", qm))
	for k, v := range qm {
		if strings.HasPrefix(k, datetimePrefix) {
			pureK := strings.TrimLeft(k, datetimePrefix)
			splitTime := strings.Split(fmt.Sprintf("%s", v), ",")
			start, _ := strconv.Atoi(splitTime[0])
			end, _ := strconv.Atoi(splitTime[1])
			return model.Where(fmt.Sprintf("%s >= '%s'", pureK,
				tools.TimestampToTime(start).Format(consts.DateTimeFormat))).
				Where(fmt.Sprintf("%s <= '%s'", pureK,
					tools.TimestampToTime(end).Format(consts.DateTimeFormat)))
		}
	}
	return model

}

func ModelPage(model *gdb.Model, page, size int) (model2 *gdb.Model, total int, err error) {
	model = model.Page(page, size)
	total, err = model.Count()
	model2 = model
	return
}

func ModelSort(model *gdb.Model, sortType, sortField string) *gdb.Model {
	if sortType == consts.OrderAsc {
		return model.OrderAsc(sortField)
	}
	return model.OrderDesc(sortField)
}

type ListSvc interface {
	List(ctx context.Context, req apiv1.ListReq) (res *apiv1.ListRes, err error)
	FilterList(ctx context.Context, req *apiv1.FilterListReq) (res *apiv1.FilterListRes, err error)
}

type SimpleList struct {
	Table string // table name
}

func NewSimpleList(table string) *SimpleList {
	return &SimpleList{Table: table}
}

func (this *SimpleList) FilterList(ctx context.Context, req *apiv1.FilterListReq) (res *apiv1.FilterListRes, err error) {
	table := this.Table
	if req.Table != "" {
		table = req.Table
	}
	model := g.DB().Model(table).Safe().Ctx(ctx)
	rs, err := model.Fields(fmt.Sprintf("DISTINCT %s", req.Field)).All()
	return &apiv1.FilterListRes{List: rs}, err
}

func (this *SimpleList) List(ctx context.Context, req apiv1.ListReq, excludeFields ...string) (res *apiv1.ListRes, err error) {

	subModel := g.DB().Model(this.Table).Safe().Ctx(ctx)
	// exclude fields
	for _, f := range excludeFields {
		subModel = subModel.FieldsEx(f)
	}

	// datetime range
	subModel = ModelDatetimeRange(ctx, subModel)
	model := g.Model("? as d", subModel)

	// search
	model = ModelSearch(ctx, model, req.SearchType)
	// filter
	model = ModalFilter(ctx, model, req.FilterType)

	// sort
	model = ModelSort(model, req.SortType, req.SortField)

	// page
	model, total, err := ModelPage(model, req.Page, req.Size)
	if err != nil {
		return nil, err
	}

	r, err := model.All()

	for i, record := range r {
		newRecord := make(gdb.Record)
		for k, v := range record {
			camelKey := gstr.CaseCamelLower(k)
			newRecord[camelKey] = v
		}
		r[i] = newRecord
	}

	return &apiv1.ListRes{
		List:  r,
		Page:  req.Page,
		Size:  req.Size,
		Total: total,
	}, err

}

type GetSvc interface {
	Get(ctx context.Context, priId int, entity interface{}) (res *apiv1.DataRes, err error)
}

type SimpleGet struct {
	Table string // table name
}

func NewSimpleGet(table string) *SimpleGet {
	return &SimpleGet{Table: table}
}

func (this *SimpleGet) Get(ctx context.Context, priId int, entity interface{}) (res *apiv1.DataRes, err error) {
	err = g.DB().Model(this.Table).Safe().Ctx(ctx).WherePri(priId).Scan(entity)
	return &apiv1.DataRes{entity}, err
}

type CreateSvc interface {
	Create(ctx context.Context, input interface{}) (*apiv1.EmptyRes, error)
}

type SimpleCreate struct {
	Table string // table name
}

func NewSimpleCreate(table string) *SimpleCreate {
	return &SimpleCreate{Table: table}
}

func (this *SimpleCreate) Create(ctx context.Context, input interface{}) (res *apiv1.EmptyRes, err error) {
	_, err = g.DB().Model(this.Table).Safe().Ctx(ctx).Data(input).Save()
	return nil, err
}

type UpdateSvc interface {
	Update(ctx context.Context, priId int, input interface{}) (*apiv1.EmptyRes, error)
}

type SimpleUpdate struct {
	Table string // table name
}

func NewSimpleUpdate(table string) *SimpleUpdate {
	return &SimpleUpdate{Table: table}
}

func (this *SimpleUpdate) Update(ctx context.Context, priId int, input interface{}) (res *apiv1.EmptyRes, err error) {
	_, err = g.DB().Model(this.Table).Safe().Ctx(ctx).WherePri(priId).Data(input).Update()
	return nil, err
}

type DeleteSvc interface {
	Delete(ctx context.Context, priId int) (*apiv1.EmptyRes, error)
}

type SimpleDelete struct {
	Table string // table name
}

func NewSimpleDelete(table string) *SimpleDelete {
	return &SimpleDelete{Table: table}
}

func (this *SimpleDelete) Delete(ctx context.Context, priId int) (res *apiv1.EmptyRes, err error) {
	_, err = g.DB().Model(this.Table).Safe().Ctx(ctx).WherePri(priId).Delete()
	return nil, err
}
