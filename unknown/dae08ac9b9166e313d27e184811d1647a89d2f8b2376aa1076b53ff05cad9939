package v1

import (
	"github.com/gogf/gf/v2/frame/g"
	"mlops/internal/model/dto"
)

type ListClusterNamespaceReq struct {
	g.Meta `path:"/team/cluster-namespace/list" method:"get" tags:"Team" sm:"list cluster namespace"`
	TeamId int `json:"teamId" v:"required#teamId is required"`
}

type ListClusterNamespaceRes struct {
	ClusterNamespaces []*dto.ClusterNamespace `json:"clusterNamespaces"`
}

type ListTeamReq struct {
	g.Meta `path:"/team/list" method:"get" tags:"Team" sm:"list team"`
}

type ListTeamRes struct {
	Teams []*dto.Team `json:"teams"`
}

type ListTeamUserReq struct {
	g.Meta `path:"/team/user/list/:pid" tags:"Team" method:"get" summary:"team's user list'"`
}

type ListTeamUserRes struct {
	Users []*dto.UserTeamRole `json:"users"`
}

type CreateClusterNamespaceReq struct {
	g.Meta            `path:"/team/cluster-namespace/create" method:"post" tags:"Team" sm:"create cluster namespace"`
	TeamId            int                  `json:"teamId" v:"required#teamId is required"`
	ClusterNamespaces dto.ClusterNamespace `json:"clusterNamespaces"`
}

type CreateClusterNamespaceRes struct{}

type DeleteClusterNamespaceReq struct {
	g.Meta    `path:"/team/cluster-namespace/delete/:teamId" method:"delete" tags:"Team" sm:"delete cluster namespace"`
	Cluster   string
	Namespace string
}

type DeleteClusterNamespaceRes struct{}

type ListGpuQuotaReq struct {
	g.Meta `path:"/team/gpu-quota/list" method:"get" tags:"Team" sm:"list gpu quota"`
	TeamId int `json:"teamId" v:"required#teamId is required"`
}

type ListGpuQuotaRes struct {
	GpuQuotas []*dto.GpuQuota `json:"gpuQuota"`
}

type CreateGpuQuotaReq struct {
	g.Meta   `path:"/team/gpu-quota/create" method:"post" tags:"Team" sm:"create gpu quota"`
	TeamId   int          `json:"teamId" v:"required#teamId is required"`
	GpuQuota dto.GpuQuota `json:"gpuQuota"`
}

type CreateGpuQuotaRes struct{}

type UpdateGpuQuotaReq struct {
	g.Meta   `path:"/team/gpu-quota/update" method:"patch" tags:"Team" sm:"update gpu quota"`
	TeamId   int          `json:"teamId" v:"required#teamId is required"`
	GpuQuota dto.GpuQuota `json:"gpuQuota"`
}

type UpdateGpuQuotaRes struct{}

type ListTeamAppReq struct {
	g.Meta `path:"/team/app/list" method:"get" tags:"Team" sm:"list team app"`
	TeamId int `json:"teamId" v:"required#teamId is required"`
	Page   int `json:"page" d:"1" v:"min:1#page must be at least 1"`
	Size   int `json:"size" d:"10" v:"min:1#size must be at least 1"`
}

type ListTeamAppRes struct {
	Apps []*dto.App `json:"apps"`
}

type DeleteGpuQuotaReq struct {
	g.Meta  `path:"/team/gpu-quota/delete/:teamId" method:"delete" tags:"Team" sm:"delete gpu quota"`
	GpuType string
}

type DeleteGpuQuotaRes struct{}
