package main

import (
	"encoding/json"
	"fmt"
)

type ClusterNamespace struct {
	Cluster    string   `json:"cluster"`
	Namespaces []string `json:"namespaces"`
}

func main() {
	cns := make([]*ClusterNamespace, 0)
	data := `[
 {
 "cluster":"k8s-tc-bj-1-test",
 "namespaces":[
 "infra"
 ]
 }
 ]`

	err := json.Unmarshal([]byte(data), &cns)
	if err != nil {
		panic(err)
	}

	for _, cn := range cns {
		fmt.Println("cluster:", cn.Cluster, "namespaces:", cn.Namespaces)
	}
}
