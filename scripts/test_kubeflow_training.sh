#!/bin/bash

# Kubeflow Training Operator 测试脚本
# 用于测试TFJob、PyTorchJob、MPIJob的CRUD操作和端到端流程

set -e

echo "=========================================="
echo "Kubeflow Training Operator 测试开始"
echo "=========================================="

# 设置测试环境变量
export ENV=test
export GO_ENV=test

# 进入项目根目录
cd "$(dirname "$0")/.."

echo "1. 检查依赖..."
go mod tidy
go mod download

echo "2. 编译项目..."
go build ./...

echo "3. 运行单元测试..."
echo "3.1 测试client层CRUD操作..."
go test -v ./internal/logic/traintask -run TestTFJobCRUD -timeout 10m
go test -v ./internal/logic/traintask -run TestPyTorchJobCRUD -timeout 10m
go test -v ./internal/logic/traintask -run TestMPIJobCRUD -timeout 10m

echo "3.2 测试logic层集成功能..."
go test -v ./internal/logic/traintask -run TestKubeflowTrainingIntegration -timeout 5m

echo "4. 运行端到端测试..."
go test -v ./internal/logic/traintask -run TestKubeflowE2E -timeout 15m

echo "5. 运行所有Kubeflow相关测试..."
go test -v ./internal/logic/traintask -run ".*Kubeflow.*" -timeout 20m

echo "=========================================="
echo "测试完成！"
echo "=========================================="

# 生成测试报告
echo "6. 生成测试报告..."
go test -v ./internal/logic/traintask -run ".*Kubeflow.*" -coverprofile=coverage.out -timeout 20m
go tool cover -html=coverage.out -o coverage.html

echo "测试覆盖率报告已生成: coverage.html"

echo "=========================================="
echo "所有测试执行完毕！"
echo "=========================================="
