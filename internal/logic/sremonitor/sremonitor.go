package sremonitor

import (
	"context"
	"fmt"

	"gitlab.ttyuyin.com/yunwei-infra/tt-cloud-constack-sdk/pkg/log"
	"mlops/internal/model/dto"
	"mlops/internal/service"
	"mlops/tools/client"
)

type sSreMonitor struct{}

func init() {
	service.RegisterSreMonitor(newSreMonitor())
}

func newSreMonitor() *sSreMonitor {
	return &sSreMonitor{}
}

// QueryGet 使用GET方法查询Prometheus指标
func (s *sSreMonitor) QueryGet(ctx context.Context, query *dto.SreMonitorQuery) (*dto.SreMonitorResponse, error) {
	// 构建请求参数
	req := &client.QueryRequest{
		Query:   query.Query,
		Time:    query.Time,
		Timeout: query.Timeout,
	}

	// 调用client
	response, err := client.SreMonitor.QueryGet(ctx, req)
	if err != nil {
		log.L.WithName("sSreMonitor.QueryGet").Errorf(ctx, "查询失败: %s", err.Error())
		return nil, fmt.Errorf("查询失败: %s", err.Error())
	}

	// 转换响应
	result, err := s.convertResponse(response)
	if err != nil {
		log.L.WithName("sSreMonitor.QueryGet").Errorf(ctx, "转换响应失败: %s", err.Error())
		return nil, fmt.Errorf("转换响应失败: %s", err.Error())
	}

	return result, nil
}

// QueryPost 使用POST方法查询Prometheus指标
func (s *sSreMonitor) QueryPost(ctx context.Context, query *dto.SreMonitorQuery) (*dto.SreMonitorResponse, error) {
	// 构建请求参数
	req := &client.QueryRequest{
		Query:   query.Query,
		Time:    query.Time,
		Timeout: query.Timeout,
	}

	// 调用client
	response, err := client.SreMonitor.QueryPost(ctx, req)
	if err != nil {
		log.L.WithName("sSreMonitor.QueryPost").Errorf(ctx, "查询失败: %s", err.Error())
		return nil, fmt.Errorf("查询失败: %s", err.Error())
	}

	// 转换响应
	result, err := s.convertResponse(response)
	if err != nil {
		log.L.WithName("sSreMonitor.QueryPost").Errorf(ctx, "转换响应失败: %s", err.Error())
		return nil, fmt.Errorf("转换响应失败: %s", err.Error())
	}

	return result, nil
}

// QueryRangeGet 使用GET方法进行范围查询
func (s *sSreMonitor) QueryRangeGet(ctx context.Context, query *dto.SreMonitorRangeQuery) (*dto.SreMonitorResponse, error) {
	// 构建请求参数
	req := &client.RangeQueryRequest{
		Query:   query.Query,
		Start:   query.Start,
		End:     query.End,
		Step:    query.Step,
		Timeout: query.Timeout,
	}

	// 调用client
	response, err := client.SreMonitor.QueryRangeGet(ctx, req)
	if err != nil {
		log.L.WithName("sSreMonitor.QueryRangeGet").Errorf(ctx, "范围查询失败: %s", err.Error())
		return nil, fmt.Errorf("范围查询失败: %s", err.Error())
	}

	// 转换响应
	result, err := s.convertRangeResponse(response)
	if err != nil {
		log.L.WithName("sSreMonitor.QueryRangeGet").Errorf(ctx, "转换响应失败: %s", err.Error())
		return nil, fmt.Errorf("转换响应失败: %s", err.Error())
	}

	return result, nil
}

// QueryRangePost 使用POST方法进行范围查询
func (s *sSreMonitor) QueryRangePost(ctx context.Context, query *dto.SreMonitorRangeQuery) (*dto.SreMonitorResponse, error) {
	// 构建请求参数
	req := &client.RangeQueryRequest{
		Query:   query.Query,
		Start:   query.Start,
		End:     query.End,
		Step:    query.Step,
		Timeout: query.Timeout,
	}

	// 调用client
	response, err := client.SreMonitor.QueryRangePost(ctx, req)
	if err != nil {
		log.L.WithName("sSreMonitor.QueryRangePost").Errorf(ctx, "范围查询失败: %s", err.Error())
		return nil, fmt.Errorf("范围查询失败: %s", err.Error())
	}

	// 转换响应
	result, err := s.convertRangeResponse(response)
	if err != nil {
		log.L.WithName("sSreMonitor.QueryRangePost").Errorf(ctx, "转换响应失败: %s", err.Error())
		return nil, fmt.Errorf("转换响应失败: %s", err.Error())
	}

	return result, nil
}

// convertResponse 转换响应格式
func (s *sSreMonitor) convertResponse(response *client.PrometheusResponse) (*dto.SreMonitorResponse, error) {
	result := &dto.SreMonitorResponse{
		Status:    response.Status,
		ErrorType: response.ErrorType,
		Error:     response.Error,
		Warnings:  response.Warnings,
	}

	// 如果有数据，转换数据格式
	if response.Data != nil {
		queryData, err := client.SreMonitor.ParseQueryData(response)
		if err != nil {
			return result, fmt.Errorf("解析查询数据失败: %s", err.Error())
		}

		// 转换为DTO格式
		dtoData := &dto.SreMonitorQueryData{
			ResultType: queryData.ResultType,
			Result:     make([]*dto.SreMonitorQueryResult, 0, len(queryData.Result)),
		}

		for _, item := range queryData.Result {
			dtoResult := &dto.SreMonitorQueryResult{
				Metric: item.Metric,
				Value:  item.Value,
			}
			dtoData.Result = append(dtoData.Result, dtoResult)
		}

		result.Data = dtoData
	}

	return result, nil
}

// convertRangeResponse 转换范围查询响应格式
func (s *sSreMonitor) convertRangeResponse(response *client.PrometheusResponse) (*dto.SreMonitorResponse, error) {
	result := &dto.SreMonitorResponse{
		Status:    response.Status,
		ErrorType: response.ErrorType,
		Error:     response.Error,
		Warnings:  response.Warnings,
	}

	// 如果有数据，转换数据格式
	if response.Data != nil {
		rangeQueryData, err := client.SreMonitor.ParseRangeQueryData(response)
		if err != nil {
			return result, fmt.Errorf("解析范围查询数据失败: %s", err.Error())
		}

		// 转换为DTO格式
		dtoData := &dto.SreMonitorRangeQueryData{
			ResultType: rangeQueryData.ResultType,
			Result:     make([]*dto.SreMonitorRangeQueryResult, 0, len(rangeQueryData.Result)),
		}

		for _, item := range rangeQueryData.Result {
			dtoResult := &dto.SreMonitorRangeQueryResult{
				Metric: item.Metric,
				Values: item.Values,
			}
			dtoData.Result = append(dtoData.Result, dtoResult)
		}

		// 直接将范围查询数据赋值给Data字段
		result.Data = dtoData
	}

	return result, nil
}

// ParseMetrics 解析指标数据为更友好的格式
func (s *sSreMonitor) ParseMetrics(data *dto.SreMonitorQueryData) ([]*dto.SreMonitorMetric, error) {
	if data == nil || len(data.Result) == 0 {
		return []*dto.SreMonitorMetric{}, nil
	}

	metrics := make([]*dto.SreMonitorMetric, 0, len(data.Result))

	for _, result := range data.Result {
		if len(result.Value) != 2 {
			continue
		}

		// 解析时间戳
		timestamp, ok := result.Value[0].(float64)
		if !ok {
			continue
		}

		// 解析值
		valueStr, ok := result.Value[1].(string)
		if !ok {
			continue
		}

		// 获取指标名称
		metricName := result.Metric["__name__"]
		if metricName == "" {
			metricName = "unknown"
		}

		// 复制labels，排除__name__
		labels := make(map[string]string)
		for k, v := range result.Metric {
			if k != "__name__" {
				labels[k] = v
			}
		}

		metric := &dto.SreMonitorMetric{
			Name:      metricName,
			Labels:    labels,
			Value:     valueStr,
			Timestamp: int64(timestamp),
		}

		metrics = append(metrics, metric)
	}

	return metrics, nil
}
