package traintaskexecution

import (
	"context"
	"fmt"
	"mlops/internal/consts"
	"mlops/internal/dao"
	"mlops/internal/model/dto"
	"mlops/internal/model/entity"
	"mlops/internal/service"
	"mlops/tools/client"
	"mlops/utility/kueue"
	"strings"

	"github.com/gogf/gf/frame/g"
	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/os/gtime"
	mpiv2beta1 "github.com/kubeflow/mpi-operator/pkg/apis/kubeflow/v2beta1"
	trainingv1 "github.com/kubeflow/training-operator/pkg/apis/kubeflow.org/v1"
	"gitlab.ttyuyin.com/yunwei-infra/tt-cloud-constack-sdk/pkg/log"
	batchv1 "k8s.io/api/batch/v1"
	v1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
)

type sTrainTaskExecution struct {
}

func init() {
	service.RegisterTrainTaskExecution(newsTrainTaskExecution())
}

func newsTrainTaskExecution() *sTrainTaskExecution {
	return &sTrainTaskExecution{}
}

func (this *sTrainTaskExecution) Table() string {
	return dao.TrainTaskExecution.Table()
}

func (this *sTrainTaskExecution) ListPage(ctx context.Context, in dto.TrainTaskExecutionListInput) (pageData *dto.TrainTaskExecutionListOutput, err error) {
	list := make([]*dto.TrainTaskExecution, 0)
	q := dao.TrainTaskExecution.Ctx(ctx).Unscoped()
	if in.TaskId != 0 {
		q = q.Where(dao.TrainTaskExecution.Columns().TaskId, in.TaskId)
	}
	if in.ExecutionId != 0 {
		q = q.Where(dao.TrainTaskExecution.Columns().Id, in.ExecutionId)
	}
	if len(in.Statuses) > 0 {
		q = q.WhereIn(dao.TrainTaskExecution.Columns().Status, in.Statuses)
	}
	if in.TeamId != 0 {
		q = q.Where(dao.TrainTaskExecution.Columns().TeamId, in.TeamId)
	}
	if in.TriggeredByUserName != "" {
		q = q.Where(dao.TrainTaskExecution.Columns().TriggeredByUserName, in.TriggeredByUserName)
	}
	if in.TriggeredByEmployeeNo != "" {
		q = q.Where(dao.TrainTaskExecution.Columns().TriggeredByEmployeeNo, in.TriggeredByEmployeeNo)
	}
	if len(in.TriggerSources) > 0 {
		q = q.WhereIn(dao.TrainTaskExecution.Columns().TriggerSource, in.TriggerSources)
	}
	if len(in.TriggerTime) > 0 {
		q = q.WhereBetween(dao.TrainTaskExecution.Columns().TriggerTime, in.TriggerTime[0], in.TriggerTime[1])
	}
	if len(in.StartTime) > 0 {
		q = q.WhereBetween(dao.TrainTaskExecution.Columns().StartTime, in.StartTime[0], in.StartTime[1])
	}
	if len(in.EndTime) > 0 {
		q = q.WhereBetween(dao.TrainTaskExecution.Columns().EndTime, in.EndTime[0], in.EndTime[1])
	}
	q = q.LeftJoin(dao.TrainTask.Table(), "tt_train_task_execution.task_id = tt_train_task.id")
	if in.TaskName != "" {
		q = q.WhereLike(dao.TrainTask.Columns().TaskName, "%"+in.TaskName+"%")
	}
	total, err := q.Count()
	if err != nil {
		return nil, err
	}
	err = q.Fields("tt_train_task_execution.*", "tt_train_task.task_name").OrderDesc(dao.TrainTaskExecution.Columns().Id).Page(in.Page, in.PageSize).Scan(&list)
	if err != nil {
		return nil, err
	}

	pageData = &dto.TrainTaskExecutionListOutput{
		List:        list,
		Total:       total,
		CurrentPage: in.Page,
		PageSize:    in.PageSize,
	}
	return
}

func (this *sTrainTaskExecution) List(ctx context.Context, in dto.TrainTaskExecutionListInput) (list []*dto.TrainTaskExecution, err error) {
	list = make([]*dto.TrainTaskExecution, 0)
	q := dao.TrainTaskExecution.Ctx(ctx)
	if in.TaskId != 0 {
		q = q.Where(dao.TrainTaskExecution.Columns().TaskId, in.TaskId)
	}
	if len(in.Statuses) > 0 {
		q = q.WhereIn(dao.TrainTaskExecution.Columns().Status, in.Statuses)
	}
	if in.TriggeredByUserName != "" {
		q = q.Where(dao.TrainTaskExecution.Columns().TriggeredByUserName, in.TriggeredByUserName)
	}
	if len(in.TriggerSources) > 0 {
		q = q.WhereIn(dao.TrainTaskExecution.Columns().TriggerSource, in.TriggerSources)
	}
	if len(in.TriggerTime) > 0 {
		q = q.WhereBetween(dao.TrainTaskExecution.Columns().TriggerTime, in.TriggerTime[0], in.TriggerTime[1])
	}
	if len(in.StartTime) > 0 {
		q = q.WhereBetween(dao.TrainTaskExecution.Columns().StartTime, in.StartTime[0], in.StartTime[1])
	}
	if len(in.EndTime) > 0 {
		q = q.WhereBetween(dao.TrainTaskExecution.Columns().EndTime, in.EndTime[0], in.EndTime[1])
	}
	err = q.OrderDesc(dao.TrainTaskExecution.Columns().Id).Scan(&list)
	if err != nil {
		return nil, err
	}
	return
}

func (this *sTrainTaskExecution) Get(ctx context.Context, id uint) (trainTaskExecution *dto.TrainTaskExecution, err error) {
	trainTaskExecution = &dto.TrainTaskExecution{}
	err = dao.TrainTaskExecution.Ctx(ctx).Where(dao.TrainTaskExecution.Columns().Id, id).Scan(trainTaskExecution)
	if err != nil {
		return nil, err
	}
	return
}

func (this *sTrainTaskExecution) Interrupt(ctx context.Context, id uint) (err error) {
	trainTaskExecution := &dto.TrainTaskExecution{}
	err = dao.TrainTaskExecution.Ctx(ctx).Where(dao.TrainTaskExecution.Columns().Id, id).Scan(trainTaskExecution)
	if err != nil {
		return err
	}
	if trainTaskExecution.Status != consts.TrainTaskExecutionStatusRunning && trainTaskExecution.Status != consts.TrainTaskExecutionStatusPending {
		return nil
	}
	err = dao.TrainTaskExecution.Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		switch trainTaskExecution.TrainingFramework {
		case consts.TrainingFrameworkRay:
			// delete rayjob
			err = client.DeleteRayJob(ctx, trainTaskExecution.ClusterName, trainTaskExecution.Namespace, trainTaskExecution.ExecutionName)
			if err != nil {
				return err
			}
		case consts.TrainingFrameworkCustom:
			// delete customjob
			deletePropagationBackground := metav1.DeletePropagationBackground
			err = client.DeleteJobWithOption(
				ctx, trainTaskExecution.ClusterName, trainTaskExecution.Namespace, trainTaskExecution.ExecutionName, &metav1.DeleteOptions{
					PropagationPolicy: &deletePropagationBackground,
				})
			if err != nil {
				return err
			}
		case consts.TrainingFrameworkTFJob:
			// delete tfjob
			err = client.DeleteTFJob(ctx, trainTaskExecution.ClusterName, trainTaskExecution.Namespace, trainTaskExecution.ExecutionName)
			if err != nil {
				return err
			}
		case consts.TrainingFrameworkPyTorch:
			// delete pytorchjob
			err = client.DeletePyTorchJob(ctx, trainTaskExecution.ClusterName, trainTaskExecution.Namespace, trainTaskExecution.ExecutionName)
			if err != nil {
				return err
			}
		case consts.TrainingFrameworkMPI:
			// delete mpijob
			err = client.DeleteMPIJobV2Beta1(ctx, trainTaskExecution.ClusterName, trainTaskExecution.Namespace, trainTaskExecution.ExecutionName)
			if err != nil {
				return err
			}
		default:
			return fmt.Errorf("不支持的训练框架: %s", trainTaskExecution.TrainingFramework)
		}
		// update status
		trainTaskExecution.Status = consts.TrainTaskExecutionStatusCancelled
		trainTaskExecution.EndTime = gtime.Now()
		trainTaskExecution.Duration = uint(trainTaskExecution.EndTime.Sub(trainTaskExecution.TriggerTime).Minutes())
		_, err = tx.Model(dao.TrainTaskExecution.Table()).Where(dao.TrainTaskExecution.Columns().Id, id).Data(trainTaskExecution).Update()
		if err != nil {
			return err
		}

		// update complete_count && last_status
		_, err = tx.Model(dao.TrainTask.Table()).Data(g.Map{"complete_count": gdb.Raw("complete_count + 1"), "last_status": trainTaskExecution.Status}).Where(dao.TrainTask.Columns().Id, trainTaskExecution.TaskId).Update()
		if err != nil {
			return err
		}
		return nil
	})
	return err
}

func (this *sTrainTaskExecution) SyncTaskExecutionStatus(ctx context.Context) (err error) {
	allTriggeredExecutions := make([]*dto.TrainTaskExecution, 0)
	err = dao.TrainTaskExecution.Ctx(ctx).WhereIn(dao.TrainTaskExecution.Columns().Status, []string{consts.TrainTaskExecutionStatusRunning, consts.TrainTaskExecutionStatusPending}).Scan(&allTriggeredExecutions)
	if err != nil {
		return err
	}
	for _, execution := range allTriggeredExecutions {
		status := ""
		switch execution.TrainingFramework {
		case consts.TrainingFrameworkRay:
			// get rayjob status
			rayjob, err := client.GetRayJob(ctx, execution.ClusterName, execution.Namespace, execution.ExecutionName)
			if err != nil {
				log.L.WithName("SyncTaskExecutionStatus").Errorf(ctx, "get rayjob error:%s", err.Error())
				if strings.Contains(err.Error(), "not found") {
					status = consts.TrainTaskExecutionStatusCancelled
				} else {
					continue
				}
			} else if rayjob.Spec.Suspend {
				status = consts.TrainTaskExecutionStatusPending
			} else {
				status = string(rayjob.Status.JobStatus)
			}
			if status == "" {
				log.L.WithName("SyncTaskExecutionStatus").Warningf(ctx, "can not get rayjob status")
				status = consts.TrainTaskExecutionStatusRunning
			}
		case consts.TrainingFrameworkCustom:
			// get customjob status
			customJob, err := client.GetJob(ctx, execution.ClusterName, execution.Namespace, execution.ExecutionName)
			if err != nil {
				log.L.WithName("SyncTaskExecutionStatus").Errorf(ctx, "get customjob error:%s", err.Error())
				if strings.Contains(err.Error(), "not found") {
					status = consts.TrainTaskExecutionStatusCancelled
				} else {
					continue
				}
			} else {
				status = getCustomJobStatus(customJob)
			}
			if status == "" {
				log.L.WithName("SyncTaskExecutionStatus").Errorf(ctx, "can not get customjob status")
				continue
			}
		case consts.TrainingFrameworkTFJob:
			// get tfjob status
			tfJob, err := client.GetTFJob(ctx, execution.ClusterName, execution.Namespace, execution.ExecutionName)
			if err != nil {
				log.L.WithName("SyncTaskExecutionStatus").Errorf(ctx, "get tfjob error:%s", err.Error())
				if strings.Contains(err.Error(), "not found") {
					status = consts.TrainTaskExecutionStatusCancelled
				} else {
					continue
				}
			} else if tfJob.Spec.RunPolicy.Suspend != nil && *tfJob.Spec.RunPolicy.Suspend {
				status = consts.TrainTaskExecutionStatusPending
			} else if len(tfJob.Status.Conditions) > 0 {
				for _, condition := range tfJob.Status.Conditions {
					if condition.Type == trainingv1.JobSucceeded && condition.Status == v1.ConditionTrue {
						status = consts.TrainTaskExecutionStatusSucceeded
						break
					}
					if condition.Type == trainingv1.JobFailed && condition.Status == v1.ConditionTrue {
						status = consts.TrainTaskExecutionStatusFailed
						break
					}
				}
			}
			if status == "" {
				status = consts.TrainTaskExecutionStatusRunning
			}
		case consts.TrainingFrameworkPyTorch:
			// get pytorchjob status
			pyTorchJob, err := client.GetPyTorchJob(ctx, execution.ClusterName, execution.Namespace, execution.ExecutionName)
			if err != nil {
				log.L.WithName("SyncTaskExecutionStatus").Errorf(ctx, "get pytorchjob error:%s", err.Error())
				if strings.Contains(err.Error(), "not found") {
					status = consts.TrainTaskExecutionStatusCancelled
				} else {
					continue
				}
			} else if pyTorchJob.Spec.RunPolicy.Suspend != nil && *pyTorchJob.Spec.RunPolicy.Suspend {
				status = consts.TrainTaskExecutionStatusPending
			} else if len(pyTorchJob.Status.Conditions) > 0 {
				for _, condition := range pyTorchJob.Status.Conditions {
					if condition.Type == trainingv1.JobSucceeded && condition.Status == v1.ConditionTrue {
						status = consts.TrainTaskExecutionStatusSucceeded
						break
					}
					if condition.Type == trainingv1.JobFailed && condition.Status == v1.ConditionTrue {
						status = consts.TrainTaskExecutionStatusFailed
						break
					}
				}
			}
			if status == "" {
				status = consts.TrainTaskExecutionStatusRunning
			}
		case consts.TrainingFrameworkMPI:
			// get mpijob status
			mpiJob, err := client.GetMPIJobV2Beta1(ctx, execution.ClusterName, execution.Namespace, execution.ExecutionName)
			if err != nil {
				log.L.WithName("SyncTaskExecutionStatus").Errorf(ctx, "get mpijob error:%s", err.Error())
				if strings.Contains(err.Error(), "not found") {
					status = consts.TrainTaskExecutionStatusCancelled
				} else {
					continue
				}
			} else if mpiJob.Spec.RunPolicy.Suspend != nil && *mpiJob.Spec.RunPolicy.Suspend {
				status = consts.TrainTaskExecutionStatusPending
			} else if len(mpiJob.Status.Conditions) > 0 {
				for _, condition := range mpiJob.Status.Conditions {
					if condition.Type == mpiv2beta1.JobSucceeded && condition.Status == v1.ConditionTrue {
						status = consts.TrainTaskExecutionStatusSucceeded
						break
					}
					if condition.Type == mpiv2beta1.JobFailed && condition.Status == v1.ConditionTrue {
						status = consts.TrainTaskExecutionStatusFailed
						break
					}
				}
			}
			if status == "" {
				status = consts.TrainTaskExecutionStatusRunning
			}
		default:
			log.L.WithName("SyncTaskExecutionStatus").Errorf(ctx, "not support training framework:%s", execution.TrainingFramework)
			continue
		}

		dao.TrainTaskExecution.Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
			trainTask := &entity.TrainTask{}
			err = dao.TrainTask.Ctx(ctx).Where(dao.TrainTask.Columns().Id, execution.TaskId).Scan(trainTask)
			if err != nil {
				log.L.WithName("SyncTaskExecutionStatus").Errorf(ctx, "get train task error:%s", err.Error())
				return err
			}
			trainTask.LastStatus = status
			_, err = tx.Model(dao.TrainTask.Table()).Where(dao.TrainTask.Columns().Id, execution.TaskId).Data(trainTask).Update()
			if err != nil {
				log.L.WithName("SyncTaskExecutionStatus").Errorf(ctx, "update train task error:%s", err.Error())
				return err
			}
			execution.Status = status
			if status == consts.TrainTaskExecutionStatusSucceeded || status == consts.TrainTaskExecutionStatusFailed || status == consts.TrainTaskExecutionStatusStopped || status == consts.TrainTaskExecutionStatusCancelled {
				execution.EndTime = gtime.Now()
				execution.Duration = uint(execution.EndTime.Sub(execution.TriggerTime).Minutes())
				// 更新完成计数
				_, err = tx.Model(dao.TrainTask.Table()).Data("complete_count", gdb.Raw("complete_count + 1")).Where(dao.TrainTask.Columns().Id, execution.TaskId).Update()
				if err != nil {
					log.L.WithName("SyncTaskExecutionStatus").Errorf(ctx, "update train task error:%s", err.Error())
					return err
				}
			}
			_, err = tx.Model(dao.TrainTaskExecution.Table()).Where(dao.TrainTaskExecution.Columns().Id, execution.Id).Data(execution).Update()
			if err != nil {
				log.L.WithName("SyncTaskExecutionStatus").Errorf(ctx, "update rayjob error:%s", err.Error())
				return err
			}
			return nil
		})
	}
	return
}

// 获取 customjob 状态
func getCustomJobStatus(job *batchv1.Job) string {
	if isJobCompleted(job) {
		return consts.TrainTaskExecutionStatusSucceeded
	}
	if isJobFailed(job) {
		return consts.TrainTaskExecutionStatusFailed
	}
	// Kueue 排队为「挂起 」状态
	if job.Spec.Suspend != nil && *job.Spec.Suspend {
		return consts.TrainTaskExecutionStatusPending
	}
	return consts.TrainTaskExecutionStatusRunning
}

// 判断 Job 是否成功完成
func isJobCompleted(job *batchv1.Job) bool {
	for _, condition := range job.Status.Conditions {
		if condition.Type == batchv1.JobComplete && condition.Status == v1.ConditionTrue {
			return true
		}
	}
	return false
}

// 判断 Job 是否失败
func isJobFailed(job *batchv1.Job) bool {
	for _, condition := range job.Status.Conditions {
		if condition.Type == batchv1.JobFailed && condition.Status == v1.ConditionTrue {
			return true
		}
	}
	return false
}

func (this *sTrainTaskExecution) UpdatePriority(ctx context.Context, id int, priority string) (err error) {
	// 1. 查找训练任务执行记录
	trainTaskExecution := &dto.TrainTaskExecution{}
	err = dao.TrainTaskExecution.Ctx(ctx).Where(dao.TrainTaskExecution.Columns().Id, id).Scan(trainTaskExecution)
	if err != nil {
		return err
	}

	// 2. 检查执行任务状态，只有 pending 状态才允许修改
	if trainTaskExecution.Status != consts.TrainTaskExecutionStatusPending {
		return fmt.Errorf("只有 pending 状态的训练任务执行才允许修改优先级，当前状态为: %s", trainTaskExecution.Status)
	}

	// 3. 使用事务确保数据一致性
	err = dao.TrainTaskExecution.Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		var jobUid string

		// 4. 根据训练框架获取对应的 Job UID
		switch trainTaskExecution.TrainingFramework {
		case consts.TrainingFrameworkRay:
			// 获取 RayJob
			rayjob, err := client.GetRayJob(ctx, trainTaskExecution.ClusterName, trainTaskExecution.Namespace, trainTaskExecution.ExecutionName)
			if err != nil {
				return fmt.Errorf("获取 RayJob 失败: %w", err)
			}
			jobUid = string(rayjob.UID)

		case consts.TrainingFrameworkCustom:
			// 获取 Job
			customJob, err := client.GetJob(ctx, trainTaskExecution.ClusterName, trainTaskExecution.Namespace, trainTaskExecution.ExecutionName)
			if err != nil {
				return fmt.Errorf("获取 Job 失败: %w", err)
			}
			jobUid = string(customJob.UID)

		default:
			return fmt.Errorf("不支持的训练框架: %s", trainTaskExecution.TrainingFramework)
		}

		// 5. 更新 Workload 的优先级
		err = kueue.UpdateWorkloadPriority(ctx, trainTaskExecution.ClusterName, trainTaskExecution.Namespace, jobUid, priority)
		if err != nil {
			return fmt.Errorf("更新 Workload 优先级失败: %w", err)
		}

		// 6. 更新数据库记录的优先级字段
		_, err = tx.Model(dao.TrainTaskExecution.Table()).
			Where(dao.TrainTaskExecution.Columns().Id, id).
			Data(g.Map{"priority": priority}).
			Update()
		if err != nil {
			return fmt.Errorf("更新数据库优先级字段失败: %w", err)
		}

		return nil
	})

	return err
}
