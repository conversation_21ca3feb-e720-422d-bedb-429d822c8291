package traintask

import (
	"context"
	"testing"
	"time"

	"github.com/gogf/gf/v2/test/gtest"
	"mlops/internal/consts"
	"mlops/internal/model/entity"
)

// TestKubeflowTrainingIntegration 测试Kubeflow训练框架的集成功能
func TestKubeflowTrainingIntegration(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		ctx := context.Background()
		
		// 测试TFJob集成
		t.Log("开始测试TFJob集成...")
		testTFJobIntegration(ctx, t)
		
		// 测试PyTorchJob集成
		t.Log("开始测试PyTorchJob集成...")
		testPyTorchJobIntegration(ctx, t)
		
		// 测试MPIJob集成
		t.Log("开始测试MPIJob集成...")
		testMPIJobIntegration(ctx, t)
	})
}

// testTFJobIntegration 测试TFJob的集成功能
func testTFJobIntegration(ctx context.Context, t *gtest.T) {
	// 创建测试用的训练任务
	trainTask := createTestTrainTask(consts.TrainingFrameworkTFJob, "test-tfjob-integration")
	clusterResource := createTestClusterResource()
	volumeMounts := createTestVolumeMounts()
	
	// 测试toTFJob转换函数
	tfJob := toTFJob(trainTask, trainTask.TaskName, clusterResource, volumeMounts)
	t.AssertNE(tfJob, nil)
	t.AssertEQ(tfJob.Name, trainTask.TaskName)
	t.AssertEQ(tfJob.Namespace, trainTask.Namespace)
	
	// 验证标签
	t.AssertEQ(tfJob.Labels[consts.TeamIdLabelKey], "1")
	t.AssertEQ(tfJob.Labels[consts.TaskExecutionNameLabelKey], trainTask.TaskName)
	
	// 验证Pod模板
	workerSpec := tfJob.Spec.TFReplicaSpecs[trainingv1.TFJobReplicaTypeWorker]
	t.AssertNE(workerSpec, nil)
	t.AssertEQ(*workerSpec.Replicas, int32(2))
	
	// 验证容器配置
	container := workerSpec.Template.Spec.Containers[0]
	t.AssertEQ(container.Name, "tensorflow")
	t.AssertEQ(container.Image, trainTask.ImageUrl)
	
	// 验证Istio sidecar禁用
	annotations := workerSpec.Template.ObjectMeta.Annotations
	t.AssertEQ(annotations[consts.SidecarIstioInjectKey], consts.SidecarIstioInjectValue)
	
	t.Log("TFJob集成测试通过")
}

// testPyTorchJobIntegration 测试PyTorchJob的集成功能
func testPyTorchJobIntegration(ctx context.Context, t *gtest.T) {
	// 创建测试用的训练任务
	trainTask := createTestTrainTask(consts.TrainingFrameworkPyTorch, "test-pytorchjob-integration")
	clusterResource := createTestClusterResource()
	volumeMounts := createTestVolumeMounts()
	
	// 测试toPyTorchJob转换函数
	pyTorchJob := toPyTorchJob(trainTask, trainTask.TaskName, clusterResource, volumeMounts)
	t.AssertNE(pyTorchJob, nil)
	t.AssertEQ(pyTorchJob.Name, trainTask.TaskName)
	t.AssertEQ(pyTorchJob.Namespace, trainTask.Namespace)
	
	// 验证标签
	t.AssertEQ(pyTorchJob.Labels[consts.TeamIdLabelKey], "1")
	t.AssertEQ(pyTorchJob.Labels[consts.TaskExecutionNameLabelKey], trainTask.TaskName)
	
	// 验证Pod模板
	workerSpec := pyTorchJob.Spec.PyTorchReplicaSpecs[trainingv1.PyTorchJobReplicaTypeWorker]
	t.AssertNE(workerSpec, nil)
	t.AssertEQ(*workerSpec.Replicas, int32(2))
	
	// 验证容器配置
	container := workerSpec.Template.Spec.Containers[0]
	t.AssertEQ(container.Name, "pytorch")
	t.AssertEQ(container.Image, trainTask.ImageUrl)
	
	// 验证Istio sidecar禁用
	annotations := workerSpec.Template.ObjectMeta.Annotations
	t.AssertEQ(annotations[consts.SidecarIstioInjectKey], consts.SidecarIstioInjectValue)
	
	t.Log("PyTorchJob集成测试通过")
}

// testMPIJobIntegration 测试MPIJob的集成功能
func testMPIJobIntegration(ctx context.Context, t *gtest.T) {
	// 创建测试用的训练任务
	trainTask := createTestTrainTask(consts.TrainingFrameworkMPI, "test-mpijob-integration")
	clusterResource := createTestClusterResource()
	volumeMounts := createTestVolumeMounts()
	
	// 测试toMPIJob转换函数
	mpiJob := toMPIJob(trainTask, trainTask.TaskName, clusterResource, volumeMounts)
	t.AssertNE(mpiJob, nil)
	t.AssertEQ(mpiJob.Name, trainTask.TaskName)
	t.AssertEQ(mpiJob.Namespace, trainTask.Namespace)
	
	// 验证标签
	t.AssertEQ(mpiJob.Labels[consts.TeamIdLabelKey], "1")
	t.AssertEQ(mpiJob.Labels[consts.TaskExecutionNameLabelKey], trainTask.TaskName)
	
	// 验证Pod模板
	workerSpec := mpiJob.Spec.MPIReplicaSpecs[trainingv1.MPIJobReplicaTypeWorker]
	launcherSpec := mpiJob.Spec.MPIReplicaSpecs[trainingv1.MPIJobReplicaTypeLauncher]
	t.AssertNE(workerSpec, nil)
	t.AssertNE(launcherSpec, nil)
	t.AssertEQ(*workerSpec.Replicas, int32(2))
	t.AssertEQ(*launcherSpec.Replicas, int32(1))
	
	// 验证容器配置
	container := workerSpec.Template.Spec.Containers[0]
	t.AssertEQ(container.Name, "mpi")
	t.AssertEQ(container.Image, trainTask.ImageUrl)
	
	// 验证Istio sidecar禁用
	annotations := workerSpec.Template.ObjectMeta.Annotations
	t.AssertEQ(annotations[consts.SidecarIstioInjectKey], consts.SidecarIstioInjectValue)
	
	t.Log("MPIJob集成测试通过")
}

// createTestTrainTask 创建测试用的训练任务
func createTestTrainTask(framework, name string) *entity.TrainTask {
	return &entity.TrainTask{
		Id:                1,
		TaskName:          name,
		TeamId:            1,
		TeamName:          "test-team",
		Namespace:         "default",
		ClusterName:       "test-cluster",
		TrainingFramework: framework,
		TaskType:          consts.TaskTypeForm,
		ImageUrl:          "test-image:latest",
		StartCmd:          "echo 'Training started'; sleep 10; echo 'Training completed'",
		EnvVars:           "TEST_ENV: test_value",
		CmdbId:            "test-cmdb-id",
		Priority:          consts.PriorityP2,
	}
}

// createTestClusterResource 创建测试用的集群资源
func createTestClusterResource() *entity.TrainTaskClusterResource {
	return &entity.TrainTaskClusterResource{
		Id:               1,
		TaskId:           1,
		MinReplicas:      1,
		MaxReplicas:      2,
		RequestCpu:       "100m",
		RequestMemory:    "256Mi",
		LimitCpu:         "500m",
		LimitMemory:      "512Mi",
		RequestGpuCore:   "0",
		LimitGpuCore:     "0",
		RequestGpuMemory: "0",
		LimitGpuMemory:   "0",
	}
}

// createTestVolumeMounts 创建测试用的存储卷挂载
func createTestVolumeMounts() []*entity.TrainTaskVolumeMount {
	return []*entity.TrainTaskVolumeMount{
		{
			Id:         1,
			TaskId:     1,
			Name:       "test-pvc",
			MountPath:  "/data",
			SubPath:    "",
			VolumeType: "pvc",
			VolumeName: "test-pvc-claim",
		},
		{
			Id:         2,
			TaskId:     1,
			Name:       "test-secret",
			MountPath:  "/secrets",
			SubPath:    "",
			VolumeType: "secret",
			VolumeName: "test-secret",
		},
	}
}
