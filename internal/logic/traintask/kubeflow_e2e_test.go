package traintask

import (
	"context"
	"encoding/json"
	"testing"
	"time"

	"github.com/gogf/gf/v2/test/gtest"
	trainingv1 "github.com/kubeflow/training-operator/pkg/apis/kubeflow.org/v1"
	"mlops/internal/consts"
	"mlops/internal/model/entity"
	"mlops/tools/client"
	v1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/api/resource"
)

// TestKubeflowE2E 端到端测试Kubeflow训练框架
func TestKubeflowE2E(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		ctx := context.Background()
		
		// 测试TFJob端到端流程
		t.Log("开始TFJob端到端测试...")
		testTFJobE2E(ctx, t)
		
		// 测试PyTorchJob端到端流程
		t.Log("开始PyTorchJob端到端测试...")
		testPyTorchJobE2E(ctx, t)
		
		// 测试MPIJob端到端流程
		t.Log("开始MPIJob端到端测试...")
		testMPIJobE2E(ctx, t)
	})
}

// testTFJobE2E 测试TFJob的端到端流程
func testTFJobE2E(ctx context.Context, t *gtest.T) {
	jobName := "e2e-tfjob-test"
	
	// 创建TFJob YAML配置
	tfJobYAML := createTFJobYAML(jobName)
	
	// 创建训练任务实体
	trainTask := &entity.TrainTask{
		Id:                1,
		TaskName:          jobName,
		TeamId:            1,
		TeamName:          "test-team",
		Namespace:         "default",
		ClusterName:       "test-cluster",
		TrainingFramework: consts.TrainingFrameworkTFJob,
		TaskType:          consts.TaskTypeYaml,
		TaskYaml:          tfJobYAML,
		Priority:          consts.PriorityP2,
		CmdbId:            "test-cmdb-id",
	}
	
	// 模拟trigger流程中的YAML解析和创建
	tfJob := &trainingv1.TFJob{}
	err := json.Unmarshal([]byte(trainTask.TaskYaml), tfJob)
	t.AssertNil(err, "解析TFJob YAML失败")
	
	// 设置执行名称
	tfJob.Name = jobName
	
	// 设置Kueue标签（模拟trigger流程）
	if tfJob.Labels == nil {
		tfJob.Labels = make(map[string]string)
	}
	tfJob.Labels["kueue.x-k8s.io/queue-name"] = "test-queue"
	tfJob.Labels["kueue.x-k8s.io/priority-class"] = "test-priority"
	
	// 创建TFJob
	err = client.CreateTFJob(ctx, trainTask.ClusterName, tfJob)
	t.AssertNil(err, "创建TFJob失败")
	
	// 等待Job完成
	waitForTFJobCompletion(ctx, t, jobName)
	
	// 清理资源
	err = client.DeleteTFJob(ctx, trainTask.ClusterName, trainTask.Namespace, jobName)
	t.AssertNil(err, "删除TFJob失败")
	
	t.Log("TFJob端到端测试完成")
}

// testPyTorchJobE2E 测试PyTorchJob的端到端流程
func testPyTorchJobE2E(ctx context.Context, t *gtest.T) {
	jobName := "e2e-pytorchjob-test"
	
	// 创建PyTorchJob YAML配置
	pyTorchJobYAML := createPyTorchJobYAML(jobName)
	
	// 创建训练任务实体
	trainTask := &entity.TrainTask{
		Id:                2,
		TaskName:          jobName,
		TeamId:            1,
		TeamName:          "test-team",
		Namespace:         "default",
		ClusterName:       "test-cluster",
		TrainingFramework: consts.TrainingFrameworkPyTorch,
		TaskType:          consts.TaskTypeYaml,
		TaskYaml:          pyTorchJobYAML,
		Priority:          consts.PriorityP2,
		CmdbId:            "test-cmdb-id",
	}
	
	// 模拟trigger流程中的YAML解析和创建
	pyTorchJob := &trainingv1.PyTorchJob{}
	err := json.Unmarshal([]byte(trainTask.TaskYaml), pyTorchJob)
	t.AssertNil(err, "解析PyTorchJob YAML失败")
	
	// 设置执行名称
	pyTorchJob.Name = jobName
	
	// 设置Kueue标签（模拟trigger流程）
	if pyTorchJob.Labels == nil {
		pyTorchJob.Labels = make(map[string]string)
	}
	pyTorchJob.Labels["kueue.x-k8s.io/queue-name"] = "test-queue"
	pyTorchJob.Labels["kueue.x-k8s.io/priority-class"] = "test-priority"
	
	// 创建PyTorchJob
	err = client.CreatePyTorchJob(ctx, trainTask.ClusterName, pyTorchJob)
	t.AssertNil(err, "创建PyTorchJob失败")
	
	// 等待Job完成
	waitForPyTorchJobCompletion(ctx, t, jobName)
	
	// 清理资源
	err = client.DeletePyTorchJob(ctx, trainTask.ClusterName, trainTask.Namespace, jobName)
	t.AssertNil(err, "删除PyTorchJob失败")
	
	t.Log("PyTorchJob端到端测试完成")
}

// testMPIJobE2E 测试MPIJob的端到端流程
func testMPIJobE2E(ctx context.Context, t *gtest.T) {
	jobName := "e2e-mpijob-test"
	
	// 创建MPIJob YAML配置
	mpiJobYAML := createMPIJobYAML(jobName)
	
	// 创建训练任务实体
	trainTask := &entity.TrainTask{
		Id:                3,
		TaskName:          jobName,
		TeamId:            1,
		TeamName:          "test-team",
		Namespace:         "default",
		ClusterName:       "test-cluster",
		TrainingFramework: consts.TrainingFrameworkMPI,
		TaskType:          consts.TaskTypeYaml,
		TaskYaml:          mpiJobYAML,
		Priority:          consts.PriorityP2,
		CmdbId:            "test-cmdb-id",
	}
	
	// 模拟trigger流程中的YAML解析和创建
	mpiJob := &trainingv1.MPIJob{}
	err := json.Unmarshal([]byte(trainTask.TaskYaml), mpiJob)
	t.AssertNil(err, "解析MPIJob YAML失败")
	
	// 设置执行名称
	mpiJob.Name = jobName
	
	// 设置Kueue标签（模拟trigger流程）
	if mpiJob.Labels == nil {
		mpiJob.Labels = make(map[string]string)
	}
	mpiJob.Labels["kueue.x-k8s.io/queue-name"] = "test-queue"
	mpiJob.Labels["kueue.x-k8s.io/priority-class"] = "test-priority"
	
	// 创建MPIJob
	err = client.CreateMPIJob(ctx, trainTask.ClusterName, mpiJob)
	t.AssertNil(err, "创建MPIJob失败")
	
	// 等待Job完成
	waitForMPIJobCompletion(ctx, t, jobName)
	
	// 清理资源
	err = client.DeleteMPIJob(ctx, trainTask.ClusterName, trainTask.Namespace, jobName)
	t.AssertNil(err, "删除MPIJob失败")
	
	t.Log("MPIJob端到端测试完成")
}

// createTFJobYAML 创建TFJob的YAML配置
func createTFJobYAML(name string) string {
	tfJob := &trainingv1.TFJob{
		ObjectMeta: metav1.ObjectMeta{
			Name:      name,
			Namespace: "default",
		},
		Spec: trainingv1.TFJobSpec{
			TFReplicaSpecs: map[trainingv1.ReplicaType]*trainingv1.ReplicaSpec{
				trainingv1.TFJobReplicaTypeWorker: {
					Replicas: &[]int32{1}[0],
					Template: v1.PodTemplateSpec{
						Spec: v1.PodSpec{
							RestartPolicy: v1.RestartPolicyNever,
							Containers: []v1.Container{
								{
									Name:    "tensorflow",
									Image:   "tensorflow/tensorflow:2.13.0",
									Command: []string{"/bin/bash", "-c"},
									Args:    []string{"echo 'TensorFlow E2E test started'; python -c 'import tensorflow as tf; print(f\"TensorFlow version: {tf.__version__}\"); print(\"E2E test completed successfully\")'; echo 'TensorFlow E2E test completed'"},
									Resources: v1.ResourceRequirements{
										Requests: v1.ResourceList{
											v1.ResourceCPU:    resource.MustParse("100m"),
											v1.ResourceMemory: resource.MustParse("256Mi"),
										},
										Limits: v1.ResourceList{
											v1.ResourceCPU:    resource.MustParse("500m"),
											v1.ResourceMemory: resource.MustParse("512Mi"),
										},
									},
								},
							},
						},
					},
				},
			},
		},
	}
	
	yamlBytes, _ := json.Marshal(tfJob)
	return string(yamlBytes)
}

// createPyTorchJobYAML 创建PyTorchJob的YAML配置
func createPyTorchJobYAML(name string) string {
	pyTorchJob := &trainingv1.PyTorchJob{
		ObjectMeta: metav1.ObjectMeta{
			Name:      name,
			Namespace: "default",
		},
		Spec: trainingv1.PyTorchJobSpec{
			PyTorchReplicaSpecs: map[trainingv1.ReplicaType]*trainingv1.ReplicaSpec{
				trainingv1.PyTorchJobReplicaTypeWorker: {
					Replicas: &[]int32{1}[0],
					Template: v1.PodTemplateSpec{
						Spec: v1.PodSpec{
							RestartPolicy: v1.RestartPolicyNever,
							Containers: []v1.Container{
								{
									Name:    "pytorch",
									Image:   "pytorch/pytorch:2.0.1-cuda11.7-cudnn8-runtime",
									Command: []string{"/bin/bash", "-c"},
									Args:    []string{"echo 'PyTorch E2E test started'; python -c 'import torch; print(f\"PyTorch version: {torch.__version__}\"); print(\"E2E test completed successfully\")'; echo 'PyTorch E2E test completed'"},
									Resources: v1.ResourceRequirements{
										Requests: v1.ResourceList{
											v1.ResourceCPU:    resource.MustParse("100m"),
											v1.ResourceMemory: resource.MustParse("256Mi"),
										},
										Limits: v1.ResourceList{
											v1.ResourceCPU:    resource.MustParse("500m"),
											v1.ResourceMemory: resource.MustParse("512Mi"),
										},
									},
								},
							},
						},
					},
				},
			},
		},
	}
	
	yamlBytes, _ := json.Marshal(pyTorchJob)
	return string(yamlBytes)
}

// createMPIJobYAML 创建MPIJob的YAML配置
func createMPIJobYAML(name string) string {
	mpiJob := &trainingv1.MPIJob{
		ObjectMeta: metav1.ObjectMeta{
			Name:      name,
			Namespace: "default",
		},
		Spec: trainingv1.MPIJobSpec{
			MPIReplicaSpecs: map[trainingv1.ReplicaType]*trainingv1.ReplicaSpec{
				trainingv1.MPIJobReplicaTypeLauncher: {
					Replicas: &[]int32{1}[0],
					Template: v1.PodTemplateSpec{
						Spec: v1.PodSpec{
							RestartPolicy: v1.RestartPolicyNever,
							Containers: []v1.Container{
								{
									Name:    "mpi",
									Image:   "mpioperator/mpi-pi:openmpi",
									Command: []string{"/bin/bash", "-c"},
									Args:    []string{"echo 'MPI E2E test started'; mpirun --allow-run-as-root -np 1 python -c 'print(\"MPI E2E test completed successfully\")'; echo 'MPI E2E test completed'"},
									Resources: v1.ResourceRequirements{
										Requests: v1.ResourceList{
											v1.ResourceCPU:    resource.MustParse("100m"),
											v1.ResourceMemory: resource.MustParse("256Mi"),
										},
										Limits: v1.ResourceList{
											v1.ResourceCPU:    resource.MustParse("500m"),
											v1.ResourceMemory: resource.MustParse("512Mi"),
										},
									},
								},
							},
						},
					},
				},
			},
		},
	}
	
	yamlBytes, _ := json.Marshal(mpiJob)
	return string(yamlBytes)
}
