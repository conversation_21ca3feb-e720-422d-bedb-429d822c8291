package traintask

import (
	"context"
	"database/sql"
	"encoding/json"
	"errors"
	"fmt"
	"mlops/internal/consts"
	errors2 "mlops/internal/consts/errors"
	"mlops/internal/dao"
	"mlops/internal/model/dto"
	"mlops/internal/model/entity"
	"mlops/internal/service"
	"mlops/tools/client"
	"mlops/tools/gpu"
	"strconv"
	"strings"
	"time"

	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"

	"gitlab.ttyuyin.com/yunwei-infra/tt-cloud-constack-sdk/pkg/log"
	"sigs.k8s.io/yaml"

	"mlops/utility/kueue"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/grand"
	rayv1 "github.com/ray-project/kuberay/ray-operator/apis/ray/v1"
	batchv1 "k8s.io/api/batch/v1"
	v1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/api/resource"
)

const (
	VolumeMountName = "%s-%s"
	ExecutionName   = "train-task-%d-%s"
	GpuCoreKey      = "52tt.com/gpu-core"
	GpuMemoryKey    = "52tt.com/gpu-memory"
	LowerLetters    = "abcdefghijklmnopqrstuvwxyz"
)

type sTrainTask struct {
}

func init() {
	service.RegisterTrainTask(newsTrainTask())
}

func newsTrainTask() *sTrainTask {
	return &sTrainTask{}
}

func (this *sTrainTask) Table() string {
	return dao.TrainTask.Table()
}

func (this *sTrainTask) ListPage(ctx context.Context, in dto.TrainTaskListInput) (pageData *dto.TrainTaskListOutput, err error) {
	list := make([]*dto.TrainTask, 0)
	q := dao.TrainTask.Ctx(ctx)
	if in.TaskName != "" {
		q = q.WhereLike(dao.TrainTask.Columns().TaskName, "%"+in.TaskName+"%")
	}
	if in.OnlyMy {
		user := service.BizCtx().Get(ctx).User
		q = q.Where(dao.TrainTask.Columns().CreatedByEmployeeNo, user.EmployeeNo)
	}
	if in.OnlyUnfinished {
		q = q.WhereIn(dao.TrainTask.Columns().LastStatus, []string{consts.TrainTaskExecutionStatusPending, consts.TrainTaskExecutionStatusRunning})
	}
	if len(in.Statuses) > 0 {
		q = q.WhereIn(dao.TrainTask.Columns().LastStatus, in.Statuses)
	}

	if in.TeamId != 0 {
		q = q.Where(dao.TrainTask.Columns().TeamId, in.TeamId)
	}
	if in.CreatedByUserName != "" {
		q = q.Where(dao.TrainTask.Columns().CreatedByUserName, in.CreatedByUserName)
	}
	if in.CreatedByEmployeeNo != "" {
		q = q.Where(dao.TrainTask.Columns().CreatedByEmployeeNo, in.CreatedByEmployeeNo)
	}
	if in.UpdatedByUserName != "" {
		q = q.Where(dao.TrainTask.Columns().UpdatedByUserName, in.UpdatedByUserName)
	}
	if in.UpdatedByEmployeeNo != "" {
		q = q.Where(dao.TrainTask.Columns().UpdatedByEmployeeNo, in.UpdatedByEmployeeNo)
	}
	if len(in.CreatedAt) > 0 {
		q = q.WhereBetween(dao.TrainTask.Columns().CreatedAt, in.CreatedAt[0], in.CreatedAt[1])
	}
	if len(in.UpdatedAt) > 0 {
		q = q.WhereBetween(dao.TrainTask.Columns().UpdatedAt, in.UpdatedAt[0], in.UpdatedAt[1])
	}
	total, err := q.Count()
	if err != nil {
		return nil, err
	}
	err = q.OrderDesc(dao.TrainTask.Columns().Id).Page(in.Page, in.PageSize).Scan(&list)
	if err != nil {
		return nil, err
	}
	return &dto.TrainTaskListOutput{
		List:        list,
		Total:       total,
		CurrentPage: in.Page,
		PageSize:    in.PageSize,
	}, nil
}

func (this *sTrainTask) Get(ctx context.Context, id uint) (trainTask *dto.TrainTask, err error) {
	trainTask = &dto.TrainTask{}
	err = dao.TrainTask.Ctx(ctx).Where(dao.TrainTask.Columns().Id, id).Scan(trainTask)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, errors2.ErrTaskNotFound
		}
		return nil, err
	}

	// json to yaml
	if trainTask.TaskYaml != "" {
		yamlData, err := yaml.JSONToYAML([]byte(trainTask.TaskYaml))
		if err != nil {
			return nil, err
		}
		trainTask.TaskYaml = string(yamlData)
	}

	if trainTask.TaskType == consts.TaskTypeYaml {
		return
	}

	if trainTask.EnvVars != "" {
		trainTask.EnvVarsMap = make(map[string]string)
		err = yaml.Unmarshal([]byte(trainTask.EnvVars), &trainTask.EnvVarsMap)
		if err != nil {
			return nil, err
		}
	}

	trainTask.ClusterResource = dto.TrainTaskClusterResource{}
	trainTask.VolumeMounts = []*dto.TrainTaskVolumeMount{}
	err = dao.TrainTaskClusterResource.Ctx(ctx).Where(dao.TrainTaskClusterResource.Columns().TaskId, id).Scan(&trainTask.ClusterResource)
	if err != nil {
		return nil, err
	}
	err = dao.TrainTaskVolumeMount.Ctx(ctx).Where(dao.TrainTaskVolumeMount.Columns().TaskId, id).Scan(&trainTask.VolumeMounts)
	if err != nil {
		return nil, err
	}
	return
}

func (this *sTrainTask) Create(ctx context.Context, trainTask *dto.TrainTask) (err error) {
	if trainTask.TaskType == consts.TaskTypeYaml && trainTask.TaskYaml == "" {
		return fmt.Errorf("YAML内容不能为空")
	}
	user := service.BizCtx().Get(ctx).User
	trainTask.CreatedByUserName = user.NickName
	trainTask.CreatedByEmployeeNo = user.EmployeeNo
	trainTaskEntity, err := toTrainTaskEntity(trainTask)
	if err != nil {
		return err
	}
	err = dao.TrainTask.Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		team := &dto.Team{}
		err = tx.Model(dao.Team.Table()).Where(dao.Team.Columns().Id, trainTask.TeamId).Scan(team)
		if err != nil {
			return err
		}
		trainTaskEntity.TeamName = team.Name
		id, err := tx.Model(dao.TrainTask.Table()).Data(trainTaskEntity).InsertAndGetId()
		if err != nil {
			return err
		}

		if trainTask.TaskType == consts.TaskTypeYaml {
			return nil
		}

		trainTask.ClusterResource.TaskId = uint(id)
		_, err = tx.Model(dao.TrainTaskClusterResource.Table()).Where(dao.TrainTaskClusterResource.Columns().TaskId, trainTask.Id).Data(trainTask.ClusterResource).Insert()
		if err != nil {
			return err
		}
		for _, volumeMount := range trainTask.VolumeMounts {
			volumeMount.TaskId = uint(id)
			volumeMount.Name = fmt.Sprintf(VolumeMountName, volumeMount.VolumeType, volumeMount.VolumeName)
		}
		_, err = tx.Model(dao.TrainTaskVolumeMount.Table()).Where(dao.TrainTaskVolumeMount.Columns().TaskId, trainTask.Id).Data(trainTask.VolumeMounts).Insert()
		if err != nil {
			return err
		}
		return nil
	})
	return err
}

func (this *sTrainTask) Update(ctx context.Context, trainTask *dto.TrainTask) (err error) {
	user := service.BizCtx().Get(ctx).User
	trainTask.UpdatedByUserName = user.NickName
	trainTask.UpdatedByEmployeeNo = user.EmployeeNo
	trainTaskEntity, err := toTrainTaskEntity(trainTask)
	if err != nil {
		return err
	}
	err = dao.TrainTask.Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		result, err := tx.Model(dao.TrainTask.Table()).
			Where(dao.TrainTask.Columns().Id, trainTask.Id).Data(trainTaskEntity).
			FieldsEx(dao.TrainTask.Columns().CreatedAt,
				dao.TrainTask.Columns().CreatedByUserName,
				dao.TrainTask.Columns().CreatedByEmployeeNo,
				dao.TrainTask.Columns().LastStatus,
				dao.TrainTask.Columns().TeamId,
				dao.TrainTask.Columns().TeamName,
				dao.TrainTask.Columns().TrainingFramework,
				dao.TrainTask.Columns().TriggerCount,
				dao.TrainTask.Columns().CompleteCount).Update()
		if err != nil {
			return err
		}
		if cnt, err := result.RowsAffected(); err != nil || cnt == 0 {
			return fmt.Errorf("train task not found")
		}

		if trainTask.TaskType == consts.TaskTypeYaml {
			return nil
		}

		_, err = tx.Model(dao.TrainTaskClusterResource.Table()).Where(dao.TrainTaskClusterResource.Columns().TaskId, trainTask.Id).Delete()
		if err != nil {
			return err
		}
		_, err = tx.Model(dao.TrainTaskVolumeMount.Table()).Where(dao.TrainTaskVolumeMount.Columns().TaskId, trainTask.Id).Delete()
		if err != nil {
			return err
		}
		trainTask.ClusterResource.TaskId = trainTask.Id
		_, err = tx.Model(dao.TrainTaskClusterResource.Table()).Where(dao.TrainTaskClusterResource.Columns().TaskId, trainTask.Id).Data(trainTask.ClusterResource).Insert()
		if err != nil {
			return err
		}
		for _, volumeMount := range trainTask.VolumeMounts {
			volumeMount.TaskId = trainTask.Id
			volumeMount.Name = fmt.Sprintf(VolumeMountName, volumeMount.VolumeType, volumeMount.VolumeName)
		}
		_, err = tx.Model(dao.TrainTaskVolumeMount.Table()).Where(dao.TrainTaskVolumeMount.Columns().TaskId, trainTask.Id).Data(trainTask.VolumeMounts).Insert()
		if err != nil {
			return err
		}
		return nil
	})
	return err
}

func (this *sTrainTask) Delete(ctx context.Context, id uint) (err error) {
	err = dao.TrainTask.Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		_, err = tx.Model(dao.TrainTask.Table()).Where(dao.TrainTask.Columns().Id, id).Delete()
		if err != nil {
			return err
		}
		//_, err = tx.Model(dao.TrainTaskExecution.Table()).Where(dao.TrainTaskExecution.Columns().TaskId, id).Delete()
		//if err != nil {
		//	return err
		//}
		_, err = tx.Model(dao.TrainTaskClusterResource.Table()).Where(dao.TrainTaskClusterResource.Columns().TaskId, id).Delete()
		if err != nil {
			return err
		}
		_, err = tx.Model(dao.TrainTaskVolumeMount.Table()).Where(dao.TrainTaskVolumeMount.Columns().TaskId, id).Delete()
		if err != nil {
			return err
		}
		return nil
	})
	return
}

func (this *sTrainTask) Trigger(ctx context.Context, id uint, triggerSource string, triggeredByUserName string, triggeredByEmployeeNo string) (err error) {
	trainTask := &entity.TrainTask{}
	err = dao.TrainTask.Ctx(ctx).Where(dao.TrainTask.Columns().Id, id).Scan(trainTask)
	if err != nil {
		return err
	}
	return trigger(ctx, trainTask, triggerSource, triggeredByUserName, triggeredByEmployeeNo)
}

func trigger(ctx context.Context, trainTask *entity.TrainTask, triggerSource string, triggeredByUserName string, triggeredByEmployeeNo string) (err error) {
	// 检查train_task
	if trainTask.LastStatus == consts.TrainTaskExecutionStatusPending || trainTask.LastStatus == consts.TrainTaskExecutionStatusRunning {
		return fmt.Errorf("当前训练任务的状态是 %s, 请不要重复触发", trainTask.LastStatus)
		//return fmt.Errorf("train task status is %s, can not duplicate trigger", trainTask.LastStatus)
	}
	trainTaskExecution := toTrainTaskExecution(trainTask)
	trainTaskExecution.TriggerSource = triggerSource
	trainTaskExecution.TriggeredByUserName = triggeredByUserName
	trainTaskExecution.TriggeredByEmployeeNo = triggeredByEmployeeNo
	err = dao.TrainTaskExecution.Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		switch trainTask.TrainingFramework {
		case consts.TrainingFrameworkRay:
			rayjob := &rayv1.RayJob{}
			switch trainTask.TaskType {
			case consts.TaskTypeYaml:
				err := json.Unmarshal([]byte(trainTask.TaskYaml), rayjob)
				if err != nil {
					log.L.WithName("trigger").Errorf(ctx, "unmarshal rayjob error:%s", err.Error())
					return err
				}
				// YAML任务，使用YAML中的name+8位随机字符串
				// trainTaskExecution.ExecutionName = fmt.Sprintf("%s-%s", rayjob.Name, grand.Str(LowerLetters, 8))
				rayjob.Name = trainTaskExecution.ExecutionName
			case consts.TaskTypeForm:
				// 查询train_task的cluster_resource
				clusterResource := &entity.TrainTaskClusterResource{}
				err = dao.TrainTaskClusterResource.Ctx(ctx).Where(dao.TrainTaskClusterResource.Columns().TaskId, trainTask.Id).Scan(clusterResource)
				if err != nil {
					return err
				}

				// 检查 Kueue 资源
				err = checkKueueResources(ctx, trainTask, clusterResource)
				if err != nil {
					return err
				}

				// 查询train_task的volume_mounts
				volumeMounts := []*entity.TrainTaskVolumeMount{}
				err = dao.TrainTaskVolumeMount.Ctx(ctx).Where(dao.TrainTaskVolumeMount.Columns().TaskId, trainTask.Id).Scan(&volumeMounts)
				if err != nil {
					return err
				}
				// 创建rayjob
				rayjob = toRayjob(trainTask, trainTaskExecution.ExecutionName, clusterResource, volumeMounts)
			}
			// 设置 Kueue 标签
			if rayjob.Labels == nil {
				rayjob.Labels = make(map[string]string)
			}
			// 队列名标签，有则覆盖，无则添加
			rayjob.Labels[kueue.QueueNameLabel] = kueue.LocalQueuePrefix + strconv.Itoa(int(trainTask.TeamId))
			// 优先级标签，有则跳过，无则添加
			if _, exists := rayjob.Labels[kueue.PriorityClassLabel]; !exists {
				rayjob.Labels[kueue.PriorityClassLabel] = kueue.CohortName + "-" + strings.ToLower(trainTask.Priority)
			}

			// https://kueue.sigs.k8s.io/zh-cn/docs/tasks/run/rayjobs/#c-limitations
			// The RayCluster should be deleted at the end of the job execution,
			// spec.ShutdownAfterJobFinishes should be true.
			rayjob.Spec.ShutdownAfterJobFinishes = true
			// Because Kueue will reserve resources for the RayCluster,
			// spec.rayClusterSpec.enableInTreeAutoscaling should be false.
			rayjob.Spec.RayClusterSpec.EnableInTreeAutoscaling = &[]bool{false}[0]
			// Because a Kueue workload can have a maximum of 8 PodSets,
			// the maximum number of spec.rayClusterSpec.workerGroupSpecs is 7.
			if len(rayjob.Spec.RayClusterSpec.WorkerGroupSpecs) > 7 {
				return fmt.Errorf("workerGroupSpecs is too many, max is 7")
			}

			err = client.CreateRayJob(ctx, trainTask.ClusterName, rayjob)
			if err != nil {
				return err
			}

			// wait
			time.Sleep(3 * time.Second)

			rayjob, err := client.GetRayJob(ctx, trainTask.ClusterName, trainTask.Namespace, trainTaskExecution.ExecutionName)
			if err != nil {
				return err
			}
			raycluster := rayjob.Status.RayClusterName
			trainTaskExecution.DashboardUrl = fmt.Sprintf("http://ray.ttyuyin.com:8265/%s/#/overview", raycluster)
			if strings.Contains(trainTask.ClusterName, "hs") {
				trainTaskExecution.DashboardUrl = fmt.Sprintf("http://ray-hs.ttyuyin.com:8265/%s/#/overview", raycluster)
			}
		case consts.TrainingFrameworkCustom:
			customJob := &batchv1.Job{}
			switch trainTask.TaskType {
			case consts.TaskTypeYaml:
				err := json.Unmarshal([]byte(trainTask.TaskYaml), customJob)
				if err != nil {
					log.L.WithName("trigger").Errorf(ctx, "unmarshal customjob error:%s", err.Error())
					return err
				}
				customJob.Name = trainTaskExecution.ExecutionName
			case consts.TaskTypeForm:
				// 查询train_task的cluster_resource
				clusterResource := &entity.TrainTaskClusterResource{}
				err = dao.TrainTaskClusterResource.Ctx(ctx).Where(dao.TrainTaskClusterResource.Columns().TaskId, trainTask.Id).Scan(clusterResource)
				if err != nil {
					return err
				}

				// 检查 Kueue 资源
				err = checkKueueResources(ctx, trainTask, clusterResource)
				if err != nil {
					return err
				}

				// 查询train_task的volume_mounts
				volumeMounts := []*entity.TrainTaskVolumeMount{}
				err = dao.TrainTaskVolumeMount.Ctx(ctx).Where(dao.TrainTaskVolumeMount.Columns().TaskId, trainTask.Id).Scan(&volumeMounts)
				if err != nil {
					return err
				}
				// 创建customjob
				customJob = toCustomjob(trainTask, trainTaskExecution.ExecutionName, clusterResource, volumeMounts)
			}
			// 设置 Kueue 标签
			if customJob.Labels == nil {
				customJob.Labels = make(map[string]string)
			}
			// 队列名标签，有则覆盖，无则添加
			customJob.Labels[kueue.QueueNameLabel] = kueue.LocalQueuePrefix + strconv.Itoa(int(trainTask.TeamId))
			// 优先级标签，有则跳过，无则添加
			if _, exists := customJob.Labels[kueue.PriorityClassLabel]; !exists {
				customJob.Labels[kueue.PriorityClassLabel] = kueue.CohortName + "-" + strings.ToLower(trainTask.Priority)
			}

			// TODO: 创建customjob
			err = client.CreateJob(ctx, trainTask.ClusterName, customJob)
			if err != nil {
				return err
			}
			// 跳转牵星链接不存，动态生成
		default:
			return fmt.Errorf("不支持的训练框架: %s", trainTask.TrainingFramework)
		}
		// 插入train_task_execution
		_, err = tx.Model(dao.TrainTaskExecution.Table()).Data(trainTaskExecution).Insert()
		if err != nil {
			return err
		}
		// 更新train_task的last_status和trigger_count
		trainTask.LastStatus = consts.TrainTaskExecutionStatusPending
		_, err = tx.Model(dao.TrainTask.Table()).Where(dao.TrainTask.Columns().Id, trainTask.Id).Data("last_status", trainTask.LastStatus, "trigger_count", gdb.Raw("trigger_count + 1")).Update()
		if err != nil {
			return err
		}
		return nil
	})
	return err
}

func toRayjob(trainTask *entity.TrainTask, executionName string, clusterResource *entity.TrainTaskClusterResource, volumeMounts []*entity.TrainTaskVolumeMount) *rayv1.RayJob {
	rayjob := &rayv1.RayJob{}
	rayjob.Name = executionName
	rayjob.Namespace = trainTask.Namespace
	rayjob.Kind = "RayJob"
	rayjob.APIVersion = "ray.io/v1"

	mounts := []v1.VolumeMount{}
	volumes := []v1.Volume{}
	duplicateMap := make(map[string]bool)
	for _, volumeMount := range volumeMounts {
		mounts = append(mounts, v1.VolumeMount{
			Name:      volumeMount.Name,
			MountPath: volumeMount.MountPath,
			SubPath:   volumeMount.SubPath,
		})
		if duplicateMap[volumeMount.Name] {
			continue
		}
		duplicateMap[volumeMount.Name] = true
		if volumeMount.VolumeType == "pvc" {
			volumes = append(volumes, v1.Volume{
				Name: volumeMount.Name,
				VolumeSource: v1.VolumeSource{
					PersistentVolumeClaim: &v1.PersistentVolumeClaimVolumeSource{
						ClaimName: volumeMount.VolumeName,
					},
				},
			})
		}
		if volumeMount.VolumeType == "secret" {
			volumes = append(volumes, v1.Volume{
				Name: volumeMount.Name,
				VolumeSource: v1.VolumeSource{
					Secret: &v1.SecretVolumeSource{
						SecretName: volumeMount.VolumeName,
					},
				},
			})
		}
		if volumeMount.VolumeType == "configMap" {
			volumes = append(volumes, v1.Volume{
				Name: volumeMount.Name,
				VolumeSource: v1.VolumeSource{
					ConfigMap: &v1.ConfigMapVolumeSource{
						LocalObjectReference: v1.LocalObjectReference{
							Name: volumeMount.VolumeName,
						},
					},
				},
			})
		}
	}
	replicas := int32(0)
	minReplicas := int32(clusterResource.MinReplicas)
	maxReplicas := int32(clusterResource.MaxReplicas)
	enableInTreeAutoscaling := true

	// parse resources
	resourceRequestMap, resourceLimitMap := parseGpuResource(trainTask.ClusterName, clusterResource)

	envVars := generateEnvVars(trainTask.EnvVars)

	numGpus := 0
	if clusterResource.LimitGpuCore != "" {
		gpuCore, err := strconv.Atoi(clusterResource.LimitGpuCore)
		if err == nil {
			q, r := gpuCore/100, gpuCore%100
			if r > 0 {
				numGpus = q + 1
			} else {
				numGpus = q
			}
		}
	}

	rayjob.Spec = rayv1.RayJobSpec{
		Entrypoint: trainTask.StartCmd,
		RayClusterSpec: &rayv1.RayClusterSpec{
			HeadServiceAnnotations: map[string]string{
				"managed-by": "kuberay-dynamic-route",
			},
			EnableInTreeAutoscaling: &enableInTreeAutoscaling,
			AutoscalerOptions: &rayv1.AutoscalerOptions{
				Resources: &v1.ResourceRequirements{
					Requests: v1.ResourceList{
						v1.ResourceCPU:    resource.MustParse("1"),
						v1.ResourceMemory: resource.MustParse("2Gi"),
					},
					Limits: v1.ResourceList{
						v1.ResourceCPU:    resource.MustParse("2"),
						v1.ResourceMemory: resource.MustParse("4Gi"),
					},
				},
			},
			HeadGroupSpec: rayv1.HeadGroupSpec{
				RayStartParams: map[string]string{
					"num-gpus": "0",
				},
				Template: v1.PodTemplateSpec{
					ObjectMeta: metav1.ObjectMeta{
						Labels: func() map[string]string {
							labels := map[string]string{
								consts.TeamIdLabelKey:            fmt.Sprintf("%d", trainTask.TeamId),
								consts.TaskExecutionNameLabelKey: executionName,
							}
							// 如果CmdbId不为空，添加label_uuid标签
							if trainTask.CmdbId != "" {
								labels[consts.LabelUuidKey] = trainTask.CmdbId
							}
							return labels
						}(),
						Annotations: map[string]string{
							consts.SidecarIstioInjectKey: consts.SidecarIstioInjectValue,
						},
					},
					Spec: v1.PodSpec{
						Containers: []v1.Container{
							{
								Name:  "ray-head",
								Image: trainTask.ImageUrl,
								Ports: []v1.ContainerPort{
									{
										ContainerPort: 6379,
										Name:          "gcs-server",
									},
									{
										ContainerPort: 8265,
										Name:          "dashboard",
									},
									{
										ContainerPort: 10001,
										Name:          "client",
									},
								},
								Resources: v1.ResourceRequirements{
									Requests: v1.ResourceList{
										v1.ResourceCPU:    resource.MustParse("1"),
										v1.ResourceMemory: resource.MustParse("2Gi"),
									},
									Limits: v1.ResourceList{
										v1.ResourceCPU:    resource.MustParse("2"),
										v1.ResourceMemory: resource.MustParse("4Gi"),
									},
								},
								VolumeMounts: mounts,
								Env:          envVars,
							},
						},
						Volumes: volumes,
					},
				},
			},
			WorkerGroupSpecs: []rayv1.WorkerGroupSpec{
				{
					RayStartParams: map[string]string{
						"num-gpus": fmt.Sprintf("%d", numGpus),
					},
					GroupName:   "gpu-group",
					Replicas:    &replicas,
					MinReplicas: &minReplicas,
					MaxReplicas: &maxReplicas,
					Template: v1.PodTemplateSpec{
						ObjectMeta: metav1.ObjectMeta{
							Labels: func() map[string]string {
								labels := map[string]string{
									consts.TeamIdLabelKey:            fmt.Sprintf("%d", trainTask.TeamId),
									consts.TaskExecutionNameLabelKey: executionName,
								}
								// 如果CmdbId不为空，添加label_uuid标签
								if trainTask.CmdbId != "" {
									labels[consts.LabelUuidKey] = trainTask.CmdbId
								}
								return labels
							}(),
							Annotations: map[string]string{
								consts.SidecarIstioInjectKey: consts.SidecarIstioInjectValue,
							},
						},
						Spec: v1.PodSpec{
							Containers: []v1.Container{
								{
									Name:  "ray-worker",
									Image: trainTask.ImageUrl,
									Resources: v1.ResourceRequirements{
										Requests: resourceRequestMap,
										Limits:   resourceLimitMap,
									},
									VolumeMounts: mounts,
									Env:          envVars,
								},
							},
							Volumes: volumes,
							Tolerations: []v1.Toleration{
								{
									Key:      "pool-type",
									Operator: "Equal",
									Value:    "gpu",
									Effect:   "NoSchedule",
								},
								// 试用先加上
								{
									Key:      "biz",
									Operator: "Equal",
									Value:    "mlops",
									Effect:   "NoSchedule",
								},
							},
						},
					},
				},
			},
		},
	}
	if clusterResource.GpuType != "" {
		rayjob.Spec.RayClusterSpec.WorkerGroupSpecs[0].Template.Spec.Affinity = &v1.Affinity{
			NodeAffinity: &v1.NodeAffinity{
				RequiredDuringSchedulingIgnoredDuringExecution: &v1.NodeSelector{
					NodeSelectorTerms: []v1.NodeSelectorTerm{
						{
							MatchExpressions: []v1.NodeSelectorRequirement{
								{
									Key:      "gpu-model",
									Operator: "In",
									Values:   []string{strings.ToLower(clusterResource.GpuType)},
								},
							},
						},
					},
				},
			},
		}
	}
	return rayjob
}

func toCustomjob(trainTask *entity.TrainTask, executionName string, clusterResource *entity.TrainTaskClusterResource, volumeMounts []*entity.TrainTaskVolumeMount) *batchv1.Job {
	job := &batchv1.Job{}
	job.Name = executionName
	job.Namespace = trainTask.Namespace
	job.Kind = "Job"
	job.APIVersion = "batch/v1"
	mounts := []v1.VolumeMount{}
	volumes := []v1.Volume{}
	duplicateMap := make(map[string]bool)
	for _, volumeMount := range volumeMounts {
		mounts = append(mounts, v1.VolumeMount{
			Name:      volumeMount.Name,
			MountPath: volumeMount.MountPath,
			SubPath:   volumeMount.SubPath,
		})
		if duplicateMap[volumeMount.Name] {
			continue
		}
		duplicateMap[volumeMount.Name] = true
		if volumeMount.VolumeType == "pvc" {
			volumes = append(volumes, v1.Volume{
				Name: volumeMount.Name,
				VolumeSource: v1.VolumeSource{
					PersistentVolumeClaim: &v1.PersistentVolumeClaimVolumeSource{
						ClaimName: volumeMount.VolumeName,
					},
				},
			})
		}
		if volumeMount.VolumeType == "secret" {
			volumes = append(volumes, v1.Volume{
				Name: volumeMount.Name,
				VolumeSource: v1.VolumeSource{
					Secret: &v1.SecretVolumeSource{
						SecretName: volumeMount.VolumeName,
					},
				},
			})
		}
		if volumeMount.VolumeType == "configMap" {
			volumes = append(volumes, v1.Volume{
				Name: volumeMount.Name,
				VolumeSource: v1.VolumeSource{
					ConfigMap: &v1.ConfigMapVolumeSource{
						LocalObjectReference: v1.LocalObjectReference{
							Name: volumeMount.VolumeName,
						},
					},
				},
			})
		}
	}
	// 解析资源
	resourceRequestMap, resourceLimitMap := parseGpuResource(trainTask.ClusterName, clusterResource)

	// 计算shm大小（申请内存的一半）
	var shmSize resource.Quantity
	if memoryRequest, exists := resourceRequestMap[v1.ResourceMemory]; exists {
		// 获取内存大小（以字节为单位）
		memoryBytes := memoryRequest.Value()
		// 计算一半大小
		shmBytes := memoryBytes / 2
		shmSize = *resource.NewQuantity(shmBytes, resource.BinarySI)
	} else {
		// 如果没有设置内存请求，使用默认值64Mi
		shmSize = resource.MustParse("64Mi")
	}

	// 添加shm volume mount
	shmVolumeMount := v1.VolumeMount{
		Name:      "dshm",
		MountPath: "/dev/shm",
	}
	mounts = append(mounts, shmVolumeMount)

	// 添加shm volume
	shmVolume := v1.Volume{
		Name: "dshm",
		VolumeSource: v1.VolumeSource{
			EmptyDir: &v1.EmptyDirVolumeSource{
				Medium:    v1.StorageMediumMemory,
				SizeLimit: &shmSize,
			},
		},
	}
	volumes = append(volumes, shmVolume)

	envVars := generateEnvVars(trainTask.EnvVars)
	job.Spec = batchv1.JobSpec{
		Template: v1.PodTemplateSpec{
			ObjectMeta: metav1.ObjectMeta{
				Labels: func() map[string]string {
					labels := map[string]string{
						consts.TeamIdLabelKey:            fmt.Sprintf("%d", trainTask.TeamId),
						consts.TaskExecutionNameLabelKey: executionName,
					}
					// 如果CmdbId不为空，添加label_uuid标签
					if trainTask.CmdbId != "" {
						labels[consts.LabelUuidKey] = trainTask.CmdbId
					}
					return labels
				}(),
				Annotations: map[string]string{
					consts.SidecarIstioInjectKey: consts.SidecarIstioInjectValue,
				},
			},
			Spec: v1.PodSpec{
				RestartPolicy: v1.RestartPolicyNever,
				Containers: []v1.Container{
					{
						Name:  "train-task",
						Image: trainTask.ImageUrl,
						Command: []string{
							"/bin/bash",
						},
						Args: []string{
							"-c",
							trainTask.StartCmd,
						},
						VolumeMounts: mounts,
						Env:          envVars,
						Resources: v1.ResourceRequirements{
							Requests: resourceRequestMap,
							Limits:   resourceLimitMap,
						},
					},
				},
				Volumes: volumes,
				Tolerations: []v1.Toleration{
					{
						Key:      "pool-type",
						Operator: "Equal",
						Value:    "gpu",
						Effect:   "NoSchedule",
					},
					// 试用先加上
					{
						Key:      "biz",
						Operator: "Equal",
						Value:    "mlops",
						Effect:   "NoSchedule",
					},
				},
			},
		},
	}
	if clusterResource.GpuType != "" {
		job.Spec.Template.Spec.Affinity = &v1.Affinity{
			NodeAffinity: &v1.NodeAffinity{
				RequiredDuringSchedulingIgnoredDuringExecution: &v1.NodeSelector{
					NodeSelectorTerms: []v1.NodeSelectorTerm{
						{
							MatchExpressions: []v1.NodeSelectorRequirement{
								{
									Key:      "gpu-model",
									Operator: "In",
									Values:   []string{strings.ToLower(clusterResource.GpuType)},
								},
							},
						},
					},
				},
			},
		}
	}
	return job
}

func generateEnvVars(envVars string) []v1.EnvVar {
	if envVars == "" {
		return nil
	}
	envVarsMap := make(map[string]string)
	err := yaml.Unmarshal([]byte(envVars), &envVarsMap)
	if err != nil {
		return nil
	}
	var envVarsList []v1.EnvVar
	for k, v := range envVarsMap {
		envVarsList = append(envVarsList, v1.EnvVar{
			Name:  k,
			Value: v,
		})
	}
	return envVarsList
}

func parseGpuResource(clusterName string, clusterResource *entity.TrainTaskClusterResource) (map[v1.ResourceName]resource.Quantity, map[v1.ResourceName]resource.Quantity) {
	// parse resources
	resourceRequestMap := make(map[v1.ResourceName]resource.Quantity)
	resourceRequestMap[v1.ResourceCPU], _ = resource.ParseQuantity(clusterResource.RequestCpu)
	resourceRequestMap[v1.ResourceMemory], _ = resource.ParseQuantity(clusterResource.RequestMemory)
	resourceRequestMap[v1.ResourceName(GpuCoreKey)], _ = resource.ParseQuantity(clusterResource.RequestGpuCore)
	resourceRequestMap[v1.ResourceName(GpuMemoryKey)], _ = resource.ParseQuantity(clusterResource.RequestGpuMemory)
	resourceLimitMap := make(map[v1.ResourceName]resource.Quantity)
	resourceLimitMap[v1.ResourceCPU], _ = resource.ParseQuantity(clusterResource.LimitCpu)
	resourceLimitMap[v1.ResourceMemory], _ = resource.ParseQuantity(clusterResource.LimitMemory)
	resourceLimitMap[v1.ResourceName(GpuCoreKey)], _ = resource.ParseQuantity(clusterResource.LimitGpuCore)
	resourceLimitMap[v1.ResourceName(GpuMemoryKey)], _ = resource.ParseQuantity(clusterResource.LimitGpuMemory)
	clusterType := getClusterType(clusterName)
	converter := gpu.GetStrategy(clusterType)
	if converter != nil {
		converter.Convert(resourceRequestMap)
		converter.Convert(resourceLimitMap)
	}
	// 去除map中quantity为0的
	for k, v := range resourceRequestMap {
		if v.Cmp(resource.Quantity{}) == 0 {
			delete(resourceRequestMap, k)
		}
	}
	for k, v := range resourceLimitMap {
		if v.Cmp(resource.Quantity{}) == 0 {
			delete(resourceLimitMap, k)
		}
	}
	return resourceRequestMap, resourceLimitMap
}

func getClusterType(clusterName string) string {
	switch {
	case strings.HasPrefix(clusterName, "k8s-tc"):
		return consts.ClusterTypeTencent
	case strings.HasPrefix(clusterName, "k8s-ali"):
		return consts.ClusterTypeAliyun
	case strings.HasPrefix(clusterName, "k8s-hw"):
		return consts.ClusterTypeHuawei
	case strings.HasPrefix(clusterName, "k8s-hs"):
		return consts.ClusterTypeVolcengine
	default:
		return ""
	}
}

func toTrainTaskExecution(trainTask *entity.TrainTask) *entity.TrainTaskExecution {
	return &entity.TrainTaskExecution{
		TaskId:            trainTask.Id,
		TeamId:            trainTask.TeamId,
		TeamName:          trainTask.TeamName,
		ExecutionName:     fmt.Sprintf(ExecutionName, trainTask.Id, grand.Str(LowerLetters, 8)),
		TrainingFramework: trainTask.TrainingFramework,
		ClusterName:       trainTask.ClusterName,
		ClusterId:         trainTask.ClusterId,
		Namespace:         trainTask.Namespace,
		ImageUrl:          trainTask.ImageUrl,
		Priority:          trainTask.Priority,
		StartTime:         gtime.Now(),
		TriggerTime:       gtime.Now(),
		Status:            consts.TrainTaskExecutionStatusPending,
	}
}

func toTrainTaskEntity(trainTask *dto.TrainTask) (*entity.TrainTask, error) {
	jsonStr := "{}"
	taskName := trainTask.TaskName
	if trainTask.TaskType == consts.TaskTypeYaml && trainTask.TaskYaml != "" {
		switch trainTask.TrainingFramework {
		case consts.TrainingFrameworkRay:
			rayjob := rayv1.RayJob{}
			err := yaml.Unmarshal([]byte(trainTask.TaskYaml), &rayjob)
			if err != nil {
				return nil, err
			}
			// 替换ns
			rayjob.Namespace = trainTask.Namespace
			// 从rayjob yaml中获取taskName
			taskName = rayjob.Name
			// 转换 GPU KEY
			clusterType := getClusterType(trainTask.ClusterName)
			converter := gpu.GetStrategy(clusterType)
			if converter != nil {
				for i, container := range rayjob.Spec.RayClusterSpec.HeadGroupSpec.Template.Spec.Containers {
					converter.Convert(container.Resources.Requests)
					converter.Convert(container.Resources.Limits)
					rayjob.Spec.RayClusterSpec.HeadGroupSpec.Template.Spec.Containers[i].Resources.Requests = container.Resources.Requests
					rayjob.Spec.RayClusterSpec.HeadGroupSpec.Template.Spec.Containers[i].Resources.Limits = container.Resources.Limits
				}
				for i, workerGroupSpec := range rayjob.Spec.RayClusterSpec.WorkerGroupSpecs {
					for j, container := range workerGroupSpec.Template.Spec.Containers {
						converter.Convert(container.Resources.Requests)
						converter.Convert(container.Resources.Limits)
						rayjob.Spec.RayClusterSpec.WorkerGroupSpecs[i].Template.Spec.Containers[j].Resources.Requests = container.Resources.Requests
						rayjob.Spec.RayClusterSpec.WorkerGroupSpecs[i].Template.Spec.Containers[j].Resources.Limits = container.Resources.Limits
					}
				}
			}
			// 转换为 json
			jsonData, err := json.Marshal(rayjob)
			if err != nil {
				return nil, err
			}

			jsonStr = string(jsonData)
		case consts.TrainingFrameworkCustom:
			customJob := batchv1.Job{}
			err := yaml.Unmarshal([]byte(trainTask.TaskYaml), &customJob)
			if err != nil {
				return nil, err
			}
			// 替换ns
			customJob.Namespace = trainTask.Namespace
			// 从customJob yaml中获取taskName
			taskName = customJob.Name
			// 转换 GPU KEY
			clusterType := getClusterType(trainTask.ClusterName)
			converter := gpu.GetStrategy(clusterType)
			if converter != nil {
				for i, container := range customJob.Spec.Template.Spec.Containers {
					converter.Convert(container.Resources.Requests)
					converter.Convert(container.Resources.Limits)
					customJob.Spec.Template.Spec.Containers[i].Resources.Requests = container.Resources.Requests
					customJob.Spec.Template.Spec.Containers[i].Resources.Limits = container.Resources.Limits
				}
			}
			// 转换为 json
			jsonData, err := json.Marshal(customJob)
			if err != nil {
				return nil, err
			}

			jsonStr = string(jsonData)
		default:
			return nil, fmt.Errorf("不支持的训练框架: %s", trainTask.TrainingFramework)
		}

	}
	priority := consts.PriorityP1
	if trainTask.Priority != "" {
		priority = trainTask.Priority
	}
	if trainTask.EnvVarsMap != nil {
		bytes, err := yaml.Marshal(trainTask.EnvVarsMap)
		if err != nil {
			return nil, err
		}
		trainTask.EnvVars = string(bytes)
	}
	return &entity.TrainTask{
		Id:                  trainTask.Id,
		TaskName:            taskName,
		TeamId:              trainTask.TeamId,
		TeamName:            trainTask.TeamName,
		ClusterName:         trainTask.ClusterName,
		ClusterId:           trainTask.ClusterId,
		Namespace:           trainTask.Namespace,
		ImageUrl:            trainTask.ImageUrl,
		StartCmd:            trainTask.StartCmd,
		Priority:            priority,
		TaskType:            trainTask.TaskType,
		TrainingFramework:   trainTask.TrainingFramework,
		TaskYaml:            jsonStr,
		CreatedByEmployeeNo: trainTask.CreatedByEmployeeNo,
		CreatedByUserName:   trainTask.CreatedByUserName,
		UpdatedByEmployeeNo: trainTask.UpdatedByEmployeeNo,
		UpdatedByUserName:   trainTask.UpdatedByUserName,
		EnvVars:             trainTask.EnvVars,
		AppName:             trainTask.AppName,
		CmdbId:              trainTask.CmdbId,
	}, nil
}

// checkKueueResources 检查 Kueue 资源
func checkKueueResources(ctx context.Context, trainTask *entity.TrainTask, clusterResource *entity.TrainTaskClusterResource) error {
	// 1. 检查 WorkloadPriorityClass 资源
	err := kueue.EnsureWorkloadPriorityClass(ctx, trainTask.ClusterName, trainTask.Priority)
	if err != nil {
		return fmt.Errorf("确认 WorkloadPriorityClass 失败: %w", err)
	}

	// 2. 获取团队资源配额
	quotas, err := service.Team().ListGPUQuota(ctx, int(trainTask.TeamId))
	if err != nil {
		return fmt.Errorf("获取团队资源配额失败: %w", err)
	}

	// 3. 检查 GPU 类型是否在配额内
	gpuTypeFound := false
	for _, quota := range quotas {
		if strings.Contains(quota.GpuType, clusterResource.GpuType) {
			gpuTypeFound = true
			break
		}
	}
	if !gpuTypeFound {
		return fmt.Errorf("GPU 类型 %s 不在您所在的团队资源配额内，请联系管理员为您的团队添加", clusterResource.GpuType)
	}

	// 4. 检查 ResourceFlavor 资源
	resourceFlavorName := kueue.ResourceFlavorPrefix + strings.ToLower(clusterResource.GpuType)

	existingRF, err := client.GetResourceFlavor(ctx, trainTask.ClusterName, resourceFlavorName)
	needUpdate := false
	if err != nil || existingRF == nil {
		needUpdate = true
	} else {
		// 5. 检查 ClusterQueue 资源
		clusterQueueName := kueue.ClusterQueuePrefix + strconv.Itoa(int(trainTask.TeamId))
		existingCQ, err := client.GetClusterQueue(ctx, trainTask.ClusterName, clusterQueueName)
		if err != nil || existingCQ == nil {
			needUpdate = true
		} else {
			// 6. 检查 ClusterQueue 的 flavors 中是否包含该 ResourceFlavor
			flavorFound := false
			for _, resourceGroup := range existingCQ.Spec.ResourceGroups {
				for _, flavor := range resourceGroup.Flavors {
					if string(flavor.Name) == resourceFlavorName {
						flavorFound = true
						break
					}
				}
				if flavorFound {
					break
				}
			}
			if !flavorFound {
				needUpdate = true
			}
		}
	}

	// 7. 如果需要更新，触发 Kueue 资源更新
	if needUpdate {
		// 只更新当前集群和命名空间的资源
		namespaces := []string{trainTask.Namespace}

		// 更新 Kueue 资源
		err = kueue.UpdateClusterKueueResources(ctx, int(trainTask.TeamId), trainTask.ClusterName, namespaces, quotas)
		if err != nil {
			return fmt.Errorf("更新 Kueue 资源失败: %w", err)
		}
	}

	return nil
}
