package traintask

import (
	"testing"

	"github.com/gogf/gf/v2/test/gtest"
	mpiv2beta1 "github.com/kubeflow/mpi-operator/pkg/apis/kubeflow/v2beta1"
	"mlops/internal/consts"
	"mlops/internal/model/entity"
)

// TestToMPIJobV2Beta1 测试toMPIJob函数生成v2beta1兼容的配置
func TestToMPIJobV2Beta1(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		// 创建测试数据
		trainTask := &entity.TrainTask{
			Id:                1,
			TaskName:          "test-mpijob-v2beta1",
			TeamId:            1,
			TeamName:          "test-team",
			Namespace:         "default",
			ClusterName:       "test-cluster",
			TrainingFramework: consts.TrainingFrameworkMPI,
			TaskType:          consts.TaskTypeForm,
			ImageUrl:          "mpioperator/mpi-pi:openmpi",
			StartCmd:          "mpirun --allow-run-as-root -np 2 /opt/mpi_hello_world",
			EnvVars:           "TEST_ENV: test_value",
			CmdbId:            "test-cmdb-id",
			Priority:          consts.PriorityP2,
		}

		clusterResource := &entity.TrainTaskClusterResource{
			Id:               1,
			TaskId:           1,
			MinReplicas:      1,
			MaxReplicas:      2,
			RequestCpu:       "200m",
			RequestMemory:    "256Mi",
			LimitCpu:         "500m",
			LimitMemory:      "512Mi",
			RequestGpuCore:   "0",
			LimitGpuCore:     "0",
			RequestGpuMemory: "0",
			LimitGpuMemory:   "0",
		}

		volumeMounts := []*entity.TrainTaskVolumeMount{
			{
				Id:         1,
				TaskId:     1,
				Name:       "test-pvc",
				MountPath:  "/data",
				SubPath:    "",
				VolumeType: "pvc",
				VolumeName: "test-pvc-claim",
			},
		}

		executionName := "test-mpijob-execution"

		// 调用toMPIJob函数
		mpiJob := toMPIJob(trainTask, executionName, clusterResource, volumeMounts)

		// 验证基本属性
		t.AssertNE(mpiJob, nil)
		t.AssertEQ(mpiJob.Name, executionName)
		t.AssertEQ(mpiJob.Namespace, trainTask.Namespace)

		// 验证标签
		t.AssertEQ(mpiJob.Labels[consts.TeamIdLabelKey], "1")
		t.AssertEQ(mpiJob.Labels[consts.TaskExecutionNameLabelKey], executionName)
		t.AssertEQ(mpiJob.Labels[consts.LabelUuidKey], trainTask.CmdbId)

		// 验证v2beta1特性
		spec := mpiJob.Spec

		// 验证SlotsPerWorker
		t.AssertNE(spec.SlotsPerWorker, nil)
		t.AssertEQ(*spec.SlotsPerWorker, int32(1))

		// 验证RunPolicy
		runPolicy := spec.RunPolicy
		t.AssertNE(runPolicy.CleanPodPolicy, nil)
		t.AssertEQ(*runPolicy.CleanPodPolicy, mpiv2beta1.CleanPodPolicyRunning)
		t.AssertNE(runPolicy.TTLSecondsAfterFinished, nil)
		t.AssertEQ(*runPolicy.TTLSecondsAfterFinished, int32(60))
		t.AssertNE(runPolicy.BackoffLimit, nil)
		t.AssertEQ(*runPolicy.BackoffLimit, int32(3))

		// 验证MPIReplicaSpecs
		replicaSpecs := spec.MPIReplicaSpecs
		t.AssertNE(replicaSpecs, nil)

		// 验证Worker配置
		workerSpec := replicaSpecs[mpiv2beta1.MPIReplicaTypeWorker]
		t.AssertNE(workerSpec, nil)
		t.AssertNE(workerSpec.Replicas, nil)
		t.AssertEQ(*workerSpec.Replicas, int32(2))
		t.AssertEQ(workerSpec.RestartPolicy, mpiv2beta1.RestartPolicyNever)

		// 验证Launcher配置
		launcherSpec := replicaSpecs[mpiv2beta1.MPIReplicaTypeLauncher]
		t.AssertNE(launcherSpec, nil)
		t.AssertNE(launcherSpec.Replicas, nil)
		t.AssertEQ(*launcherSpec.Replicas, int32(1))
		t.AssertEQ(launcherSpec.RestartPolicy, mpiv2beta1.RestartPolicyNever)

		// 验证Pod模板
		workerTemplate := workerSpec.Template
		t.AssertEQ(string(workerTemplate.Spec.RestartPolicy), "Never")
		t.AssertEQ(len(workerTemplate.Spec.Containers), 1)

		container := workerTemplate.Spec.Containers[0]
		t.AssertEQ(container.Name, "mpi")
		t.AssertEQ(container.Image, trainTask.ImageUrl)

		// 验证Istio sidecar禁用
		annotations := workerTemplate.ObjectMeta.Annotations
		t.AssertEQ(annotations[consts.SidecarIstioInjectKey], consts.SidecarIstioInjectValue)

		// 验证存储卷挂载
		t.AssertEQ(len(container.VolumeMounts), 1)
		t.AssertEQ(container.VolumeMounts[0].Name, "test-pvc")
		t.AssertEQ(container.VolumeMounts[0].MountPath, "/data")

		t.AssertEQ(len(workerTemplate.Spec.Volumes), 1)
		t.AssertEQ(workerTemplate.Spec.Volumes[0].Name, "test-pvc")
		t.AssertNE(workerTemplate.Spec.Volumes[0].PersistentVolumeClaim, nil)

		t.Log("toMPIJob函数生成的v2beta1配置验证通过")
	})
}

// TestMPIJobV2Beta1Features 测试v2beta1特有功能
func TestMPIJobV2Beta1Features(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		// 创建最小测试数据
		trainTask := &entity.TrainTask{
			Id:                1,
			TaskName:          "test-v2beta1-features",
			TeamId:            1,
			Namespace:         "default",
			ClusterName:       "test-cluster",
			TrainingFramework: consts.TrainingFrameworkMPI,
			ImageUrl:          "test-image:latest",
			StartCmd:          "echo 'test'",
		}

		clusterResource := &entity.TrainTaskClusterResource{
			MaxReplicas:   3,
			RequestCpu:    "100m",
			RequestMemory: "128Mi",
			LimitCpu:      "200m",
			LimitMemory:   "256Mi",
		}

		mpiJob := toMPIJob(trainTask, "test-execution", clusterResource, nil)

		// 测试v2beta1特性
		spec := mpiJob.Spec

		// 1. SlotsPerWorker特性
		t.AssertNE(spec.SlotsPerWorker, nil)
		t.AssertEQ(*spec.SlotsPerWorker, int32(1))

		// 2. RunPolicy特性
		runPolicy := spec.RunPolicy
		
		// CleanPodPolicy
		t.AssertNE(runPolicy.CleanPodPolicy, nil)
		t.AssertEQ(*runPolicy.CleanPodPolicy, mpiv2beta1.CleanPodPolicyRunning)

		// TTLSecondsAfterFinished
		t.AssertNE(runPolicy.TTLSecondsAfterFinished, nil)
		t.AssertEQ(*runPolicy.TTLSecondsAfterFinished, int32(60))

		// BackoffLimit (v2beta1新特性)
		t.AssertNE(runPolicy.BackoffLimit, nil)
		t.AssertEQ(*runPolicy.BackoffLimit, int32(3))

		// 3. 副本级RestartPolicy特性
		workerSpec := spec.MPIReplicaSpecs[mpiv2beta1.MPIReplicaTypeWorker]
		launcherSpec := spec.MPIReplicaSpecs[mpiv2beta1.MPIReplicaTypeLauncher]

		t.AssertEQ(workerSpec.RestartPolicy, mpiv2beta1.RestartPolicyNever)
		t.AssertEQ(launcherSpec.RestartPolicy, mpiv2beta1.RestartPolicyNever)

		// 4. 验证副本数配置
		t.AssertEQ(*workerSpec.Replicas, int32(3)) // 使用MaxReplicas
		t.AssertEQ(*launcherSpec.Replicas, int32(1)) // Launcher固定为1

		t.Log("v2beta1特有功能验证通过")
	})
}

// TestMPIJobCompatibility 测试向后兼容性
func TestMPIJobCompatibility(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		// 测试生成的MPIJob是否与现有代码兼容
		trainTask := &entity.TrainTask{
			Id:                1,
			TaskName:          "compatibility-test",
			TeamId:            1,
			Namespace:         "default",
			TrainingFramework: consts.TrainingFrameworkMPI,
			ImageUrl:          "test-image:latest",
			StartCmd:          "echo 'compatibility test'",
		}

		clusterResource := &entity.TrainTaskClusterResource{
			MaxReplicas:   1,
			RequestCpu:    "100m",
			RequestMemory: "128Mi",
		}

		mpiJob := toMPIJob(trainTask, "compatibility-test", clusterResource, nil)

		// 验证生成的MPIJob可以被正常序列化
		t.AssertNE(mpiJob, nil)
		t.AssertEQ(mpiJob.Kind, "")  // Kind在运行时由client设置
		t.AssertEQ(mpiJob.APIVersion, "") // APIVersion在运行时由client设置

		// 验证必需字段都已设置
		t.AssertNE(mpiJob.Name, "")
		t.AssertNE(mpiJob.Namespace, "")
		t.AssertNE(mpiJob.Spec.MPIReplicaSpecs, nil)
		t.AssertGT(len(mpiJob.Spec.MPIReplicaSpecs), 0)

		t.Log("MPIJob向后兼容性验证通过")
	})
}
