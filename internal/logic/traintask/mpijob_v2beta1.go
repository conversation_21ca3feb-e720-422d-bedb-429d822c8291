package traintask

import (
	"fmt"
	"os"
	"strconv"
	"strings"

	"mlops/internal/consts"
	"mlops/internal/model/entity"

	mpiv2beta1 "github.com/kubeflow/mpi-operator/pkg/apis/kubeflow/v2beta1"
	v1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/api/resource"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
)

// toMPIJobV2Beta1 创建符合v2beta1规范的MPIJob
func toMPIJobV2Beta1(trainTask *entity.TrainTask, executionName string, clusterResource *entity.TrainTaskClusterResource, volumeMounts []*entity.TrainTaskVolumeMount) *mpiv2beta1.MPIJob {
	// 解析资源
	resourceRequestMap, resourceLimitMap := parseGpuResource(trainTask.ClusterName, clusterResource)

	// 生成环境变量
	envVars := generateEnvVars(trainTask.EnvVars)

	// 处理存储卷挂载
	volumes, containerVolumeMounts := processVolumeMounts(volumeMounts)

	// 构建基础标签
	labels := map[string]string{
		consts.TeamIdLabelKey:            strconv.Itoa(int(trainTask.TeamId)),
		consts.TaskExecutionNameLabelKey: executionName,
	}

	// 添加环境标签
	if env := os.Getenv("ENV"); env != "" {
		labels[consts.EnvLabelKey] = env
	}

	// 添加CMDB标签
	if trainTask.CmdbId != "" {
		labels[consts.LabelUuidKey] = trainTask.CmdbId
	}

	// 构建Pod容忍
	tolerations := []v1.Toleration{
		{
			Key:      "pool-type",
			Operator: "Equal",
			Value:    "gpu",
			Effect:   "NoSchedule",
		},
		{
			Key:      "biz",
			Operator: "Equal",
			Value:    "mlops",
			Effect:   "NoSchedule",
		},
	}

	// 构建Pod模板
	podTemplate := v1.PodTemplateSpec{
		ObjectMeta: metav1.ObjectMeta{
			Labels: labels,
			Annotations: map[string]string{
				consts.SidecarIstioInjectKey: consts.SidecarIstioInjectValue,
			},
		},
		Spec: v1.PodSpec{
			RestartPolicy: v1.RestartPolicyNever,
			Containers: []v1.Container{
				{
					Name:            "worker",
					Image:           trainTask.ImageUrl,
					Resources:       v1.ResourceRequirements{Requests: resourceRequestMap, Limits: resourceLimitMap},
					Env:             envVars,
					VolumeMounts:    containerVolumeMounts,
					ImagePullPolicy: v1.PullIfNotPresent,
				},
			},
			Volumes:     volumes,
			Tolerations: tolerations,
		},
	}

	// 添加节点亲和性
	if clusterResource.GpuType != "" {
		podTemplate.Spec.Affinity = &v1.Affinity{
			NodeAffinity: &v1.NodeAffinity{
				RequiredDuringSchedulingIgnoredDuringExecution: &v1.NodeSelector{
					NodeSelectorTerms: []v1.NodeSelectorTerm{
						{
							MatchExpressions: []v1.NodeSelectorRequirement{
								{
									Key:      "gpu-model",
									Operator: "In",
									Values:   []string{strings.ToLower(clusterResource.GpuType)},
								},
							},
						},
					},
				},
			},
		}
	}

	// 创建v2beta1 MPIJob
	mpiJob := &mpiv2beta1.MPIJob{
		ObjectMeta: metav1.ObjectMeta{
			Name:      executionName,
			Namespace: trainTask.Namespace,
			Labels:    labels,
		},
		Spec: mpiv2beta1.MPIJobSpec{
			// v2beta1特性：每个worker的槽位数
			SlotsPerWorker: func() *int32 {
				numGpus := 1
				if clusterResource.LimitGpuCore != "" {
					gpuCore, err := strconv.Atoi(clusterResource.LimitGpuCore)
					if err == nil && gpuCore > 0 {
						q, r := gpuCore/100, gpuCore%100
						if r > 0 {
							numGpus = q + 1
						} else {
							numGpus = q
						}
					}
				}
				i := int32(numGpus)
				return &i
			}(),

			// v2beta1特性：SSH认证挂载路径
			SSHAuthMountPath: "/root/.ssh",

			// v2beta1特性：启动器创建策略
			LauncherCreationPolicy: mpiv2beta1.LauncherCreationPolicyAtStartup,

			// v2beta1特性：MPI实现
			MPIImplementation: mpiv2beta1.MPIImplementationOpenMPI,

			// v2beta1特性：增强的运行策略
			RunPolicy: mpiv2beta1.RunPolicy{
				CleanPodPolicy:          func() *mpiv2beta1.CleanPodPolicy { p := mpiv2beta1.CleanPodPolicyRunning; return &p }(),
				TTLSecondsAfterFinished: func() *int32 { i := int32(60); return &i }(),
				BackoffLimit:            func() *int32 { i := int32(3); return &i }(),
			},

			// MPI副本规格
			MPIReplicaSpecs: map[mpiv2beta1.MPIReplicaType]*mpiv2beta1.ReplicaSpec{
				mpiv2beta1.MPIReplicaTypeWorker: {
					Replicas:      func() *int32 { i := int32(clusterResource.MaxReplicas); return &i }(),
					RestartPolicy: mpiv2beta1.RestartPolicyNever,
					Template:      podTemplate,
				},
				mpiv2beta1.MPIReplicaTypeLauncher: {
					Replicas:      func() *int32 { i := int32(1); return &i }(),
					RestartPolicy: mpiv2beta1.RestartPolicyNever,
					Template: v1.PodTemplateSpec{
						ObjectMeta: metav1.ObjectMeta{
							Labels: labels,
							Annotations: map[string]string{
								consts.SidecarIstioInjectKey: consts.SidecarIstioInjectValue,
							},
						},
						Spec: v1.PodSpec{
							RestartPolicy: v1.RestartPolicyNever,
							Containers: []v1.Container{
								{
									Name:    "launcher",
									Image:   trainTask.ImageUrl,
									Command: []string{"/bin/bash", "-c"},
									Args:    []string{trainTask.StartCmd},
									Resources: v1.ResourceRequirements{
										Requests: v1.ResourceList{
											v1.ResourceCPU:    resource.MustParse("500m"),
											v1.ResourceMemory: resource.MustParse("500Mi"),
										},
										Limits: v1.ResourceList{
											v1.ResourceCPU:    resource.MustParse("2"),
											v1.ResourceMemory: resource.MustParse("1Gi"),
										},
									},
									Env:             envVars,
									VolumeMounts:    containerVolumeMounts,
									ImagePullPolicy: v1.PullIfNotPresent,
								},
							},
							Volumes:     volumes,
							Tolerations: tolerations,
						},
					},
				},
			},
		},
	}

	return mpiJob
}

// parseGpuResource 解析GPU资源配置（复用现有函数逻辑）
func parseGpuResourceV2Beta1(clusterName string, clusterResource *entity.TrainTaskClusterResource) (v1.ResourceList, v1.ResourceList) {
	// 复用现有的parseGpuResource函数逻辑
	return parseGpuResource(clusterName, clusterResource)
}

// generateEnvVarsV2Beta1 生成环境变量（复用现有函数逻辑）
func generateEnvVarsV2Beta1(envVarsStr string) []v1.EnvVar {
	// 复用现有的generateEnvVars函数逻辑
	return generateEnvVars(envVarsStr)
}

// processVolumeMounts 处理存储卷挂载
func processVolumeMounts(volumeMounts []*entity.TrainTaskVolumeMount) ([]v1.Volume, []v1.VolumeMount) {
	mounts := []v1.VolumeMount{}
	volumes := []v1.Volume{}
	duplicateMap := make(map[string]bool)

	for _, volumeMount := range volumeMounts {
		// 添加挂载点
		mounts = append(mounts, v1.VolumeMount{
			Name:      volumeMount.Name,
			MountPath: volumeMount.MountPath,
			SubPath:   volumeMount.SubPath,
		})

		// 避免重复的卷定义
		if duplicateMap[volumeMount.Name] {
			continue
		}
		duplicateMap[volumeMount.Name] = true

		// 根据卷类型创建相应的卷
		switch volumeMount.VolumeType {
		case "pvc":
			volumes = append(volumes, v1.Volume{
				Name: volumeMount.Name,
				VolumeSource: v1.VolumeSource{
					PersistentVolumeClaim: &v1.PersistentVolumeClaimVolumeSource{
						ClaimName: volumeMount.VolumeName,
					},
				},
			})
		case "secret":
			volumes = append(volumes, v1.Volume{
				Name: volumeMount.Name,
				VolumeSource: v1.VolumeSource{
					Secret: &v1.SecretVolumeSource{
						SecretName: volumeMount.VolumeName,
					},
				},
			})
		case "configMap":
			volumes = append(volumes, v1.Volume{
				Name: volumeMount.Name,
				VolumeSource: v1.VolumeSource{
					ConfigMap: &v1.ConfigMapVolumeSource{
						LocalObjectReference: v1.LocalObjectReference{
							Name: volumeMount.VolumeName,
						},
					},
				},
			})
		}
	}

	return volumes, mounts
}

// 注意：不再需要convertToV1MPIJob函数，因为我们直接使用v2beta1版本

// getMPIJobV2Beta1Features 获取v2beta1特有的特性配置
func getMPIJobV2Beta1Features() map[string]interface{} {
	return map[string]interface{}{
		"sshAuthMountPath":        "/root/.ssh",
		"launcherCreationPolicy":  "AtStartup",
		"mpiImplementation":       "OpenMPI",
		"runLauncherAsWorker":     false,
		"backoffLimit":            3,
		"ttlSecondsAfterFinished": 60,
		"cleanPodPolicy":          "Running",
	}
}

// validateMPIJobV2Beta1 验证v2beta1 MPIJob配置
func validateMPIJobV2Beta1(mpiJob *mpiv2beta1.MPIJob) error {
	if mpiJob == nil {
		return fmt.Errorf("MPIJob cannot be nil")
	}

	if mpiJob.Name == "" {
		return fmt.Errorf("MPIJob name cannot be empty")
	}

	if mpiJob.Namespace == "" {
		return fmt.Errorf("MPIJob namespace cannot be empty")
	}

	if len(mpiJob.Spec.MPIReplicaSpecs) == 0 {
		return fmt.Errorf("MPIJob must have at least one replica spec")
	}

	// 验证必需的副本类型
	if _, exists := mpiJob.Spec.MPIReplicaSpecs[mpiv2beta1.MPIReplicaTypeLauncher]; !exists {
		return fmt.Errorf("MPIJob must have a Launcher replica")
	}

	if _, exists := mpiJob.Spec.MPIReplicaSpecs[mpiv2beta1.MPIReplicaTypeWorker]; !exists {
		return fmt.Errorf("MPIJob must have at least one Worker replica")
	}

	return nil
}
