package traintask

import (
	"testing"

	"github.com/gogf/gf/v2/test/gtest"
	mpiv2beta1 "github.com/kubeflow/mpi-operator/pkg/apis/kubeflow/v2beta1"
	"mlops/internal/consts"
	"mlops/internal/model/entity"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
)

// TestToMPIJobV2Beta1Integration 测试v2beta1 MPIJob的完整集成
func TestToMPIJobV2Beta1Integration(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		// 创建测试数据
		trainTask := &entity.TrainTask{
			Id:                1,
			TaskName:          "test-mpijob-v2beta1-integration",
			TeamId:            1,
			TeamName:          "test-team",
			Namespace:         "default",
			ClusterName:       "test-cluster",
			TrainingFramework: consts.TrainingFrameworkMPI,
			TaskType:          consts.TaskTypeForm,
			ImageUrl:          "mpioperator/mpi-pi:openmpi",
			StartCmd:          "mpirun --allow-run-as-root -np 2 /opt/mpi_hello_world",
			EnvVars:           "MPI_ENV: test_value\nOMPI_ALLOW_RUN_AS_ROOT: 1",
			CmdbId:            "test-cmdb-id",
			Priority:          consts.PriorityP2,
		}

		clusterResource := &entity.TrainTaskClusterResource{
			Id:               1,
			TaskId:           1,
			MinReplicas:      1,
			MaxReplicas:      3,
			RequestCpu:       "500m",
			RequestMemory:    "1Gi",
			LimitCpu:         "1000m",
			LimitMemory:      "2Gi",
			RequestGpuCore:   "0",
			LimitGpuCore:     "0",
			RequestGpuMemory: "0",
			LimitGpuMemory:   "0",
		}

		volumeMounts := []*entity.TrainTaskVolumeMount{
			{
				Id:         1,
				TaskId:     1,
				Name:       "data-pvc",
				MountPath:  "/data",
				SubPath:    "",
				VolumeType: "pvc",
				VolumeName: "training-data-pvc",
			},
			{
				Id:         2,
				TaskId:     1,
				Name:       "ssh-secret",
				MountPath:  "/root/.ssh",
				SubPath:    "",
				VolumeType: "secret",
				VolumeName: "mpi-ssh-secret",
			},
		}

		executionName := "test-mpijob-v2beta1-execution"

		// 调用toMPIJobV2Beta1函数
		mpiJob := toMPIJobV2Beta1(trainTask, executionName, clusterResource, volumeMounts)

		// 验证基本属性
		t.AssertNE(mpiJob, nil)
		t.AssertEQ(mpiJob.Name, executionName)
		t.AssertEQ(mpiJob.Namespace, trainTask.Namespace)

		// 验证标签
		t.AssertEQ(mpiJob.Labels[consts.TeamIdLabelKey], "1")
		t.AssertEQ(mpiJob.Labels[consts.TaskExecutionNameLabelKey], executionName)
		t.AssertEQ(mpiJob.Labels[consts.LabelUuidKey], trainTask.CmdbId)

		// 验证v2beta1特有属性
		spec := mpiJob.Spec

		// 验证SlotsPerWorker
		t.AssertNE(spec.SlotsPerWorker, nil)
		t.AssertEQ(*spec.SlotsPerWorker, int32(1))

		// 验证SSHAuthMountPath
		t.AssertEQ(spec.SSHAuthMountPath, "/root/.ssh")

		// 验证LauncherCreationPolicy
		t.AssertEQ(spec.LauncherCreationPolicy, mpiv2beta1.LauncherCreationPolicyAtStartup)

		// 验证MPIImplementation
		t.AssertEQ(spec.MPIImplementation, mpiv2beta1.MPIImplementationOpenMPI)

		// 验证RunPolicy
		runPolicy := spec.RunPolicy
		t.AssertNE(runPolicy.CleanPodPolicy, nil)
		t.AssertEQ(*runPolicy.CleanPodPolicy, mpiv2beta1.CleanPodPolicyRunning)
		t.AssertNE(runPolicy.TTLSecondsAfterFinished, nil)
		t.AssertEQ(*runPolicy.TTLSecondsAfterFinished, int32(60))
		t.AssertNE(runPolicy.BackoffLimit, nil)
		t.AssertEQ(*runPolicy.BackoffLimit, int32(3))

		// 验证MPIReplicaSpecs
		replicaSpecs := spec.MPIReplicaSpecs
		t.AssertNE(replicaSpecs, nil)

		// 验证Worker配置
		workerSpec := replicaSpecs[mpiv2beta1.MPIReplicaTypeWorker]
		t.AssertNE(workerSpec, nil)
		t.AssertNE(workerSpec.Replicas, nil)
		t.AssertEQ(*workerSpec.Replicas, int32(3)) // MaxReplicas
		t.AssertEQ(workerSpec.RestartPolicy, mpiv2beta1.RestartPolicyNever)

		// 验证Launcher配置
		launcherSpec := replicaSpecs[mpiv2beta1.MPIReplicaTypeLauncher]
		t.AssertNE(launcherSpec, nil)
		t.AssertNE(launcherSpec.Replicas, nil)
		t.AssertEQ(*launcherSpec.Replicas, int32(1))
		t.AssertEQ(launcherSpec.RestartPolicy, mpiv2beta1.RestartPolicyNever)

		// 验证Pod模板
		workerTemplate := workerSpec.Template
		t.AssertEQ(string(workerTemplate.Spec.RestartPolicy), "Never")
		t.AssertEQ(len(workerTemplate.Spec.Containers), 1)

		container := workerTemplate.Spec.Containers[0]
		t.AssertEQ(container.Name, "mpi")
		t.AssertEQ(container.Image, trainTask.ImageUrl)

		// 验证Istio sidecar禁用
		annotations := workerTemplate.ObjectMeta.Annotations
		t.AssertEQ(annotations[consts.SidecarIstioInjectKey], consts.SidecarIstioInjectValue)

		// 验证存储卷挂载
		t.AssertEQ(len(container.VolumeMounts), 2)
		
		// 验证数据PVC挂载
		dataMountFound := false
		sshMountFound := false
		for _, mount := range container.VolumeMounts {
			if mount.Name == "data-pvc" && mount.MountPath == "/data" {
				dataMountFound = true
			}
			if mount.Name == "ssh-secret" && mount.MountPath == "/root/.ssh" {
				sshMountFound = true
			}
		}
		t.AssertEQ(dataMountFound, true)
		t.AssertEQ(sshMountFound, true)

		// 验证卷定义
		t.AssertEQ(len(workerTemplate.Spec.Volumes), 2)
		
		pvcVolumeFound := false
		secretVolumeFound := false
		for _, volume := range workerTemplate.Spec.Volumes {
			if volume.Name == "data-pvc" && volume.PersistentVolumeClaim != nil {
				t.AssertEQ(volume.PersistentVolumeClaim.ClaimName, "training-data-pvc")
				pvcVolumeFound = true
			}
			if volume.Name == "ssh-secret" && volume.Secret != nil {
				t.AssertEQ(volume.Secret.SecretName, "mpi-ssh-secret")
				secretVolumeFound = true
			}
		}
		t.AssertEQ(pvcVolumeFound, true)
		t.AssertEQ(secretVolumeFound, true)

		// 验证环境变量
		envFound := false
		for _, env := range container.Env {
			if env.Name == "MPI_ENV" && env.Value == "test_value" {
				envFound = true
				break
			}
		}
		t.AssertEQ(envFound, true)

		t.Log("MPIJob v2beta1集成测试通过")
	})
}

// 注意：不再需要转换测试，因为我们直接使用v2beta1版本

// TestValidateMPIJobV2Beta1 测试v2beta1验证功能
func TestValidateMPIJobV2Beta1(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		// 测试有效的MPIJob
		validJob := &mpiv2beta1.MPIJob{
			ObjectMeta: metav1.ObjectMeta{
				Name:      "valid-job",
				Namespace: "default",
			},
			Spec: mpiv2beta1.MPIJobSpec{
				MPIReplicaSpecs: map[mpiv2beta1.MPIReplicaType]*mpiv2beta1.ReplicaSpec{
					mpiv2beta1.MPIReplicaTypeLauncher: {
						Replicas: func() *int32 { i := int32(1); return &i }(),
					},
					mpiv2beta1.MPIReplicaTypeWorker: {
						Replicas: func() *int32 { i := int32(2); return &i }(),
					},
				},
			},
		}

		err := validateMPIJobV2Beta1(validJob)
		t.AssertNil(err)

		// 测试无效的MPIJob（缺少名称）
		invalidJob := &mpiv2beta1.MPIJob{
			ObjectMeta: metav1.ObjectMeta{
				Namespace: "default",
			},
		}

		err = validateMPIJobV2Beta1(invalidJob)
		t.AssertNE(err, nil)

		// 测试nil MPIJob
		err = validateMPIJobV2Beta1(nil)
		t.AssertNE(err, nil)

		t.Log("MPIJob v2beta1验证测试通过")
	})
}
