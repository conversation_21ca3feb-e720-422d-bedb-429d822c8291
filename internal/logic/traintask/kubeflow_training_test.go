package traintask

import (
	"context"
	"testing"
	"time"

	"github.com/gogf/gf/v2/test/gtest"
	trainingv1 "github.com/kubeflow/training-operator/pkg/apis/kubeflow.org/v1"
	"mlops/internal/consts"
	"mlops/tools/client"
	v1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/api/resource"
)

const (
	testClusterName = "test-cluster"
	testNamespace   = "default"
	testTimeout     = 300 * time.Second // 5分钟超时
)

// TestTFJobCRUD 测试TFJob的完整CRUD操作
func TestTFJobCRUD(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		ctx := context.Background()
		
		// 创建测试用的TFJob
		tfJob := createTestTFJob("test-tfjob")
		
		// 测试创建
		err := client.CreateTFJob(ctx, testClusterName, tfJob)
		t.AssertNil(err)
		
		// 等待一段时间让Job启动
		time.Sleep(10 * time.Second)
		
		// 测试获取
		retrievedTFJob, err := client.GetTFJob(ctx, testClusterName, testNamespace, tfJob.Name)
		t.AssertNil(err)
		t.AssertEQ(retrievedTFJob.Name, tfJob.Name)
		
		// 测试列表
		tfJobs, err := client.ListTFJob(ctx, testClusterName, testNamespace, nil)
		t.AssertNil(err)
		t.AssertGT(len(tfJobs), 0)
		
		// 等待Job完成
		waitForTFJobCompletion(ctx, t, tfJob.Name)
		
		// 测试删除
		err = client.DeleteTFJob(ctx, testClusterName, testNamespace, tfJob.Name)
		t.AssertNil(err)
		
		// 验证删除成功
		time.Sleep(5 * time.Second)
		_, err = client.GetTFJob(ctx, testClusterName, testNamespace, tfJob.Name)
		t.AssertNE(err, nil)
	})
}

// TestPyTorchJobCRUD 测试PyTorchJob的完整CRUD操作
func TestPyTorchJobCRUD(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		ctx := context.Background()
		
		// 创建测试用的PyTorchJob
		pyTorchJob := createTestPyTorchJob("test-pytorchjob")
		
		// 测试创建
		err := client.CreatePyTorchJob(ctx, testClusterName, pyTorchJob)
		t.AssertNil(err)
		
		// 等待一段时间让Job启动
		time.Sleep(10 * time.Second)
		
		// 测试获取
		retrievedPyTorchJob, err := client.GetPyTorchJob(ctx, testClusterName, testNamespace, pyTorchJob.Name)
		t.AssertNil(err)
		t.AssertEQ(retrievedPyTorchJob.Name, pyTorchJob.Name)
		
		// 测试列表
		pyTorchJobs, err := client.ListPyTorchJob(ctx, testClusterName, testNamespace, nil)
		t.AssertNil(err)
		t.AssertGT(len(pyTorchJobs), 0)
		
		// 等待Job完成
		waitForPyTorchJobCompletion(ctx, t, pyTorchJob.Name)
		
		// 测试删除
		err = client.DeletePyTorchJob(ctx, testClusterName, testNamespace, pyTorchJob.Name)
		t.AssertNil(err)
		
		// 验证删除成功
		time.Sleep(5 * time.Second)
		_, err = client.GetPyTorchJob(ctx, testClusterName, testNamespace, pyTorchJob.Name)
		t.AssertNE(err, nil)
	})
}

// TestMPIJobCRUD 测试MPIJob的完整CRUD操作
func TestMPIJobCRUD(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		ctx := context.Background()
		
		// 创建测试用的MPIJob
		mpiJob := createTestMPIJob("test-mpijob")
		
		// 测试创建
		err := client.CreateMPIJob(ctx, testClusterName, mpiJob)
		t.AssertNil(err)
		
		// 等待一段时间让Job启动
		time.Sleep(10 * time.Second)
		
		// 测试获取
		retrievedMPIJob, err := client.GetMPIJob(ctx, testClusterName, testNamespace, mpiJob.Name)
		t.AssertNil(err)
		t.AssertEQ(retrievedMPIJob.Name, mpiJob.Name)
		
		// 测试列表
		mpiJobs, err := client.ListMPIJob(ctx, testClusterName, testNamespace, nil)
		t.AssertNil(err)
		t.AssertGT(len(mpiJobs), 0)
		
		// 等待Job完成
		waitForMPIJobCompletion(ctx, t, mpiJob.Name)
		
		// 测试删除
		err = client.DeleteMPIJob(ctx, testClusterName, testNamespace, mpiJob.Name)
		t.AssertNil(err)
		
		// 验证删除成功
		time.Sleep(5 * time.Second)
		_, err = client.GetMPIJob(ctx, testClusterName, testNamespace, mpiJob.Name)
		t.AssertNE(err, nil)
	})
}

// createTestTFJob 创建测试用的TFJob
func createTestTFJob(name string) *trainingv1.TFJob {
	return &trainingv1.TFJob{
		ObjectMeta: metav1.ObjectMeta{
			Name:      name,
			Namespace: testNamespace,
			Labels: map[string]string{
				consts.TeamIdLabelKey:            "1",
				consts.TaskExecutionNameLabelKey: name,
				consts.EnvLabelKey:               "test",
			},
		},
		Spec: trainingv1.TFJobSpec{
			TFReplicaSpecs: map[trainingv1.ReplicaType]*trainingv1.ReplicaSpec{
				trainingv1.TFJobReplicaTypeWorker: {
					Replicas: &[]int32{1}[0],
					Template: v1.PodTemplateSpec{
						ObjectMeta: metav1.ObjectMeta{
							Annotations: map[string]string{
								consts.SidecarIstioInjectKey: consts.SidecarIstioInjectValue,
							},
						},
						Spec: v1.PodSpec{
							RestartPolicy: v1.RestartPolicyNever,
							Containers: []v1.Container{
								{
									Name:    "tensorflow",
									Image:   "tensorflow/tensorflow:2.13.0",
									Command: []string{"/bin/bash", "-c"},
									Args:    []string{"echo 'TensorFlow training started'; python -c 'import tensorflow as tf; print(tf.__version__); print(\"Training completed successfully\")'; echo 'TensorFlow training completed'"},
									Resources: v1.ResourceRequirements{
										Requests: v1.ResourceList{
											v1.ResourceCPU:    resource.MustParse("100m"),
											v1.ResourceMemory: resource.MustParse("256Mi"),
										},
										Limits: v1.ResourceList{
											v1.ResourceCPU:    resource.MustParse("500m"),
											v1.ResourceMemory: resource.MustParse("512Mi"),
										},
									},
								},
							},
						},
					},
				},
			},
		},
	}
}

// createTestPyTorchJob 创建测试用的PyTorchJob
func createTestPyTorchJob(name string) *trainingv1.PyTorchJob {
	return &trainingv1.PyTorchJob{
		ObjectMeta: metav1.ObjectMeta{
			Name:      name,
			Namespace: testNamespace,
			Labels: map[string]string{
				consts.TeamIdLabelKey:            "1",
				consts.TaskExecutionNameLabelKey: name,
				consts.EnvLabelKey:               "test",
			},
		},
		Spec: trainingv1.PyTorchJobSpec{
			PyTorchReplicaSpecs: map[trainingv1.ReplicaType]*trainingv1.ReplicaSpec{
				trainingv1.PyTorchJobReplicaTypeWorker: {
					Replicas: &[]int32{1}[0],
					Template: v1.PodTemplateSpec{
						ObjectMeta: metav1.ObjectMeta{
							Annotations: map[string]string{
								consts.SidecarIstioInjectKey: consts.SidecarIstioInjectValue,
							},
						},
						Spec: v1.PodSpec{
							RestartPolicy: v1.RestartPolicyNever,
							Containers: []v1.Container{
								{
									Name:    "pytorch",
									Image:   "pytorch/pytorch:2.0.1-cuda11.7-cudnn8-runtime",
									Command: []string{"/bin/bash", "-c"},
									Args:    []string{"echo 'PyTorch training started'; python -c 'import torch; print(f\"PyTorch version: {torch.__version__}\"); print(\"Training completed successfully\")'; echo 'PyTorch training completed'"},
									Resources: v1.ResourceRequirements{
										Requests: v1.ResourceList{
											v1.ResourceCPU:    resource.MustParse("100m"),
											v1.ResourceMemory: resource.MustParse("256Mi"),
										},
										Limits: v1.ResourceList{
											v1.ResourceCPU:    resource.MustParse("500m"),
											v1.ResourceMemory: resource.MustParse("512Mi"),
										},
									},
								},
							},
						},
					},
				},
			},
		},
	}
}

// createTestMPIJob 创建测试用的MPIJob
func createTestMPIJob(name string) *trainingv1.MPIJob {
	return &trainingv1.MPIJob{
		ObjectMeta: metav1.ObjectMeta{
			Name:      name,
			Namespace: testNamespace,
			Labels: map[string]string{
				consts.TeamIdLabelKey:            "1",
				consts.TaskExecutionNameLabelKey: name,
				consts.EnvLabelKey:               "test",
			},
		},
		Spec: trainingv1.MPIJobSpec{
			MPIReplicaSpecs: map[trainingv1.ReplicaType]*trainingv1.ReplicaSpec{
				trainingv1.MPIJobReplicaTypeLauncher: {
					Replicas: &[]int32{1}[0],
					Template: v1.PodTemplateSpec{
						ObjectMeta: metav1.ObjectMeta{
							Annotations: map[string]string{
								consts.SidecarIstioInjectKey: consts.SidecarIstioInjectValue,
							},
						},
						Spec: v1.PodSpec{
							RestartPolicy: v1.RestartPolicyNever,
							Containers: []v1.Container{
								{
									Name:    "mpi",
									Image:   "mpioperator/mpi-pi:openmpi",
									Command: []string{"/bin/bash", "-c"},
									Args:    []string{"echo 'MPI training started'; mpirun --allow-run-as-root -np 1 python -c 'print(\"MPI training completed successfully\")'; echo 'MPI training completed'"},
									Resources: v1.ResourceRequirements{
										Requests: v1.ResourceList{
											v1.ResourceCPU:    resource.MustParse("100m"),
											v1.ResourceMemory: resource.MustParse("256Mi"),
										},
										Limits: v1.ResourceList{
											v1.ResourceCPU:    resource.MustParse("500m"),
											v1.ResourceMemory: resource.MustParse("512Mi"),
										},
									},
								},
							},
						},
					},
				},
				trainingv1.MPIJobReplicaTypeWorker: {
					Replicas: &[]int32{1}[0],
					Template: v1.PodTemplateSpec{
						ObjectMeta: metav1.ObjectMeta{
							Annotations: map[string]string{
								consts.SidecarIstioInjectKey: consts.SidecarIstioInjectValue,
							},
						},
						Spec: v1.PodSpec{
							RestartPolicy: v1.RestartPolicyNever,
							Containers: []v1.Container{
								{
									Name:    "mpi",
									Image:   "mpioperator/mpi-pi:openmpi",
									Command: []string{"/bin/bash", "-c"},
									Args:    []string{"echo 'MPI worker started'; sleep 30; echo 'MPI worker completed'"},
									Resources: v1.ResourceRequirements{
										Requests: v1.ResourceList{
											v1.ResourceCPU:    resource.MustParse("100m"),
											v1.ResourceMemory: resource.MustParse("256Mi"),
										},
										Limits: v1.ResourceList{
											v1.ResourceCPU:    resource.MustParse("500m"),
											v1.ResourceMemory: resource.MustParse("512Mi"),
										},
									},
								},
							},
						},
					},
				},
			},
		},
	}
}

// waitForTFJobCompletion 等待TFJob完成
func waitForTFJobCompletion(ctx context.Context, t *gtest.T, jobName string) {
	timeout := time.After(testTimeout)
	ticker := time.NewTicker(10 * time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-timeout:
			t.Fatalf("TFJob %s 超时未完成", jobName)
			return
		case <-ticker.C:
			tfJob, err := client.GetTFJob(ctx, testClusterName, testNamespace, jobName)
			if err != nil {
				t.Logf("获取TFJob状态失败: %v", err)
				continue
			}

			// 检查Job状态
			for _, condition := range tfJob.Status.Conditions {
				if condition.Type == trainingv1.JobSucceeded && condition.Status == v1.ConditionTrue {
					t.Logf("TFJob %s 成功完成", jobName)
					return
				}
				if condition.Type == trainingv1.JobFailed && condition.Status == v1.ConditionTrue {
					t.Fatalf("TFJob %s 执行失败: %s", jobName, condition.Message)
					return
				}
			}
			t.Logf("TFJob %s 仍在运行中...", jobName)
		}
	}
}

// waitForPyTorchJobCompletion 等待PyTorchJob完成
func waitForPyTorchJobCompletion(ctx context.Context, t *gtest.T, jobName string) {
	timeout := time.After(testTimeout)
	ticker := time.NewTicker(10 * time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-timeout:
			t.Fatalf("PyTorchJob %s 超时未完成", jobName)
			return
		case <-ticker.C:
			pyTorchJob, err := client.GetPyTorchJob(ctx, testClusterName, testNamespace, jobName)
			if err != nil {
				t.Logf("获取PyTorchJob状态失败: %v", err)
				continue
			}

			// 检查Job状态
			for _, condition := range pyTorchJob.Status.Conditions {
				if condition.Type == trainingv1.JobSucceeded && condition.Status == v1.ConditionTrue {
					t.Logf("PyTorchJob %s 成功完成", jobName)
					return
				}
				if condition.Type == trainingv1.JobFailed && condition.Status == v1.ConditionTrue {
					t.Fatalf("PyTorchJob %s 执行失败: %s", jobName, condition.Message)
					return
				}
			}
			t.Logf("PyTorchJob %s 仍在运行中...", jobName)
		}
	}
}

// waitForMPIJobCompletion 等待MPIJob完成
func waitForMPIJobCompletion(ctx context.Context, t *gtest.T, jobName string) {
	timeout := time.After(testTimeout)
	ticker := time.NewTicker(10 * time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-timeout:
			t.Fatalf("MPIJob %s 超时未完成", jobName)
			return
		case <-ticker.C:
			mpiJob, err := client.GetMPIJob(ctx, testClusterName, testNamespace, jobName)
			if err != nil {
				t.Logf("获取MPIJob状态失败: %v", err)
				continue
			}

			// 检查Job状态
			for _, condition := range mpiJob.Status.Conditions {
				if condition.Type == trainingv1.JobSucceeded && condition.Status == v1.ConditionTrue {
					t.Logf("MPIJob %s 成功完成", jobName)
					return
				}
				if condition.Type == trainingv1.JobFailed && condition.Status == v1.ConditionTrue {
					t.Fatalf("MPIJob %s 执行失败: %s", jobName, condition.Message)
					return
				}
			}
			t.Logf("MPIJob %s 仍在运行中...", jobName)
		}
	}
}
