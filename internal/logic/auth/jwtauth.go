package auth

import (
	"context"
	"github.com/gogf/gf/v2/util/gconv"
	"gitlab.ttyuyin.com/yunwei-infra/tt-cloud-constack-sdk/pkg/log"
	"gitlab.ttyuyin.com/yunwei-infra/tt-cloud-constack-sdk/pkg/utils/constack/tools"
	"mlops/internal/consts"
	"mlops/internal/model/dto"
	"mlops/internal/service"
	"time"

	jwt "github.com/gogf/gf-jwt/v2"
	"github.com/gogf/gf/v2/frame/g"
)

var JwtMid *jwt.GfJWTMiddleware

func init() {
	auth := jwt.New(&jwt.GfJWTMiddleware{
		Realm:      consts.ServerName,
		Key:        []byte(consts.DefaultCipherKey),
		Timeout:    time.Hour * tools.ConfigGet("app.jwtAuthTimeout").Duration(),
		MaxRefresh: time.Hour * tools.ConfigGet("app.jwtAuthTimeout").Duration(),
		//Timeout:           time.Second * 5,
		//MaxRefresh:        time.Second * 5,
		IdentityKey:       "id",
		TokenLookup:       "header: Authorization, query: token, cookie: jwt",
		TokenHeadName:     "Bearer",
		TimeFunc:          time.Now,
		Authenticator:     Authenticator,
		Unauthorized:      Unauthorized,
		PayloadFunc:       PayloadFunc,
		IdentityHandler:   IdentityHandler,
		SendAuthorization: true,
	})
	JwtMid = auth
	service.RegisterJwtAuth(newsAuthService(JwtMid))
}

type sJwtAuth struct {
	jwtMid *jwt.GfJWTMiddleware
}

func newsAuthService(mid *jwt.GfJWTMiddleware) *sJwtAuth {
	return &sJwtAuth{
		jwtMid: mid,
	}
}

func (this *sJwtAuth) Do() *jwt.GfJWTMiddleware {
	return this.jwtMid
}

// PayloadFunc is a callback function that will be called during login.
// Using this function it is possible to add additional payload data to the webtoken.
// The data is then made available during requests via c.Get("JWT_PAYLOAD").
// Note that the payload is not encrypted.
// The attributes mentioned on jwt.io can't be used as keys for the map.
// Optional, by default no additional data will be set.
func PayloadFunc(data interface{}) jwt.MapClaims {
	claims := jwt.MapClaims{}
	params := data.(map[string]interface{})
	if len(params) > 0 {
		for k, v := range params {
			claims[k] = v
		}
	}
	return claims
}

// IdentityHandler get the identity from JWT and set the identity for every request
// Using this function, by r.GetParam("id") get identity
func IdentityHandler(ctx context.Context) interface{} {
	claims := jwt.ExtractClaims(ctx)
	return claims[JwtMid.IdentityKey]
}

// Unauthorized is used to define customized Unauthorized callback function.
func Unauthorized(ctx context.Context, code int, message string) {
	r := g.RequestFromCtx(ctx)
	r.Response.WriteJson(g.Map{
		"code":    code,
		"message": message,
	})
	r.Response.Status = code
	r.ExitAll()
}

// Authenticator is used to validate login parameters.
// It must return user data as user identifier, it will be stored in Claim Array.
// if your identityKey is 'id', your user data must have 'id'
// Check error (e) to determine the appropriate error message.
func Authenticator(ctx context.Context) (interface{}, error) {
	var (
		r  = g.RequestFromCtx(ctx)
		in dto.UserLoginInput
	)

	ctxVar := r.GetCtxVar(consts.SSO)
	if ctxVar.IsNil() {
		// parse req body
		if err := r.Parse(&in); err != nil {
			return "", err
		}
	} else {
		log.L.WithName("jwtAuth.Authenticator").Infof(ctx, "ctxVal.Val:%+v", ctxVar.Val())
		in = ctxVar.Val().(dto.UserLoginInput)
	}

	user, err := service.User().GetUserWithUserNamePassword(ctx, in.Username, in.Password)
	if err != nil {
		return nil, err
	}

	if user != nil {
		return gconv.Map(user), nil
	}

	return nil, jwt.ErrFailedAuthentication
}
