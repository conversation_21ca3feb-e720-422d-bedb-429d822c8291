package auth

import (
	"context"
	"fmt"
	"mlops/internal/consts"
	"mlops/internal/dao"
	"mlops/internal/model/entity"
	"mlops/internal/service"
	"mlops/tools/errors"
	"strings"

	"github.com/gogf/gf/v2/encoding/gbase64"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/util/grand"
	"gitlab.ttyuyin.com/yunwei-infra/tt-cloud-constack-sdk/pkg/log"
	"gitlab.ttyuyin.com/yunwei-infra/tt-cloud-constack-sdk/pkg/utils/constack/tools"
)

type sOpenApiToken struct {
}

func init() {
	service.RegisterOpenApiToken(newSOpenApiToken())
}

func newSOpenApiToken() *sOpenApiToken {
	return &sOpenApiToken{}
}

// generateToken 生产token
func (this *sOpenApiToken) generateToken(name string) (token string, err error) {
	encodeName := gbase64.Encode([]byte(name))
	digits := grand.Digits(8)
	eData, err := tools.EncryptData(string(encodeName)+digits, consts.DefaultCipherKey)
	if err != nil {
		return token, err
	}
	token = fmt.Sprintf("%s.%s", encodeName, eData)
	return
}

// parseTokenName 通过token解析出token 名字
func (this *sOpenApiToken) parseTokenName(token string) (name string) {
	split := strings.Split(token, ".")
	if len(split) > 0 {
		decode, _ := gbase64.Decode([]byte(split[0]))
		name = string(decode)
	}
	return
}

// CheckAuth check openapi token
func (this *sOpenApiToken) CheckAuth(ctx context.Context, token string) (tokenName string, err error) {
	name := this.parseTokenName(token)
	tokenObj := &entity.OpenapiToken{}
	if err = dao.OpenapiToken.Ctx(ctx).Where("name", name).Scan(tokenObj); err != nil {
		return "", fmt.Errorf("sOpenApiToken.CheckAuth sql execute eror: %s", err.Error())
	}

	if tokenObj.Token != token {
		log.L.WithName("sOpenApiToken.CheckAuth").Warningf(ctx, "sOpenApiToken.CheckAuth %s check openapi token failed.", name)
		err = errors.ValidateError()
	}

	return tokenObj.Name, err
}

// CreateToken create openapi token
func (this *sOpenApiToken) CreateToken(ctx context.Context, name, desc string) (err error) {
	// (base64)name.(encrypt)token
	token, err := this.generateToken(name)
	if err != nil {
		return err
	}

	count, err := dao.OpenapiToken.Ctx(ctx).Where("name", name).Count()
	if err != nil {
		return err
	}
	if count > 0 {
		return fmt.Errorf("当前token已存在")
	}

	_, err = dao.OpenapiToken.Ctx(ctx).Data(g.Map{
		"name": name, "desc": desc, "token": token,
	}).Replace()
	return err
}

func (this *sOpenApiToken) ParseNameWithToken(ctx context.Context, token string) (name string, err error) {
	openapiToken := &entity.OpenapiToken{}

	err = dao.OpenapiToken.Ctx(ctx).Where("token", token).Scan(&openapiToken)
	if err != nil {
		return
	}

	return openapiToken.Name, err
}
