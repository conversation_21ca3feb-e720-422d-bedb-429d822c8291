package auth

import (
	"context"
	"fmt"
	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
	"gitlab.ttyuyin.com/yunwei-infra/tt-cloud-constack-sdk/pkg/log"
	authConsts "mlops/internal/consts/auth"
	"mlops/internal/dao"
	"mlops/internal/model/do"
	"mlops/internal/model/dto"
	"mlops/internal/model/entity"
	"mlops/internal/service"
	"mlops/tools/client"
	"reflect"
	"strings"
)

const (
	ApiFieldName  = "Meta"
	ApiPathPrefix = "path:"
	ApiRolePrefix = "role:"
)

type sPlatFormAuth struct {
	ApiRoleMap map[string][]string // key: uri  value: roles  [admin,consoleAdmin...]
}

/**
  授权: 实际就仨表 + 一个逻辑关联表

  1  user  role  team  user_role_team_rela

    授权 控制台 team默认为 default团队

    工作台授权团队时, team指定team表中团队
*/

func init() {
	service.RegisterPlatFormAuth(newsPlatFormAuth())
}

func newsPlatFormAuth() *sPlatFormAuth {
	return &sPlatFormAuth{ApiRoleMap: make(map[string][]string, 0)}
}

// ApiRegister api 注册
func (this *sPlatFormAuth) ApiRegister(ctx context.Context, apiReq ...interface{}) {
	for _, ar := range apiReq {
		tOf := reflect.TypeOf(ar)
		var elem reflect.Type
		if tOf.Kind() == reflect.Pointer {
			elem = tOf.Elem()
		} else {
			elem = tOf
		}

		for i := 0; i < elem.NumField(); i++ {
			if elem.Field(i).Name == ApiFieldName {
				split := strings.Split(string(elem.Field(i).Tag), " ")

				var currentUri string
				for _, s := range split {
					if strings.HasPrefix(s, ApiPathPrefix) {
						s = strings.TrimLeft(s, ApiPathPrefix)
						currentUri = strings.Trim(s, "\"")
						this.ApiRoleMap[currentUri] = []string{}
						continue
					}

					if strings.HasPrefix(s, ApiRolePrefix) {
						s = strings.TrimLeft(s, ApiRolePrefix)
						s = strings.Trim(s, "\"")
						this.ApiRoleMap[currentUri] = strings.Split(s, ",")
					}
				}
			}
		}
	}
}

func (this *sPlatFormAuth) ShowApiAuthList(ctx context.Context) {
	for uri, roleName := range this.ApiRoleMap {
		log.L.WithName("sPlatFormAuth.ShowApiAuthList").Infof(ctx, "uri: %s, role: %v", uri, roleName)
	}
}

func (this *sPlatFormAuth) GlobalCheck(ctx context.Context, userId int, uri string) bool {
	roles, exits := this.ApiRoleMap[uri]
	if exits {
		if len(roles) == 0 {
			return true
		}
		return this.CheckAuthWithoutTeam(ctx, userId, roles)
	}
	return true
}

func (this *sPlatFormAuth) CheckAuthWithoutTeam(ctx context.Context, userId int, roles []string) bool {
	// 根据当前uid查询是否含有 roles其中一个的权限
	roleIds := make([]int64, 0)
	all, err := dao.Role.Ctx(ctx).WhereIn(dao.Role.Columns().Name, roles).Fields(dao.Role.Columns().Id).All()
	if err != nil {
		return false
	}

	for _, r := range all {
		roleIds = append(roleIds, r.Map()[dao.Role.Columns().Id].(int64))
	}

	count, err := dao.UserRoleTeamRela.Ctx(ctx).Where(dao.UserRoleTeamRela.Columns().UserId, userId).
		WhereIn(dao.UserRoleTeamRela.Columns().RoleId, roleIds).Count()
	if err != nil {
		return false
	}

	if count > 0 {
		return true
	}

	return false
}

func (this *sPlatFormAuth) CheckTeamAuth(ctx context.Context, userId, teamId int, roles []string) bool {
	// 根据当前uid查询是否含有 roles其中一个的权限
	roleIds := make([]int64, 0)
	all, err := dao.Role.Ctx(ctx).WhereIn(dao.Role.Columns().Name, roles).Fields(dao.Role.Columns().Id).All()
	if err != nil {
		log.L.WithName("sPlatFormAuth.CheckTeamAuth").Errorf(ctx, "check team auth error:%s", err.Error())
		return false
	}

	for _, r := range all {
		roleIds = append(roleIds, r.Map()[dao.Role.Columns().Id].(int64))
	}

	teamEntity := &entity.Team{}
	err = dao.Team.Ctx(ctx).Where(do.Team{Id: teamId}).Scan(teamEntity)
	if err != nil {
		log.L.WithName("sPlatFormAuth.CheckTeamAuth").Errorf(ctx, "check team auth error:%s", err.Error())
		return false
	}

	count, err := dao.UserRoleTeamRela.Ctx(ctx).Where(dao.UserRoleTeamRela.Columns().UserId, userId).
		Where(dao.UserRoleTeamRela.Columns().TeamId, teamEntity.TeamId).
		WhereIn(dao.UserRoleTeamRela.Columns().RoleId, roleIds).Count()
	if err != nil {
		log.L.WithName("sPlatFormAuth.CheckTeamAuth").Errorf(ctx, "check team auth error:%s", err.Error())
		return false
	}

	if count > 0 {
		return true
	}

	return false
}

func (this *sPlatFormAuth) BindAdmin(ctx context.Context, uid int) (err error) {
	if uid == 0 {
		return fmt.Errorf("uid is empty")
	}

	adminRole := &entity.Role{}

	err = dao.Role.Ctx(ctx).Where(do.Role{Name: authConsts.RoleAdmin}).Scan(adminRole)
	if err != nil {
		return fmt.Errorf("select admin role error:%s", err.Error())
	}

	_, err = dao.UserRoleTeamRela.Ctx(ctx).Data(do.UserRoleTeamRela{
		UserId: uid,
		RoleId: adminRole.Id,
		TeamId: authConsts.DefaultTeamId,
	}).Save()
	if err != nil {
		return err
	}

	return
}

func (this *sPlatFormAuth) UnBindAdmin(ctx context.Context, uid int) (err error) {
	if uid == 0 {
		return fmt.Errorf("uid is empty")
	}

	adminRole := &entity.Role{}

	err = dao.Role.Ctx(ctx).Where(do.Role{Name: authConsts.RoleAdmin}).Scan(adminRole)
	if err != nil {
		return fmt.Errorf("select admin role error:%s", err.Error())
	}

	_, err = dao.UserRoleTeamRela.Ctx(ctx).Where(do.UserRoleTeamRela{
		UserId: uid,
		RoleId: adminRole.Id,
		TeamId: authConsts.DefaultTeamId,
	}).Delete()
	if err != nil {
		return err
	}
	return
}

func (this *sPlatFormAuth) BindUser(ctx context.Context, uid int) (err error) {
	if uid == 0 {
		return fmt.Errorf("uid is empty")
	}

	userRole := &entity.Role{}

	err = dao.Role.Ctx(ctx).Where(do.Role{Name: authConsts.RoleUser}).Scan(userRole)
	if err != nil {
		return fmt.Errorf("select user role error:%s", err.Error())
	}

	_, err = dao.UserRoleTeamRela.Ctx(ctx).Data(do.UserRoleTeamRela{
		UserId: uid,
		RoleId: userRole.Id,
		TeamId: authConsts.DefaultTeamId,
	}).Save()
	if err != nil {
		return err
	}

	return
}

func (this *sPlatFormAuth) BindKnowledgeBaseAdmin(ctx context.Context, uid int) (err error) {
	if uid == 0 {
		return fmt.Errorf("uid is empty")
	}

	adminRole := &entity.Role{}

	err = dao.Role.Ctx(ctx).Where(do.Role{Name: authConsts.RoleKnowledgeBaseAdmin}).Scan(adminRole)
	if err != nil {
		return fmt.Errorf("select admin role error:%s", err.Error())
	}

	_, err = dao.UserRoleTeamRela.Ctx(ctx).Data(do.UserRoleTeamRela{
		UserId: uid,
		RoleId: adminRole.Id,
		TeamId: authConsts.DefaultTeamId,
	}).Save()
	if err != nil {
		return err
	}
	return
}

func (this *sPlatFormAuth) UnBindKnowledgeBaseAdmin(ctx context.Context, uid int) (err error) {
	if uid == 0 {
		return fmt.Errorf("uid is empty")
	}

	adminRole := &entity.Role{}

	err = dao.Role.Ctx(ctx).Where(do.Role{Name: authConsts.RoleKnowledgeBaseAdmin}).Scan(adminRole)
	if err != nil {
		return fmt.Errorf("select admin role error:%s", err.Error())
	}

	_, err = dao.UserRoleTeamRela.Ctx(ctx).Where(do.UserRoleTeamRela{
		UserId: uid,
		RoleId: adminRole.Id,
		TeamId: authConsts.DefaultTeamId,
	}).Delete()
	if err != nil {
		return err
	}
	return
}

func (this *sPlatFormAuth) BindConsoleAdmin(ctx context.Context, uid int) (err error) {
	if uid == 0 {
		return fmt.Errorf("uid is empty")
	}

	adminRole := &entity.Role{}

	err = dao.Role.Ctx(ctx).Where(do.Role{Name: authConsts.RoleConsoleAdmin}).Scan(adminRole)
	if err != nil {
		return fmt.Errorf("select admin role error:%s", err.Error())
	}

	_, err = dao.UserRoleTeamRela.Ctx(ctx).Data(do.UserRoleTeamRela{
		UserId: uid,
		RoleId: adminRole.Id,
		TeamId: authConsts.DefaultTeamId,
	}).Save()
	if err != nil {
		return err
	}
	return
}

func (this *sPlatFormAuth) UnBindConsoleAdmin(ctx context.Context, uid int) (err error) {
	if uid == 0 {
		return fmt.Errorf("uid is empty")
	}

	adminRole := &entity.Role{}

	err = dao.Role.Ctx(ctx).Where(do.Role{Name: authConsts.RoleConsoleAdmin}).Scan(adminRole)
	if err != nil {
		return fmt.Errorf("select admin role error:%s", err.Error())
	}

	_, err = dao.UserRoleTeamRela.Ctx(ctx).Where(do.UserRoleTeamRela{
		UserId: uid,
		RoleId: adminRole.Id,
		TeamId: authConsts.DefaultTeamId,
	}).Delete()
	if err != nil {
		return err
	}
	return
}

// BindTeamRole 绑定团队角色, 相当于替换权限
func (this *sPlatFormAuth) BindTeamRole(ctx context.Context, uid, teamId int, kind authConsts.TeamRoleKind) (err error) {
	if uid == 0 {
		return fmt.Errorf("uid is empty")
	}

	teamEntity := &entity.Team{}
	err = dao.Team.Ctx(ctx).Where(do.Team{Id: teamId}).Scan(teamEntity)
	if err != nil {
		return err
	}

	teamRole := &entity.Role{}

	teamRoleDao := dao.Role.Ctx(ctx)
	switch kind {
	case authConsts.TeamRoleAdmin:
		teamRoleDao = teamRoleDao.Where(do.Role{Name: authConsts.RoleTeamAdmin})
	case authConsts.TeamRoleUser:
		teamRoleDao = teamRoleDao.Where(do.Role{Name: authConsts.RoleUser})
	}

	err = teamRoleDao.Scan(teamRole)
	if err != nil {
		return fmt.Errorf("select team role error:%s", err.Error())
	}

	count, err := dao.UserRoleTeamRela.Ctx(ctx).Where(do.UserRoleTeamRela{
		UserId: uid,
		TeamId: teamEntity.TeamId,
	}).Count()
	if err != nil {
		return err
	}

	data := do.UserRoleTeamRela{
		UserId: uid,
		RoleId: teamRole.Id,
		TeamId: teamEntity.TeamId,
	}
	urtRDao := dao.UserRoleTeamRela.Ctx(ctx)
	if count != 0 {
		// update
		_, err = urtRDao.Where(do.UserRoleTeamRela{
			UserId: uid,
			TeamId: teamEntity.TeamId,
		}).Data(data).Update()
		if err != nil {
			return err
		}
		return
	}

	_, err = urtRDao.Data(data).Save()
	if err != nil {
		return err
	}
	return
}

func (this *sPlatFormAuth) UnBindTeamRole(ctx context.Context, uid, teamId int, kind authConsts.TeamRoleKind) (err error) {
	if uid == 0 {
		return fmt.Errorf("uid is empty")
	}
	teamEntity := &entity.Team{}
	err = dao.Team.Ctx(ctx).Where(do.Team{Id: teamId}).Scan(teamEntity)
	if err != nil {
		return err
	}

	teamRole := &entity.Role{}

	roleDao := dao.Role.Ctx(ctx)
	switch kind {
	case authConsts.TeamRoleAdmin:
		roleDao = roleDao.Where(do.Role{Name: authConsts.RoleTeamAdmin})
	case authConsts.TeamRoleUser:
		roleDao = roleDao.Where(do.Role{Name: authConsts.RoleUser})
	}

	err = roleDao.Scan(teamRole)
	if err != nil {
		return fmt.Errorf("select team role error:%s", err.Error())
	}

	_, err = dao.UserRoleTeamRela.Ctx(ctx).Where(do.UserRoleTeamRela{
		UserId: uid,
		RoleId: teamRole.Id,
		TeamId: teamEntity.TeamId,
	}).Delete()
	if err != nil {
		return err
	}
	return
}

func (this *sPlatFormAuth) GetUserAuthProfile(ctx context.Context, uid int) (authProfile *dto.AuthProfile, err error) {
	authProfile = &dto.AuthProfile{
		Roles:     make([]authConsts.RoleKind, 0),
		TeamRoles: make([]dto.TeamRole, 0),
	}
	db, err := gdb.Instance()
	if err != nil {
		return nil, err
	}

	sql := `
SELECT
  u.id AS userId,
  u.username,
  u.email,
  r.name role_name,
  t.name team_name ,
  t.team_id team_id ,
  t.id team_pid ,
  t.category team_type
FROM
  tt_user u
  LEFT JOIN tt_user_role_team_rela urt ON u.id = urt.user_id
  LEFT JOIN tt_team t ON urt.team_id = t.team_id
  LEFT JOIN tt_role r ON urt.role_id = r.id 
WHERE u.id = ?

`

	resultDto := make([]*dto.GetUserAuthProfileDbResult, 0, 256)

	all, err := db.Ctx(ctx).GetAll(ctx, sql, uid)
	if err != nil {
		return nil, err
	}

	err = all.Structs(&resultDto)
	if err != nil {
		return nil, err
	}

	for _, result := range resultDto {
		// 全局角色
		if result.TeamName == "" && result.RoleName != "" {
			authProfile.Roles = append(authProfile.Roles, result.RoleName)
			continue
		}

		switch result.RoleName {
		case authConsts.RoleTeamAdmin:
			authProfile.TeamRoles = append(authProfile.TeamRoles, dto.TeamRole{
				Id:       result.TeamPid,
				Role:     authConsts.TeamRoleAdmin,
				TeamId:   result.TeamId,
				TeamName: result.TeamName,
				TeamType: result.TeamType,
			})
		case authConsts.RoleUser:
			authProfile.TeamRoles = append(authProfile.TeamRoles, dto.TeamRole{
				Id:       result.TeamPid,
				Role:     authConsts.TeamRoleUser,
				TeamId:   result.TeamId,
				TeamName: result.TeamName,
				TeamType: result.TeamType,
			})
		}
	}

	return
}

func (this *sPlatFormAuth) SyncUserTeam(ctx context.Context, uid int) (err error) {
	err = this.syncFeatureTeam(ctx, uid)
	if err != nil {
		log.L.WithName("sPlatFormAuth.SyncUserTeam").Errorf(ctx, "syncFeatureTeam uid: %d,error:%s", uid, err.Error())
	}

	// 禁止同步组织架构团队
	//err = this.syncOrganizationTeam(ctx, uid)
	//if err != nil {
	//	log.L.WithName("sPlatFormAuth.SyncUserTeam").Errorf(ctx, "syncOrganizationTeam uid: %d,error:%s", uid, err.Error())
	//}

	err = this.BindUser(ctx, uid)
	if err != nil {
		log.L.WithName("sPlatFormAuth.SyncUserTeam").Errorf(ctx, "bind default user uid: %d,error:%s", uid, err.Error())
	}
	return nil
}

func (this *sPlatFormAuth) syncFeatureTeam(ctx context.Context, uid int) (err error) {
	userEntity, err := service.User().GetUserById(ctx, uid)
	if err != nil {
		return err
	}

	projects, err := client.CicdAppProject.ListUserProject(ctx, userEntity.Email)
	if err != nil {
		return err
	}

	roleList := make([]*entity.Role, 0, 4)
	roleMap := make(map[string]*entity.Role) // key: role name

	currentUserRoleTeamRelaList := make([]*entity.UserRoleTeamRela, 0, 16)
	currentUserRoleTeamRelaMap := make(map[string]*entity.UserRoleTeamRela) // key: team_id
	err = dao.UserRoleTeamRela.Ctx(ctx).Where(dao.UserRoleTeamRela.Columns().UserId, uid).Scan(&currentUserRoleTeamRelaList)
	if err != nil {
		return err
	}

	for _, rela := range currentUserRoleTeamRelaList {
		currentUserRoleTeamRelaMap[rela.TeamId] = rela
	}

	err = dao.Role.Ctx(ctx).Scan(&roleList)
	if err != nil {
		return err
	}

	for _, role := range roleList {
		roleMap[role.Name] = role
	}

	userRoleTeamRelas := make([]*entity.UserRoleTeamRela, 0, 16)

	for _, project := range projects {
		teamEntity := &entity.Team{}

		err = dao.Team.Ctx(ctx).Where(do.Team{TeamId: project.Id}).Scan(teamEntity)
		if err != nil {
			continue
		}

		userRoleTeamRela := &entity.UserRoleTeamRela{
			UserId: uid,
			TeamId: teamEntity.TeamId,
		}

		if value, ok := currentUserRoleTeamRelaMap[fmt.Sprintf("%d", project.Id)]; ok {
			// 存在当前团队数据, 如果是cicdDevAdmin 则提权, 否则跳过
			if project.Role == authConsts.CicdDevAdmin {
				// 自动晋升teamAdmin
				_, err = dao.UserRoleTeamRela.Ctx(ctx).Data().Where(g.Map{
					dao.UserRoleTeamRela.Columns().UserId: userEntity.Id,
					dao.UserRoleTeamRela.Columns().TeamId: value.TeamId,
				}).Update(do.UserRoleTeamRela{
					UserId: userEntity.Id,
					RoleId: int(roleMap[string(authConsts.RoleTeamAdmin)].Id),
					TeamId: value.TeamId,
				})
				if err != nil {
					continue
				}
			}

			continue
		}

		if project.Role == authConsts.CicdDevAdmin {
			// 自动授权teamAdmin
			userRoleTeamRela.RoleId = int(roleMap[string(authConsts.RoleTeamAdmin)].Id)
		} else {
			userRoleTeamRela.RoleId = int(roleMap[string(authConsts.RoleUser)].Id)
		}

		userRoleTeamRelas = append(userRoleTeamRelas, userRoleTeamRela)
	}

	if len(userRoleTeamRelas) != 0 {
		_, err = dao.UserRoleTeamRela.Ctx(ctx).Data(userRoleTeamRelas).Batch(50).Save()
		if err != nil {
			return err
		}
	}
	return
}

func (this *sPlatFormAuth) syncOrganizationTeam(ctx context.Context, uid int) (err error) {
	pid, err := service.Team().SyncTeamOrganization(ctx, uid)
	if err != nil {
		return err
	}

	// 绑定权限
	err = this.BindTeamRole(ctx, uid, int(pid), authConsts.TeamRoleUser)
	if err != nil {
		return err
	}

	return
}

func (this *sPlatFormAuth) CheckAdmin(ctx context.Context, userId int) bool {
	return this.CheckAuthWithoutTeam(ctx, userId, []string{string(authConsts.RoleAdmin)})
}
