package bizctx

import (
	"context"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/net/ghttp"
	"mlops/internal/consts"
	"mlops/internal/model/dto"
	"mlops/internal/service"
)

type sBizCtx struct{}

func newsBizCtx() *sBizCtx {
	return &sBizCtx{}
}

func init() {
	service.RegisterBizCtx(newsBizCtx())
}

// Init 初始化上下文对象指针到上下文对象中，以便后续的请求流程中可以修改。
func (this *sBizCtx) Init(r *ghttp.Request, customCtx *dto.Context) {
	r.SetCtxVar(consts.ContextKey, customCtx)
}

// Get 获得上下文变量，如果没有设置，那么返回nil
func (this *sBizCtx) Get(ctx context.Context) *dto.Context {
	value := ctx.Value(consts.ContextKey)
	if value == nil {
		return nil
	}
	if localCtx, ok := value.(*dto.Context); ok {
		return localCtx
	}
	return nil
}

// SetUser 将上下文信息设置到上下文请求中，注意是完整覆盖
func (this *sBizCtx) SetUser(ctx context.Context, ctxUser *dto.ContextUser) {
	this.Get(ctx).User = ctxUser
}

// SetData 将上下文信息设置到上下文请求中，注意是完整覆盖
func (this *sBizCtx) SetData(ctx context.Context, data g.Map) {
	this.Get(ctx).Data = data
}
