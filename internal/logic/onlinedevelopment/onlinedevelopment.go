package onlinedevelopment

import (
	"context"
	"database/sql"
	"encoding/json"
	"errors"
	"fmt"
	"mlops/internal/consts"
	errors2 "mlops/internal/consts/errors"
	"mlops/internal/dao"
	"mlops/internal/model/dto"
	"mlops/internal/model/entity"
	"mlops/internal/service"
	"mlops/tools/client"
	"mlops/tools/gpu"
	"mlops/utility/kueue"
	"os"
	"strconv"
	"strings"

	"github.com/gogf/gf/frame/g"
	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/util/grand"
	"gitlab.ttyuyin.com/yunwei-infra/tt-cloud-constack-sdk/pkg/log"
	constack_openapi "gitlab.ttyuyin.com/yunwei-infra/tt-cloud-constack-sdk/pkg/resources/constack/api.v1/openapi"
	appsv1 "k8s.io/api/apps/v1"
	v1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/api/resource"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/util/intstr"
)

const (
	VolumeMountName = "vol-%d"
	GpuCoreKey      = "52tt.com/gpu-core"
	GpuMemoryKey    = "52tt.com/gpu-memory"
	ExecutionName   = "online-dev-%s-%d" // type + devId

	ManagedByAnnotationKey   = "managed-by"
	ManagedByAnnotationValue = "kuberay-dynamic-route"

	OnlineDevelopmentTypeAnnotationKey = "online-dev-type"
)

type sOnlineDevelopment struct {
}

func init() {
	service.RegisterOnlineDevelopment(newsOnlineDevelopment())
}

func newsOnlineDevelopment() *sOnlineDevelopment {
	return &sOnlineDevelopment{}
}

func (this *sOnlineDevelopment) Table() string {
	return dao.OnlineDevelopment.Table()
}

func (this *sOnlineDevelopment) ListPage(ctx context.Context, in dto.OnlineDevelopmentListInput) (
	pageData *dto.OnlineDevelopmentListOutput, err error) {
	list := make([]*dto.OnlineDevelopment, 0)
	q := dao.OnlineDevelopment.Ctx(ctx)
	if in.Pid != 0 {
		q = q.Where(dao.OnlineDevelopment.Columns().Id, in.Pid)
	}
	if in.DevName != "" {
		q = q.WhereLike(dao.OnlineDevelopment.Columns().DevName, "%"+in.DevName+"%")
	}
	if in.OnlyMy {
		user := service.BizCtx().Get(ctx).User
		q = q.Where(dao.OnlineDevelopment.Columns().CreatedByEmployeeNo, user.EmployeeNo)
	}
	if in.TeamId != 0 {
		q = q.Where(dao.OnlineDevelopment.Columns().TeamId, in.TeamId)
	}
	if in.CreatedByUserName != "" {
		q = q.Where(dao.OnlineDevelopment.Columns().CreatedByUserName, in.CreatedByUserName)
	}
	if in.CreatedByEmployeeNo != "" {
		q = q.Where(dao.OnlineDevelopment.Columns().CreatedByEmployeeNo, in.CreatedByEmployeeNo)
	}
	if in.UpdatedByUserName != "" {
		q = q.Where(dao.OnlineDevelopment.Columns().UpdatedByUserName, in.UpdatedByUserName)
	}
	if in.UpdatedByEmployeeNo != "" {
		q = q.Where(dao.OnlineDevelopment.Columns().UpdatedByEmployeeNo, in.UpdatedByEmployeeNo)
	}
	if len(in.CreatedAt) > 0 {
		q = q.WhereBetween(dao.OnlineDevelopment.Columns().CreatedAt, in.CreatedAt[0], in.CreatedAt[1])
	}
	if len(in.UpdatedAt) > 0 {
		q = q.WhereBetween(dao.OnlineDevelopment.Columns().UpdatedAt, in.UpdatedAt[0], in.UpdatedAt[1])
	}
	total, err := q.Count()
	if err != nil {
		return nil, err
	}
	err = q.OrderDesc(dao.OnlineDevelopment.Columns().Id).Page(in.Page, in.PageSize).Scan(&list)
	if err != nil {
		return nil, err
	}
	return &dto.OnlineDevelopmentListOutput{
		List:        list,
		Total:       total,
		CurrentPage: in.Page,
		PageSize:    in.PageSize,
	}, nil
}

func (this *sOnlineDevelopment) Get(ctx context.Context, id uint) (onlineDevelopment *dto.OnlineDevelopment, err error) {
	onlineDevelopment = &dto.OnlineDevelopment{}
	err = dao.OnlineDevelopment.Ctx(ctx).Where(dao.OnlineDevelopment.Columns().Id, id).Scan(onlineDevelopment)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, errors2.ErrTaskNotFound
		}
		return nil, err
	}

	onlineDevelopment.ClusterResource = dto.OnlineDevelopmentClusterResource{}
	onlineDevelopment.VolumeMounts = []*dto.OnlineDevelopmentVolumeMount{}
	err = dao.OnlineDevelopmentClusterResource.Ctx(ctx).
		Where(dao.OnlineDevelopmentClusterResource.Columns().DevId, id).Scan(&onlineDevelopment.ClusterResource)
	if err != nil {
		return nil, err
	}
	err = dao.OnlineDevelopmentVolumeMount.Ctx(ctx).
		Where(dao.OnlineDevelopmentVolumeMount.Columns().DevId, id).Scan(&onlineDevelopment.VolumeMounts)
	if err != nil {
		return nil, err
	}
	return
}

func (this *sOnlineDevelopment) Create(ctx context.Context, onlineDevelopment *dto.OnlineDevelopment) (err error) {
	user := service.BizCtx().Get(ctx).User
	onlineDevelopment.CreatedByUserName = user.NickName
	onlineDevelopment.CreatedByEmployeeNo = user.EmployeeNo
	onlineDevelopmentEntity := toOnlineDevelopmentEntity(onlineDevelopment)
	if onlineDevelopmentEntity == nil {
		return fmt.Errorf("invalid train task")
	}
	err = dao.OnlineDevelopment.Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		team := &dto.Team{}
		err = tx.Model(dao.Team.Table()).Where(dao.Team.Columns().Id, onlineDevelopment.TeamId).Scan(team)
		if err != nil {
			return err
		}
		onlineDevelopmentEntity.TeamName = team.Name

		// 随机生成密码
		onlineDevelopmentEntity.Password = grand.S(8)

		id, err := tx.Model(dao.OnlineDevelopment.Table()).Data(onlineDevelopmentEntity).InsertAndGetId()
		if err != nil {
			return err
		}

		onlineDevelopment.ClusterResource.DevId = uint(id)
		_, err = tx.Model(dao.OnlineDevelopmentClusterResource.Table()).
			Where(dao.OnlineDevelopmentClusterResource.Columns().DevId, onlineDevelopment.Id).
			Data(onlineDevelopment.ClusterResource).Insert()
		if err != nil {
			return err
		}
		for i, volumeMount := range onlineDevelopment.VolumeMounts {
			volumeMount.DevId = uint(id)
			volumeMount.Name = fmt.Sprintf(VolumeMountName, i)
		}

		if len(onlineDevelopment.VolumeMounts) > 0 {
			_, err = tx.Model(dao.OnlineDevelopmentVolumeMount.Table()).
				Where(dao.OnlineDevelopmentVolumeMount.Columns().DevId, onlineDevelopment.Id).
				Data(onlineDevelopment.VolumeMounts).Insert()
			if err != nil {
				return err
			}
			return nil
		}
		return nil
	})
	return err
}

func (this *sOnlineDevelopment) Update(ctx context.Context, onlineDevelopment *dto.OnlineDevelopment) (err error) {
	user := service.BizCtx().Get(ctx).User
	onlineDevelopment.UpdatedByUserName = user.NickName
	onlineDevelopment.UpdatedByEmployeeNo = user.EmployeeNo
	onlineDevelopmentEntity := toOnlineDevelopmentEntity(onlineDevelopment)
	if onlineDevelopmentEntity == nil {
		return fmt.Errorf("invalid train task")
	}
	err = dao.OnlineDevelopment.Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		result, err := tx.Model(dao.OnlineDevelopment.Table()).
			Where(dao.OnlineDevelopment.Columns().Id, onlineDevelopment.Id).Data(onlineDevelopmentEntity).
			FieldsEx(dao.OnlineDevelopment.Columns().CreatedAt,
				dao.OnlineDevelopment.Columns().CreatedByUserName,
				dao.OnlineDevelopment.Columns().CreatedByEmployeeNo,
				dao.OnlineDevelopment.Columns().TeamId,
				dao.OnlineDevelopment.Columns().TeamName,
				dao.OnlineDevelopment.Columns().Status,
				dao.OnlineDevelopment.Columns().Password,
				dao.OnlineDevelopment.Columns().DevUrl).Update()
		if err != nil {
			return err
		}
		if cnt, err := result.RowsAffected(); err != nil || cnt == 0 {
			return fmt.Errorf("train task not found")
		}

		_, err = tx.Model(dao.OnlineDevelopmentClusterResource.Table()).
			Where(dao.OnlineDevelopmentClusterResource.Columns().DevId, onlineDevelopment.Id).Delete()
		if err != nil {
			return err
		}
		_, err = tx.Model(dao.OnlineDevelopmentVolumeMount.Table()).
			Where(dao.OnlineDevelopmentVolumeMount.Columns().DevId, onlineDevelopment.Id).Delete()
		if err != nil {
			return err
		}
		onlineDevelopment.ClusterResource.DevId = onlineDevelopment.Id
		_, err = tx.Model(dao.OnlineDevelopmentClusterResource.Table()).
			Where(dao.OnlineDevelopmentClusterResource.Columns().DevId, onlineDevelopment.Id).
			Data(onlineDevelopment.ClusterResource).Insert()
		if err != nil {
			return err
		}
		for i, volumeMount := range onlineDevelopment.VolumeMounts {
			volumeMount.DevId = onlineDevelopment.Id
			volumeMount.Name = fmt.Sprintf(VolumeMountName, i)
		}
		if len(onlineDevelopment.VolumeMounts) > 0 {
			_, err = tx.Model(dao.OnlineDevelopmentVolumeMount.Table()).
				Where(dao.OnlineDevelopmentVolumeMount.Columns().DevId, onlineDevelopment.Id).Data(onlineDevelopment.VolumeMounts).Insert()
			if err != nil {
				return err
			}
		}
		return nil
	})
	return err
}

func (this *sOnlineDevelopment) Delete(ctx context.Context, id uint) (err error) {
	err = dao.OnlineDevelopment.Transaction(ctx, func(ctx context.Context, tx gdb.TX) error {
		_, err = tx.Model(dao.OnlineDevelopment.Table()).Where(dao.OnlineDevelopment.Columns().Id, id).Delete()
		if err != nil {
			return err
		}
		_, err = tx.Model(dao.OnlineDevelopmentClusterResource.Table()).
			Where(dao.OnlineDevelopmentClusterResource.Columns().DevId, id).Delete()
		if err != nil {
			return err
		}
		_, err = tx.Model(dao.OnlineDevelopmentVolumeMount.Table()).
			Where(dao.OnlineDevelopmentVolumeMount.Columns().DevId, id).Delete()
		if err != nil {
			return err
		}
		return nil
	})
	return
}

func (this *sOnlineDevelopment) Start(ctx context.Context, id uint) (err error) {
	development := &entity.OnlineDevelopment{}
	err = dao.OnlineDevelopment.Ctx(ctx).Where(dao.OnlineDevelopment.Columns().Id, id).Scan(development)
	if err != nil {
		return err
	}
	clusterResource := &entity.OnlineDevelopmentClusterResource{}
	err = dao.OnlineDevelopmentClusterResource.Ctx(ctx).
		Where(dao.OnlineDevelopmentClusterResource.Columns().DevId, id).Scan(clusterResource)
	if err != nil {
		return err
	}
	volumeMounts := []*entity.OnlineDevelopmentVolumeMount{}
	err = dao.OnlineDevelopmentVolumeMount.Ctx(ctx).
		Where(dao.OnlineDevelopmentVolumeMount.Columns().DevId, id).Scan(&volumeMounts)
	if err != nil {
		return err
	}

	if development.Password == "" {
		development.Password = grand.S(8)
	}

	executionName := fmt.Sprintf(ExecutionName, development.Type, development.Id)

	// check if deployment exists
	deploy, err := client.GetDeployment(ctx, development.ClusterName, development.Namespace, executionName)
	if err != nil {
		log.L.WithName("sOnlineDevelopment.Start").Warningf(ctx, "get deployment error:%s", err.Error())
	}

	if deploy != nil && deploy.Status.AvailableReplicas == 1 {
		_, err = dao.OnlineDevelopment.Ctx(ctx).Where(dao.OnlineDevelopment.Columns().Id, id).Data(g.Map{
			dao.OnlineDevelopment.Columns().Status: consts.OnlineDevelopmentStatusRunning,
		}).Update()
		if err != nil {
			log.L.WithName("sOnlineDevelopment.Start").Errorf(ctx, "update status error:%s", err.Error())
			return err
		}
		return nil
	}

	// 检查 Kueue 资源
	err = checkKueueResources(ctx, development, clusterResource)
	if err != nil {
		return err
	}

	deployment := toDeployment(executionName, development, clusterResource, volumeMounts)
	// 设置 Kueue 标签
	if deployment.Labels == nil {
		deployment.Labels = make(map[string]string)
	}
	// 队列名标签，有则覆盖，无则添加
	deployment.Labels[kueue.QueueNameLabel] = kueue.LocalQueuePrefix + strconv.Itoa(int(development.TeamId)) + "-" + strconv.Itoa(int(kueue.OnlineDevelopmentIndex))
	// 优先级标签，有则跳过，无则添加
	if _, exists := deployment.Labels[kueue.PriorityClassLabel]; !exists {
		deployment.Labels[kueue.PriorityClassLabel] = kueue.CohortName + "-p0"
	}

	err = client.CreateDeployment(ctx, development.ClusterName, deployment)
	if err != nil {
		return err
	}

	svc := toService(executionName, development)
	err = client.CreateService(ctx, development.ClusterName, svc)
	if err != nil {
		return err
	}

	devUrl := fmt.Sprintf("https://ray.ttyuyin.com:8000/%s/", executionName)
	if strings.Contains(development.ClusterName, "hs") {
		devUrl = fmt.Sprintf("https://ray-hs.ttyuyin.com:8000/%s/", executionName)
	}

	// 生成dev_url
	_, err = dao.OnlineDevelopment.Ctx(ctx).Where(dao.OnlineDevelopment.Columns().Id, id).Data(g.Map{
		dao.OnlineDevelopment.Columns().DevUrl:   devUrl,
		dao.OnlineDevelopment.Columns().Status:   consts.OnlineDevelopmentStatusPending,
		dao.OnlineDevelopment.Columns().Password: development.Password,
	}).Update()
	if err != nil {
		log.L.WithName("sOnlineDevelopment.Start").Errorf(ctx, "update dev_url error:%s", err.Error())
		return err
	}

	return nil
}

func (this *sOnlineDevelopment) Stop(ctx context.Context, id uint) (err error) {
	development := &entity.OnlineDevelopment{}
	err = dao.OnlineDevelopment.Ctx(ctx).Where(dao.OnlineDevelopment.Columns().Id, id).Scan(development)
	if err != nil {
		return err
	}
	executionName := fmt.Sprintf(ExecutionName, development.Type, development.Id)
	err = client.DeleteDeployment(ctx, development.ClusterName, development.Namespace, executionName)
	if err != nil {
		return err
	}
	err = client.DeleteService(ctx, development.ClusterName, development.Namespace, executionName)
	if err != nil {
		return err
	}

	// 将url置空
	_, err = dao.OnlineDevelopment.Ctx(ctx).Where(dao.OnlineDevelopment.Columns().Id, id).Data(g.Map{
		dao.OnlineDevelopment.Columns().DevUrl: "",
		dao.OnlineDevelopment.Columns().Status: consts.OnlineDevelopmentStatusShutdown,
	}).Update()
	if err != nil {
		log.L.WithName("sOnlineDevelopment.Stop").Errorf(ctx, "update dev_url error:%s", err.Error())
		return err
	}

	return nil
}

func (this *sOnlineDevelopment) SyncStatus(ctx context.Context) (err error) {
	// 从表中查到所有数据
	developments := make([]*entity.OnlineDevelopment, 0)
	err = dao.OnlineDevelopment.Ctx(ctx).WhereIn(dao.OnlineDevelopment.Columns().Status,
		[]string{consts.OnlineDevelopmentStatusRunning, consts.OnlineDevelopmentStatusPending, consts.OnlineDevelopmentStatusShutdown}).
		Scan(&developments)
	if err != nil {
		return err
	}

	// 准备批量更新的数据
	shutdownIds := make([]uint, 0)
	runningIds := make([]uint, 0)
	pendingIds := make([]uint, 0)

	for _, development := range developments {
		executionName := fmt.Sprintf(ExecutionName, development.Type, development.Id)
		deployment, err := client.GetDeployment(ctx, development.ClusterName, development.Namespace, executionName)
		if err != nil {
			log.L.WithName("sOnlineDevelopment.SyncStatus").Warningf(ctx, "get deployment error:%s", err.Error())
			// 报错就shutdown
			shutdownIds = append(shutdownIds, development.Id)
			continue
		}

		switch deployment.Status.AvailableReplicas {
		case 0:
			pendingIds = append(pendingIds, development.Id)
		case 1:
			runningIds = append(runningIds, development.Id)
		}
	}

	// 批量更新状态为shutdown的记录
	if len(shutdownIds) > 0 {
		_, err = dao.OnlineDevelopment.Ctx(ctx).
			WhereIn(dao.OnlineDevelopment.Columns().Id, shutdownIds).
			Data(g.Map{dao.OnlineDevelopment.Columns().Status: consts.OnlineDevelopmentStatusShutdown}).
			Update()
		if err != nil {
			log.L.WithName("sOnlineDevelopment.SyncStatus").Errorf(ctx, "batch update shutdown status error:%s", err.Error())
		}
	}

	// 批量更新状态为running的记录
	if len(runningIds) > 0 {
		_, err = dao.OnlineDevelopment.Ctx(ctx).
			WhereIn(dao.OnlineDevelopment.Columns().Id, runningIds).
			Data(g.Map{dao.OnlineDevelopment.Columns().Status: consts.OnlineDevelopmentStatusRunning}).
			Update()
		if err != nil {
			log.L.WithName("sOnlineDevelopment.SyncStatus").Errorf(ctx, "batch update running status error:%s", err.Error())
		}
	}

	// 批量更新状态为pending的记录
	if len(pendingIds) > 0 {
		_, err = dao.OnlineDevelopment.Ctx(ctx).
			WhereIn(dao.OnlineDevelopment.Columns().Id, pendingIds).
			Data(g.Map{dao.OnlineDevelopment.Columns().Status: consts.OnlineDevelopmentStatusPending}).
			Update()
		if err != nil {
			log.L.WithName("sOnlineDevelopment.SyncStatus").Errorf(ctx, "batch update pending status error:%s", err.Error())
		}
	}

	return nil
}

func toOnlineDevelopmentEntity(development *dto.OnlineDevelopment) *entity.OnlineDevelopment {
	var env string
	marshal, _ := json.Marshal(development.Env)
	env = string(marshal)
	return &entity.OnlineDevelopment{
		Id:                  development.Id,
		DevName:             development.DevName,
		TeamId:              development.TeamId,
		TeamName:            development.TeamName,
		ClusterName:         development.ClusterName,
		ClusterId:           development.ClusterId,
		Namespace:           development.Namespace,
		ImageUrl:            development.ImageUrl,
		Type:                development.Type,
		CreatedByEmployeeNo: development.CreatedByEmployeeNo,
		CreatedByUserName:   development.CreatedByUserName,
		UpdatedByEmployeeNo: development.UpdatedByEmployeeNo,
		UpdatedByUserName:   development.UpdatedByUserName,
		Env:                 env,
		AppName:             development.AppName,
		CmdbId:              development.CmdbId,
	}
}

func toDeployment(executionName string, development *entity.OnlineDevelopment,
	clusterResource *entity.OnlineDevelopmentClusterResource,
	volumeMounts []*entity.OnlineDevelopmentVolumeMount) (deployment *appsv1.Deployment) {
	mounts := []v1.VolumeMount{}
	volumes := []v1.Volume{}
	for _, volumeMount := range volumeMounts {
		mounts = append(mounts, v1.VolumeMount{
			Name:      volumeMount.Name,
			MountPath: volumeMount.MountPath,
			SubPath:   volumeMount.SubPath,
		})
		if volumeMount.VolumeType == "pvc" {
			volumes = append(volumes, v1.Volume{
				Name: volumeMount.Name,
				VolumeSource: v1.VolumeSource{
					PersistentVolumeClaim: &v1.PersistentVolumeClaimVolumeSource{
						ClaimName: volumeMount.VolumeName,
					},
				},
			})
		}
		if volumeMount.VolumeType == "secret" {
			volumes = append(volumes, v1.Volume{
				Name: volumeMount.Name,
				VolumeSource: v1.VolumeSource{
					Secret: &v1.SecretVolumeSource{
						SecretName: volumeMount.VolumeName,
					},
				},
			})
		}
		if volumeMount.VolumeType == "configMap" {
			volumes = append(volumes, v1.Volume{
				Name: volumeMount.Name,
				VolumeSource: v1.VolumeSource{
					ConfigMap: &v1.ConfigMapVolumeSource{
						LocalObjectReference: v1.LocalObjectReference{
							Name: volumeMount.VolumeName,
						},
					},
				},
			})
		}
	}

	envs := make(map[string]string)
	err := json.Unmarshal([]byte(development.Env), &envs)
	if err != nil {
		log.L.WithName("toDeployment").Errorf(context.TODO(), "unmarshal env error:%s", err.Error())
		return nil
	}

	deployment = &appsv1.Deployment{
		TypeMeta: metav1.TypeMeta{
			Kind:       "Deployment",
			APIVersion: "apps/v1",
		},
	}
	deployment.Name = executionName
	deployment.Namespace = development.Namespace
	deployment.Annotations = map[string]string{
		ManagedByAnnotationKey:             ManagedByAnnotationValue,
		OnlineDevelopmentTypeAnnotationKey: development.Type,
	}
	if deployment.Spec.Selector == nil {
		deployment.Spec.Selector = &metav1.LabelSelector{}
	}
	deployment.Spec.Selector.MatchLabels = map[string]string{"app": executionName}
	deployment.Spec.Template.Labels = map[string]string{"app": executionName}
	deployment.Spec.Template.Annotations = map[string]string{
		ManagedByAnnotationKey:             ManagedByAnnotationValue,
		OnlineDevelopmentTypeAnnotationKey: development.Type,
	}

	var envVar []v1.EnvVar
	for key, val := range envs {
		envVar = append(envVar, v1.EnvVar{
			Name:  key,
			Value: val,
		})
	}
	envVar = append(envVar, v1.EnvVar{
		Name:  "PASSWORD",
		Value: development.Password,
	})
	envVar = append(envVar, v1.EnvVar{
		Name:  "JUPYTER_PORT",
		Value: "8888",
	})
	envVar = append(envVar, v1.EnvVar{
		Name:  "JUPYTER_TOKEN",
		Value: development.Password,
	})
	envVar = append(envVar, v1.EnvVar{
		Name:  "BASE_URL",
		Value: fmt.Sprintf("/%s", executionName),
	})
	envVar = append(envVar, v1.EnvVar{
		Name:  "JUPYTER_BASE_URL",
		Value: fmt.Sprintf("/%s", executionName),
	})
	envVar = append(envVar, v1.EnvVar{
		Name:  "NOTEBOOK_ARGS",
		Value: fmt.Sprintf("--NotebookApp.base_url=/%s --ServerApp.base_url=/%s", executionName, executionName),
	})

	// parse resources
	resourceRequestMap, resourceLimitMap := parseGpuResource(development.ClusterName, clusterResource)

	// 计算shm大小（申请内存的一半）
	var shmSize resource.Quantity
	if memoryRequest, exists := resourceRequestMap[v1.ResourceMemory]; exists {
		// 获取内存大小（以字节为单位）
		memoryBytes := memoryRequest.Value()
		// 计算一半大小
		shmBytes := memoryBytes / 2
		shmSize = *resource.NewQuantity(shmBytes, resource.BinarySI)
	} else {
		// 如果没有设置内存请求，使用默认值64Mi
		shmSize = resource.MustParse("64Mi")
	}

	// 添加shm volume mount
	shmVolumeMount := v1.VolumeMount{
		Name:      "dshm",
		MountPath: "/dev/shm",
	}
	mounts = append(mounts, shmVolumeMount)

	deployment.Spec.Template.Spec.Containers = []v1.Container{
		{
			Name:  development.Type,
			Image: development.ImageUrl,
			Env:   envVar,
			Resources: v1.ResourceRequirements{
				Requests: resourceRequestMap,
				Limits:   resourceLimitMap,
			},
			VolumeMounts: mounts,
		},
	}

	deployment.Spec.Template.Annotations[consts.SidecarIstioInjectKey] = consts.SidecarIstioInjectValue
	deployment.Spec.Template.Labels[consts.TeamIdLabelKey] = fmt.Sprintf("%d", development.TeamId)
	deployment.Spec.Template.Labels[consts.TaskExecutionNameLabelKey] = executionName

	// 添加环境标签，从ENV环境变量获取值
	if envValue := os.Getenv("ENV"); envValue != "" {
		deployment.Spec.Template.Labels[consts.EnvLabelKey] = envValue
	}

	// 如果CmdbId不为空，添加label_uuid标签
	if development.CmdbId != "" {
		deployment.Spec.Template.Labels[consts.LabelUuidKey] = development.CmdbId
	}

	// 添加shm volume
	shmVolume := v1.Volume{
		Name: "dshm",
		VolumeSource: v1.VolumeSource{
			EmptyDir: &v1.EmptyDirVolumeSource{
				Medium:    v1.StorageMediumMemory,
				SizeLimit: &shmSize,
			},
		},
	}
	volumes = append(volumes, shmVolume)

	deployment.Spec.Template.Spec.Volumes = volumes
	if clusterResource.GpuType != "" {
		deployment.Spec.Template.Spec.Affinity = &v1.Affinity{
			NodeAffinity: &v1.NodeAffinity{
				RequiredDuringSchedulingIgnoredDuringExecution: &v1.NodeSelector{
					NodeSelectorTerms: []v1.NodeSelectorTerm{
						{
							MatchExpressions: []v1.NodeSelectorRequirement{
								{
									Key:      "gpu-model",
									Operator: "In",
									Values:   []string{strings.ToLower(clusterResource.GpuType)},
								},
							},
						},
					},
				},
			},
		}
		deployment.Spec.Template.Spec.Tolerations = []v1.Toleration{
			{
				Key:      "pool-type",
				Operator: "Equal",
				Value:    "gpu",
				Effect:   "NoSchedule",
			},
		}
	}

	return deployment
}

func toService(executionName string, development *entity.OnlineDevelopment) (svc *v1.Service) {
	svc = &v1.Service{
		TypeMeta: metav1.TypeMeta{
			Kind:       "Service",
			APIVersion: "v1",
		},
	}
	svc.Name = executionName
	svc.Namespace = development.Namespace
	svc.Spec.Selector = map[string]string{"app": executionName}
	svc.Annotations = map[string]string{
		ManagedByAnnotationKey:             ManagedByAnnotationValue,
		OnlineDevelopmentTypeAnnotationKey: development.Type,
	}
	svc.Spec.Type = v1.ServiceTypeClusterIP

	if development.Type == consts.OnlineDevTypeJupyter.ToString() {
		svc.Spec.Ports = []v1.ServicePort{
			{
				Protocol:   v1.ProtocolTCP,
				Port:       8000,
				TargetPort: intstr.FromInt(8888),
			},
		}
	}
	if development.Type == consts.OnlineDevTypeCodeServer.ToString() {
		svc.Spec.Ports = []v1.ServicePort{
			{
				Protocol:   v1.ProtocolTCP,
				Port:       8000,
				TargetPort: intstr.FromInt(8080),
			},
		}
	}

	return svc
}

func parseGpuResource(clusterName string, clusterResource *entity.OnlineDevelopmentClusterResource) (map[v1.ResourceName]resource.Quantity, map[v1.ResourceName]resource.Quantity) {
	// parse resources
	resourceRequestMap := make(map[v1.ResourceName]resource.Quantity)
	resourceRequestMap[v1.ResourceCPU], _ = resource.ParseQuantity(clusterResource.RequestCpu)
	resourceRequestMap[v1.ResourceMemory], _ = resource.ParseQuantity(clusterResource.RequestMemory)
	resourceRequestMap[v1.ResourceName(GpuCoreKey)], _ = resource.ParseQuantity(clusterResource.RequestGpuCore)
	resourceRequestMap[v1.ResourceName(GpuMemoryKey)], _ = resource.ParseQuantity(clusterResource.RequestGpuMemory)
	resourceLimitMap := make(map[v1.ResourceName]resource.Quantity)
	resourceLimitMap[v1.ResourceCPU], _ = resource.ParseQuantity(clusterResource.LimitCpu)
	resourceLimitMap[v1.ResourceMemory], _ = resource.ParseQuantity(clusterResource.LimitMemory)
	resourceLimitMap[v1.ResourceName(GpuCoreKey)], _ = resource.ParseQuantity(clusterResource.LimitGpuCore)
	resourceLimitMap[v1.ResourceName(GpuMemoryKey)], _ = resource.ParseQuantity(clusterResource.LimitGpuMemory)
	clusterType := getClusterType(clusterName)
	converter := gpu.GetStrategy(clusterType)
	if converter != nil {
		converter.Convert(resourceRequestMap)
		converter.Convert(resourceLimitMap)
	}
	// 去除map中quantity为0的
	for k, v := range resourceRequestMap {
		if v.Cmp(resource.Quantity{}) == 0 {
			delete(resourceRequestMap, k)
		}
	}
	for k, v := range resourceLimitMap {
		if v.Cmp(resource.Quantity{}) == 0 {
			delete(resourceLimitMap, k)
		}
	}
	return resourceRequestMap, resourceLimitMap
}

func getClusterType(clusterName string) string {
	switch {
	case strings.HasPrefix(clusterName, "k8s-tc"):
		return consts.ClusterTypeTencent
	case strings.HasPrefix(clusterName, "k8s-ali"):
		return consts.ClusterTypeAliyun
	case strings.HasPrefix(clusterName, "k8s-hw"):
		return consts.ClusterTypeHuawei
	case strings.HasPrefix(clusterName, "k8s-hs"):
		return consts.ClusterTypeVolcengine
	default:
		return ""
	}
}

// checkKueueResources 检查 Kueue 资源
func checkKueueResources(ctx context.Context, development *entity.OnlineDevelopment, clusterResource *entity.OnlineDevelopmentClusterResource) error {
	// 1. 检查团队集群和命名空间
	clusterNamespaces, err := service.Team().ListClusterNamespace(ctx, int(development.TeamId))
	if err != nil {
		return fmt.Errorf("获取团队集群命名空间失败: %w", err)
	}

	// 2. 检查 onlineDevelopment 的 ClusterName 和 Namespace 是否在团队配置中
	clusterNamespaceFound := false
	for _, clusterNs := range clusterNamespaces {
		if clusterNs.Cluster == development.ClusterName {
			for _, namespace := range clusterNs.Namespaces {
				if namespace == development.Namespace {
					clusterNamespaceFound = true
					break
				}
			}
			if clusterNamespaceFound {
				break
			}
		}
	}
	if !clusterNamespaceFound {
		return fmt.Errorf("集群 %s 的命名空间 %s 不在您所在的团队配置内，请联系管理员为您的团队添加相应的集群和命名空间权限", development.ClusterName, development.Namespace)
	}

	// 3. 获取团队资源配额
	quotas, err := service.Team().ListGPUQuota(ctx, int(development.TeamId))
	if err != nil {
		return fmt.Errorf("获取团队资源配额失败: %w", err)
	}

	var resourceFlavorName string
	if len(clusterResource.GpuType) > 0 {
		// 4. 检查 GPU 类型是否在配额内
		gpuTypeFound := false
		for _, quota := range quotas {
			if strings.Contains(quota.GpuType, clusterResource.GpuType) {
				gpuTypeFound = true
				break
			}
		}
		if !gpuTypeFound {
			return fmt.Errorf("GPU 类型 %s 不在您所在的团队资源配额内，请联系管理员为您的团队添加", clusterResource.GpuType)
		}

		// 5. 检查节点是否实际配备GPU资源
		nodes, err := client.ListNode(ctx, development.ClusterName, []constack_openapi.LabelSelector{
			{
				Key:   kueue.NodeLabelGpuModel,
				Op:    "=",
				Value: strings.ToLower(clusterResource.GpuType),
			},
		})
		if err != nil {
			return fmt.Errorf("获取 GPU[%s] 节点失败: %w", clusterResource.GpuType, err)
		}
		if len(nodes) == 0 {
			return fmt.Errorf("GPU 类型 %s 在集群 %s 没有实际配备，请联系管理员为您的集群添加", clusterResource.GpuType, development.ClusterName)
		}

		resourceFlavorName = kueue.ResourceFlavorPrefix + strings.ToLower(clusterResource.GpuType)
	} else {
		resourceFlavorName = kueue.NullResourceFlavorName
	}

	// 6. 检查 WorkloadPriorityClass 资源 (使用默认优先级 P0)
	err = kueue.EnsureWorkloadPriorityClass(ctx, development.ClusterName, "P0")
	if err != nil {
		return fmt.Errorf("确认 WorkloadPriorityClass 失败: %w", err)
	}

	// 7. 检查 ResourceFlavor 资源
	existingRF, err := client.GetResourceFlavor(ctx, development.ClusterName, resourceFlavorName)
	needUpdate := false
	if err != nil || existingRF == nil {
		needUpdate = true
	} else {
		// 8. 检查 ClusterQueue 资源
		clusterQueueName := kueue.ClusterQueuePrefix + strconv.Itoa(int(development.TeamId))
		existingCQ, err := client.GetClusterQueue(ctx, development.ClusterName, clusterQueueName)
		if err != nil || existingCQ == nil {
			needUpdate = true
		} else {
			// 9. 检查 ClusterQueue 的 flavors 中是否包含该 ResourceFlavor
			flavorFound := false
			for _, resourceGroup := range existingCQ.Spec.ResourceGroups {
				for _, flavor := range resourceGroup.Flavors {
					if string(flavor.Name) == resourceFlavorName {
						flavorFound = true
						break
					}
				}
				if flavorFound {
					break
				}
			}
			if !flavorFound {
				needUpdate = true
			}
		}
	}

	// 10. 如果需要更新，触发 Kueue 资源更新
	if needUpdate {
		// 更新 Kueue 资源
		err = kueue.UpdateClusterKueueResources(ctx, int(development.TeamId), development.ClusterName, []string{development.Namespace}, quotas)
		if err != nil {
			return fmt.Errorf("更新 Kueue 资源失败: %w", err)
		}
	}

	// 11. 检查 LocalQueue 资源
	err = kueue.EnsureLocalQueue(ctx, development.ClusterName, development.Namespace, int(development.TeamId), kueue.OnlineDevelopmentIndex)
	if err != nil {
		return fmt.Errorf("确认 LocalQueue 失败: %w", err)
	}

	return nil
}
