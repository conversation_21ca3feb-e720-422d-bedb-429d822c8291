package resource

import (
	"context"
	"encoding/json"
	v1 "mlops/api/resource/v1"
	"mlops/internal/consts/team"
	"mlops/internal/dao"
	"mlops/internal/model/dto"
	"mlops/internal/model/entity"
	"mlops/internal/service"
	"mlops/utility/kueue"

	"gitlab.ttyuyin.com/yunwei-infra/tt-cloud-constack-sdk/pkg/log"
)

type sResource struct{}

func init() {
	service.RegisterResource(newsResource())
}

func newsResource() *sResource {
	return &sResource{}
}

type teamT interface {
	dto.ClusterNamespace | dto.GpuQuota
}

func gpuQuotaJsonParse(data string) []*dto.GpuQuota {
	res := make([]*dto.GpuQuota, 0)
	_ = json.Unmarshal([]byte(data), &res)
	return res
}

func clusterNamespaceJsonParse(data string) []*dto.ClusterNamespace {
	res := make([]*dto.ClusterNamespace, 0)
	_ = json.Unmarshal([]byte(data), &res)
	return res
}

func (this *sResource) GetGpuList(ctx context.Context) (res []*dto.GpuDetail, err error) {

	res = make([]*dto.GpuDetail, 0)
	gpuListEntity := make([]*entity.GpuList, 0)
	err = dao.GpuList.Ctx(ctx).
		Scan(&gpuListEntity)
	if err != nil {
		return
	}
	for _, v := range gpuListEntity {
		res = append(res, &dto.GpuDetail{
			GpuCore:   v.GpuCore,
			GpuAlias:  v.GpuAlias,
			GpuMemory: v.GpuMemory,
			GpuType:   v.GpuType,
		})
	}

	return
}

func (this *sResource) CreateGpuList(ctx context.Context, data *dto.GpuDetail) (err error) {

	log.L.WithName("sResource.CreateGpuList").Infof(ctx, "data:%s", data)
	_, err = dao.GpuList.Ctx(ctx).
		Save(&entity.GpuList{
			GpuType:   data.GpuType,
			GpuAlias:  data.GpuAlias,
			GpuCore:   data.GpuCore,
			GpuMemory: data.GpuMemory,
		})

	return
}

func (this *sResource) GetGpuOverview(ctx context.Context) (res []*dto.GpuOverview, err error) {

	res = make([]*dto.GpuOverview, 0)
	gpuListEntity := make([]*entity.GpuList, 0)
	if err = dao.GpuList.Ctx(ctx).Scan(&gpuListEntity); err != nil {
		return
	}
	gpuMap := make(map[string]*entity.GpuList)
	for _, v := range gpuListEntity {
		gpuMap[v.GpuType] = v
	}

	quotaListEntity := make([]*entity.Team, 0)
	if err = dao.Team.Ctx(ctx).
		Fields(team.GpuQuota).
		Fields(team.ClusterNamespace).
		Scan(&quotaListEntity); err != nil {
		return
	}

	clusterMap := make(map[string]bool)
	gpuOverviewMap := make(map[string]*dto.GpuOverview)
	for i := 0; i < len(quotaListEntity); i++ {
		if len(quotaListEntity[i].ClusterNamespace) > 0 {
			clusterNamespaces := clusterNamespaceJsonParse(quotaListEntity[i].ClusterNamespace)
			for j := 0; j < len(clusterNamespaces); j++ {
				if _, ok := clusterMap[clusterNamespaces[j].Cluster]; !ok {
					clusterMap[clusterNamespaces[j].Cluster] = true
				}
			}
		}
		if len(quotaListEntity[i].GpuQuota) > 0 {
			quotasList := gpuQuotaJsonParse(quotaListEntity[i].GpuQuota)
			for j := 0; j < len(quotasList); j++ {
				gpuDetail, exist := gpuMap[quotasList[j].GpuType]
				if exist {
					gpuOverview, ok := gpuOverviewMap[quotasList[j].GpuType]
					if !ok {
						gpuOverviewMap[quotasList[j].GpuType] = &dto.GpuOverview{
							GpuType:    gpuDetail.GpuType,
							GpuAlias:   gpuDetail.GpuAlias,
							GpuCore:    gpuDetail.GpuCore,
							GpuMemory:  gpuDetail.GpuMemory,
							Budget:     quotasList[j].Nums,
							Allocation: 0,
						}
					} else {
						gpuOverview.Budget += quotasList[j].Nums
					}
				}
			}
		}
	}

	var clusters []string
	for k := range clusterMap {
		clusters = append(clusters, k)
	}

	allocatableResources, err := kueue.GetClusterGpuAllocatableResources(ctx, clusters)
	if err != nil {
		return
	}

	for _, v := range gpuOverviewMap {
		if allocation, ok := allocatableResources[v.GpuType]; ok {
			v.Allocation = uint(allocation)
		}
		res = append(res, v)
	}

	return
}

func (this *sResource) GetTeamOverview(ctx context.Context, teamId int) (res []*dto.TeamOverview, err error) {

	res = make([]*dto.TeamOverview, 0)
	teamEntity := make([]*entity.Team, 0)
	m := dao.Team.Ctx(ctx).
		Fields(team.PriId,
			team.Name,
			team.TeamId,
			team.GpuQuota,
			team.ClusterNamespace)
	if teamId != -1 {
		m = m.Where(team.PriId, teamId)
	}
	if err = m.Scan(&teamEntity); err != nil {
		return
	}
	for i := 0; i < len(teamEntity); i++ {
		teamOverview := &dto.TeamOverview{
			Id:                teamEntity[i].Id,
			Name:              teamEntity[i].Name,
			TeamId:            teamEntity[i].TeamId,
			ClusterNamespaces: []*dto.ClusterNamespace{},
			GpuQuota:          []*dto.GpuQuota{},
		}
		if len(teamEntity[i].GpuQuota) > 0 {
			teamOverview.GpuQuota = gpuQuotaJsonParse(teamEntity[i].GpuQuota)
		}
		if len(teamEntity[i].ClusterNamespace) > 0 {
			teamOverview.ClusterNamespaces = clusterNamespaceJsonParse(teamEntity[i].ClusterNamespace)
		}
		res = append(res, teamOverview)
	}

	return
}

func (this *sResource) ListGpuMonitor(ctx context.Context, teamId int) (res *v1.ListGpuMonitorRes, err error) {
	res = &v1.ListGpuMonitorRes{
		Running:                  make([]*dto.GpuQuotaFloat, 0),
		Pending:                  make([]*dto.GpuQuotaFloat, 0),
		Idle:                     make([]*dto.GpuQuotaFloat, 0),
		RunningTrainTask:         make([]*dto.GpuQuotaFloat, 0),
		RunningOnlineDevelopment: make([]*dto.GpuQuotaFloat, 0),
	}

	var clusters []string

	// 获取集群信息
	if teamId > 0 {
		// 获取指定团队的集群命名空间信息
		teamEntity := &entity.Team{}
		err = dao.Team.Ctx(ctx).Fields("cluster_namespace").Where("id", teamId).Scan(teamEntity)
		if err != nil {
			log.L.WithName("ListGpuMonitor").Errorf(ctx, "Failed to get team cluster_namespace: %s", err.Error())
			return nil, err
		}

		// 解析 cluster_namespace 获取集群列表
		if teamEntity.ClusterNamespace != "" {
			var clusterNamespaces []*dto.ClusterNamespace
			err = json.Unmarshal([]byte(teamEntity.ClusterNamespace), &clusterNamespaces)
			if err != nil {
				log.L.WithName("ListGpuMonitor").Errorf(ctx, "Failed to parse cluster_namespace: %s", err.Error())
				return nil, err
			}

			// 提取集群名称
			clusterMap := make(map[string]bool)
			for _, cn := range clusterNamespaces {
				clusterMap[cn.Cluster] = true
			}
			for cluster := range clusterMap {
				clusters = append(clusters, cluster)
			}
		}
	} else {
		// teamId == 0 时，获取所有团队记录中的 cluster_namespace 并解析出所有集群
		var teamEntities []*entity.Team
		err = dao.Team.Ctx(ctx).Fields("cluster_namespace").Where("cluster_namespace IS NOT NULL AND cluster_namespace != ''").Scan(&teamEntities)
		if err != nil {
			log.L.WithName("ListGpuMonitor").Errorf(ctx, "Failed to get all teams cluster_namespace: %s", err.Error())
			return nil, err
		}

		// 解析所有团队的 cluster_namespace 获取集群列表
		clusterMap := make(map[string]bool)
		for _, team := range teamEntities {
			if team.ClusterNamespace != "" {
				var clusterNamespaces []*dto.ClusterNamespace
				err = json.Unmarshal([]byte(team.ClusterNamespace), &clusterNamespaces)
				if err != nil {
					log.L.WithName("ListGpuMonitor").Warningf(ctx, "Failed to parse cluster_namespace for team %d: %s", team.Id, err.Error())
					continue
				}

				// 提取集群名称
				for _, cn := range clusterNamespaces {
					clusterMap[cn.Cluster] = true
				}
			}
		}

		// 转换为切片
		for cluster := range clusterMap {
			clusters = append(clusters, cluster)
		}
	}

	if len(clusters) == 0 {
		return res, nil
	}

	// 获取运行中的GPU配额
	runningGpuMap, err := kueue.GetClusterGpuUsedResources(ctx, clusters, teamId)
	if err != nil {
		log.L.WithName("ListGpuMonitor").Errorf(ctx, "Failed to get cluster GPU used resources: %s", err.Error())
		return nil, err
	}

	// 转换为 GpuQuotaFloat 格式
	for gpuType, nums := range runningGpuMap {
		res.Running = append(res.Running, &dto.GpuQuotaFloat{
			GpuType: gpuType,
			Nums:    nums,
		})
	}

	// 获取训练任务的GPU使用情况
	trainTaskGpuMap, err := kueue.GetModelGpuUsedResources(ctx, clusters, teamId, kueue.TrainTaskValue)
	if err != nil {
		log.L.WithName("ListGpuMonitor").Errorf(ctx, "Failed to get train task GPU used resources: %s", err.Error())
		return nil, err
	}

	// 转换为 GpuQuotaFloat 格式
	for gpuType, nums := range trainTaskGpuMap {
		res.RunningTrainTask = append(res.RunningTrainTask, &dto.GpuQuotaFloat{
			GpuType: gpuType,
			Nums:    nums,
		})
	}

	// 获取在线开发的GPU使用情况
	onlineDevGpuMap, err := kueue.GetModelGpuUsedResources(ctx, clusters, teamId, kueue.OnlineDevelopmentValue)
	if err != nil {
		log.L.WithName("ListGpuMonitor").Errorf(ctx, "Failed to get online development GPU used resources: %s", err.Error())
		return nil, err
	}

	// 转换为 GpuQuotaFloat 格式
	for gpuType, nums := range onlineDevGpuMap {
		res.RunningOnlineDevelopment = append(res.RunningOnlineDevelopment, &dto.GpuQuotaFloat{
			GpuType: gpuType,
			Nums:    nums,
		})
	}

	// 获取等待中的GPU配额和等待时间
	pendingGpuMap, waitingTimeSeconds, err := kueue.GetClusterGpuPendingResources(ctx, clusters, teamId)
	if err != nil {
		log.L.WithName("ListGpuMonitor").Errorf(ctx, "Failed to get cluster GPU pending resources: %s", err.Error())
		return nil, err
	}

	// 转换为 GpuQuotaFloat 格式
	for gpuType, nums := range pendingGpuMap {
		res.Pending = append(res.Pending, &dto.GpuQuotaFloat{
			GpuType: gpuType,
			Nums:    nums,
		})
	}

	// 设置预估等待时间
	res.EstimatedPendingTimeSeconds = waitingTimeSeconds

	// 当 teamId == 0 时，计算空闲GPU配额
	if teamId == 0 {
		// 获取所有集群的可分配GPU资源
		allocatableGpuMap, err := kueue.GetClusterGpuAllocatableResources(ctx, clusters)
		if err != nil {
			log.L.WithName("ListGpuMonitor").Errorf(ctx, "Failed to get cluster GPU allocatable resources: %s", err.Error())
			return nil, err
		}

		// 计算空闲GPU = 可分配GPU - 运行中的GPU
		idleGpuMap := make(map[string]float64)
		for gpuType, allocatable := range allocatableGpuMap {
			idle := allocatable
			if running, exists := runningGpuMap[gpuType]; exists {
				idle -= running
			}
			if idle > 0 {
				idleGpuMap[gpuType] = idle
			}
		}

		// 转换为 GpuQuotaFloat 格式
		for gpuType, nums := range idleGpuMap {
			res.Idle = append(res.Idle, &dto.GpuQuotaFloat{
				GpuType: gpuType,
				Nums:    nums,
			})
		}
	}

	return res, nil
}
