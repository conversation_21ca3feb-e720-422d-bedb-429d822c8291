package resource

import (
	"context"
	"gitlab.ttyuyin.com/yunwei-infra/tt-cloud-constack-sdk/pkg/log"
	"mlops/internal/dao"
	"mlops/internal/model/dto"
	"mlops/internal/model/entity"
	"mlops/internal/service"
)

type sResource struct{}

func init() {
	service.RegisterResource(newsResource())
}

func newsResource() *sResource {
	return &sResource{}
}

func (this *sResource) GetGpuList(ctx context.Context) (res []*dto.GpuDetail, err error) {

	res = make([]*dto.GpuDetail, 0)
	gpuListEntity := make([]*entity.GpuList, 0)
	err = dao.GpuList.Ctx(ctx).
		Scan(&gpuListEntity)
	if err != nil {
		return
	}
	for _, v := range gpuListEntity {
		res = append(res, &dto.GpuDetail{
			GpuCore:   v.Gpu<PERSON>ore,
			GpuMemory: v.Gpu<PERSON>ory,
			GpuType:   v.GpuType,
		})
	}

	return
}

func (this *sResource) CreateGpuList(ctx context.Context, data *dto.GpuDetail) (err error) {

	log.L.WithName("sResource.CreateGpuList").Infof(ctx, "data:%s", data)
	_, err = dao.GpuList.Ctx(ctx).
		Save(&entity.GpuList{
			GpuType:   data.GpuType,
			GpuCore:   data.GpuCore,
			GpuMemory: data.GpuMemory,
		})

	return
}
