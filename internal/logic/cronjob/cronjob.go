package cronjob

import (
	"context"
	"fmt"
	"mlops/internal/service"
	"time"

	"mlops/tools/election"

	"github.com/gogf/gf/v2/os/gcron"
	"gitlab.ttyuyin.com/yunwei-infra/tt-cloud-constack-sdk/pkg/log"
)

type sCronJob struct {
	cron     *gcron.Cron
	election election.Election
}

func init() {
	service.RegisterCronJob(newsCronJob())
}

func newsCronJob() *sCronJob {
	ctx := context.Background()
	election := election.NewRedisElection(ctx)
	election.Start()
	return &sCronJob{
		cron:     gcron.New(),
		election: election,
	}
}

//	========  seconds ========
//
// gcron 内部的 func 所有panic错误都回被忽略
func (this *sCronJob) Test(ctx context.Context) (err error) {
	_, err = gcron.Add(ctx, "*/3 * * * * *", func(ctx context.Context) {
		fmt.Println("test-print-cronjob ", time.Now())
	}, "test-print-cronjob")

	return err
}

func (this *sCronJob) SyncCicdTeam(ctx context.Context) error {
	_, err := gcron.Add(ctx, "0 0/10 * * * ?", func(ctx context.Context) {
		log.L.WithName("sCronJob.SyncCicdTeam").Info(ctx, "CronJob SyncCicdTeam running.")
		if !this.election.IsMaster() {
			log.L.WithName("sCronJob.SyncCicdTeam").Info(ctx, "not master, skip")
			return
		}
		err := service.Team().Sync(ctx)
		if err != nil {
			log.L.WithName("sCronJob.SyncCicdTeam").Errorf(ctx, "error: %s", err.Error())
		}
	}, "SyncCicdTeamCron")
	return err
}

func (this *sCronJob) SyncTrainTaskExecution(ctx context.Context) error {
	_, err := gcron.Add(ctx, "*/30 * * * * ?", func(ctx context.Context) {
		log.L.WithName("sCronJob.SyncTrainTaskExecution").Info(ctx, "CronJob SyncTrainTaskExecution running.")
		if !this.election.IsMaster() {
			log.L.WithName("sCronJob.SyncTrainTaskExecution").Info(ctx, "not master, skip")
			return
		}
		err := service.TrainTaskExecution().SyncTaskExecutionStatus(ctx)
		if err != nil {
			log.L.WithName("sCronJob.SyncTrainTaskExecution").Errorf(ctx, "error: %s", err.Error())
		}
	}, "SyncTrainTaskExecutionCron")
	return err
}

func (this *sCronJob) SyncOnlineDevelopmentStatus(ctx context.Context) error {
	_, err := gcron.Add(ctx, "*/15 * * * * ?", func(ctx context.Context) {
		log.L.WithName("sCronJob.SyncOnlineDevelopmentStatus").Info(ctx, "CronJob SyncOnlineDevelopmentStatus running.")
		if !this.election.IsMaster() {
			log.L.WithName("sCronJob.SyncOnlineDevelopmentStatus").Info(ctx, "not master, skip")
			return
		}

		err := service.OnlineDevelopment().SyncStatus(ctx)
		if err != nil {
			log.L.WithName("sCronJob.SyncOnlineDevelopmentStatus").Errorf(ctx, "error: %s", err.Error())
		}
	}, "SyncOnlineDevelopmentStatusCron")
	return err
}
