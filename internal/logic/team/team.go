package team

import (
	"context"
	"database/sql"
	"encoding/json"
	"errors"
	"fmt"
	"mlops/internal/consts"
	"mlops/internal/consts/auth"
	"mlops/internal/consts/team"
	"mlops/internal/dao"
	"mlops/internal/model/do"
	"mlops/internal/model/dto"
	"mlops/internal/model/entity"
	"mlops/internal/service"
	"mlops/tools/client"
	localErrors "mlops/tools/errors"
	"mlops/utility/kueue"
	"strconv"
	"strings"

	"github.com/gogf/gf/v2/crypto/gmd5"
	"github.com/gogf/gf/v2/database/gdb"
	"gitlab.ttyuyin.com/yunwei-infra/tt-cloud-constack-sdk/pkg/log"
	cicd_app_api_v1 "gitlab.ttyuyin.com/yunwei-infra/tt-cloud-constack-sdk/pkg/resources/cicd/b.api.v1/app"
	"gitlab.ttyuyin.com/yunwei-infra/tt-cloud-constack-sdk/pkg/resources/cmdb/api"
	"gitlab.ttyuyin.com/yunwei-infra/tt-cloud-constack-sdk/pkg/utils/slice"
)

/**
team:
定时向cicd拉取团队列表 并持久化在team表中
*/

type sTeam struct {
	teamSynchronizer *teamSynchronizer
}

func init() {
	service.RegisterTeam(newsTeam())
}

func newsTeam() *sTeam {
	return &sTeam{
		teamSynchronizer: newTeamSynchronizer(),
	}
}

func (this *sTeam) Table() string {
	return dao.Team.Table()
}

func (this *sTeam) Sync(ctx context.Context) (err error) {
	return this.teamSynchronizer.start(ctx)
}

// SyncTeamOrganization 根据用户id同步组织架构团队
func (this *sTeam) SyncTeamOrganization(ctx context.Context, userId int) (pid int64, err error) {
	// 同步当前团队
	userInfo, err := service.User().GetUserById(ctx, userId)
	if err != nil {
		return 0, err
	}

	relations, err := client.CmdbApiJsonRpc.GetUserOrganizationRelation(ctx, userInfo.EmployeeNo)
	if err != nil {
		return 0, err
	}

	slice.QuickSort[*api.OrganizationRelationInfo](relations, func(a, b *api.OrganizationRelationInfo) bool {
		return a.Level < b.Level
	})

	var (
		teamName string
	)

	for _, relation := range relations {
		teamName = fmt.Sprintf("%s-%s", teamName, relation.DeptName)
	}

	teamId, err := gmd5.Encrypt(teamName)
	if err != nil {
		return 0, err
	}

	teamName = strings.Trim(teamName, "-")

	res, err := dao.Team.Ctx(ctx).Data(do.Team{
		Category: auth.TeamOrganization,
		Name:     teamName,
		TeamId:   teamId,
	}).Save()
	if err != nil {
		return 0, err
	}

	return res.LastInsertId()
}

func (this *sTeam) List(ctx context.Context) (list []*entity.Team, err error) {
	err = dao.Team.Ctx(ctx).Scan(&list)
	return
}

func (this *sTeam) ListRelatedClusterNamespace(ctx context.Context, teamId int) (res []*dto.ClusterNamespace, err error) {
	res = make([]*dto.ClusterNamespace, 0)
	val, err := service.Setting().GetVal(ctx, consts.GenerateTeamRelatedClusterNamespaceKey(teamId))
	if err != nil {
		return nil, err
	}

	log.L.WithName("sTeam.ListRelatedClusterNamespace").Infof(ctx, "team related cluster namespace: %s", val)

	if val == "" {
		log.L.WithName("sTeam.ListRelatedClusterNamespace").Warningf(ctx, "team related cluster namespace is empty")
		return res, nil
	}

	err = json.Unmarshal([]byte(val), &res)
	if err != nil {
		log.L.WithName("sTeam.ListRelatedClusterNamespace").Errorf(ctx, "unmarshal team"+
			" related cluster namespace error:%s", err.Error())
		return res, err
	}

	return
}

func (this *sTeam) ListTeamUser(ctx context.Context, teamId int) (res []*dto.UserTeamRole, err error) {
	res = make([]*dto.UserTeamRole, 0)

	// 首先根据teamId查询团队信息，获取业务teamId
	teamEntity := &entity.Team{}
	err = dao.Team.Ctx(ctx).Where(dao.Team.Columns().Id, teamId).Scan(teamEntity)
	if err != nil {
		return nil, fmt.Errorf("查询团队信息失败: %s", err.Error())
	}

	// 使用原生SQL查询用户、角色、团队关系
	db, err := gdb.Instance()
	if err != nil {
		return nil, err
	}

	sql := `
SELECT
  u.id AS user_id,
  u.username,
  u.nick_name,
  u.email,
  u.employee_no,
  r.name AS role_name,
  t.team_id,
  t.category
FROM
  tt_user u
  INNER JOIN tt_user_role_team_rela urt ON u.id = urt.user_id
  INNER JOIN tt_team t ON urt.team_id = t.team_id
  INNER JOIN tt_role r ON urt.role_id = r.id
WHERE t.team_id = ?
`

	type queryResult struct {
		UserId     int    `json:"user_id"`
		Username   string `json:"username"`
		NickName   string `json:"nick_name"`
		Email      string `json:"email"`
		EmployeeNo string `json:"employee_no"`
		RoleName   string `json:"role_name"`
		TeamId     string `json:"team_id"`
		Category   string `json:"category"`
	}

	results := make([]*queryResult, 0)
	all, err := db.Ctx(ctx).GetAll(ctx, sql, teamEntity.TeamId)
	if err != nil {
		return nil, fmt.Errorf("查询团队用户失败: %s", err.Error())
	}

	err = all.Structs(&results)
	if err != nil {
		return nil, fmt.Errorf("解析查询结果失败: %s", err.Error())
	}

	// 转换查询结果为返回格式
	for _, result := range results {
		var role auth.TeamRoleKind
		switch result.RoleName {
		case string(auth.RoleTeamAdmin):
			role = auth.TeamRoleAdmin
		case string(auth.RoleUser):
			role = auth.TeamRoleUser
		default:
			// 跳过不认识的角色
			continue
		}

		res = append(res, &dto.UserTeamRole{
			TeamId:     result.TeamId,
			UserId:     result.UserId,
			Role:       role,
			Username:   result.Username,
			NickName:   result.NickName,
			Email:      result.Email,
			EmployeeNo: result.EmployeeNo,
			Id:         teamId,
			TeamName:   teamEntity.Name,
			TeamType:   auth.TeamKind(teamEntity.Category),
		})
	}

	return
}

type teamSynchronizer struct {
}

func newTeamSynchronizer() *teamSynchronizer {
	return &teamSynchronizer{}
}

func (this *teamSynchronizer) start(ctx context.Context) (err error) {
	projects, err := client.CicdAppProject.ListProject(ctx)
	if err != nil {
		return err
	}

	teamEntities := make([]*entity.Team, 0, len(projects))

	for _, project := range projects {
		teamEntities = append(teamEntities, &entity.Team{
			Category: string(auth.TeamFeature),
			Name:     project.Name,
			TeamId:   fmt.Sprintf("%d", project.Id),
		})
	}

	_, err = dao.Team.Ctx(ctx).Data(teamEntities).Fields(dao.Team.Columns().Category,
		dao.Team.Columns().Name,
		dao.Team.Columns().TeamId).Batch(50).Save()
	if err != nil {
		return err
	}

	return
}

func (this *sTeam) ListClusterNamespace(ctx context.Context, teamId int) (list []*dto.ClusterNamespace, err error) {

	list = make([]*dto.ClusterNamespace, 0)
	teamEntity := entity.Team{}
	err = dao.Team.Ctx(ctx).
		Fields(team.ClusterNamespace).
		Where("id", strconv.Itoa(teamId)).
		Scan(&teamEntity)
	if errors.Is(err, sql.ErrNoRows) {
		log.L.WithName("team ListClusterNamespace").Infof(ctx, "teamId %d, sql.ErrNoRows", teamId)
		return list, nil
	}
	if err != nil {
		return
	}
	if len(teamEntity.ClusterNamespace) > 0 {
		err = json.Unmarshal([]byte(teamEntity.ClusterNamespace), &list)
	}

	return
}

func (this *sTeam) CreateGPUQuota(ctx context.Context, teamId int, quota *dto.GpuQuota) (err error) {

	if !team.IsValidGpuType(quota.GpuType) {
		return localErrors.NewError(localErrors.CodeInvalidGpu, "Invalid GpuType")
	}

	quotas := make([]*dto.GpuQuota, 0)
	teamEntity := entity.Team{}
	err = dao.Team.Transaction(ctx, func(ctx context.Context, tx gdb.TX) (err error) {
		err = dao.Team.Ctx(ctx).
			Fields(team.GpuQuota).
			Where(team.PriId, strconv.Itoa(teamId)).
			Scan(&teamEntity)
		if err != nil {
			return
		}
		if len(teamEntity.GpuQuota) > 0 {
			err = json.Unmarshal([]byte(teamEntity.GpuQuota), &quotas)
			if err != nil {
				return
			}
		}

		for _, q := range quotas {
			if q.GpuType == quota.GpuType {
				return localErrors.NewError(localErrors.CodeDuplicate, fmt.Sprintf("%s 类型的卡已存在，请勿重复添加。", q.GpuType))
			}
		}
		quotas = append(quotas, quota)

		jsonData, err := json.Marshal(quotas)
		if err != nil {
			return
		}
		_, err = dao.Team.Ctx(ctx).
			Where(team.PriId, strconv.Itoa(teamId)).
			Data(team.GpuQuota, string(jsonData)).
			Update()
		if err != nil {
			return
		}
		return
	})
	if err != nil {
		return
	}

	// 更新 Kueue 资源
	err = this.updateKueueResourcesByQuatas(ctx, teamId, quotas)
	if err != nil {
		log.L.WithName("sTeam.CreateGPUQuota").Errorf(ctx, "update kueue resources error: %s", err.Error())
		return err
	}

	return
}

func (this *sTeam) UpdateGPUQuota(ctx context.Context, teamId int, quota *dto.GpuQuota) (err error) {

	if !team.IsValidGpuType(quota.GpuType) {
		return localErrors.NewError(localErrors.CodeInvalidGpu, "Invalid GpuType")
	}

	quotas := make([]*dto.GpuQuota, 0)
	teamEntity := entity.Team{}
	err = dao.Team.Transaction(ctx, func(ctx context.Context, tx gdb.TX) (err error) {
		err = dao.Team.Ctx(ctx).
			Fields(team.GpuQuota).
			Where(team.PriId, strconv.Itoa(teamId)).
			Scan(&teamEntity)
		if err != nil {
			return
		}
		if len(teamEntity.GpuQuota) > 0 {
			err = json.Unmarshal([]byte(teamEntity.GpuQuota), &quotas)
			if err != nil {
				return
			}
		}

		for _, q := range quotas {
			if q.GpuType == quota.GpuType {
				q.GpuAlias = quota.GpuAlias
				q.GpuCore = quota.GpuCore
				q.GpuMemory = quota.GpuMemory
				q.Nums = quota.Nums
				break
			}
		}

		jsonData, err := json.Marshal(quotas)
		if err != nil {
			return
		}
		_, err = dao.Team.Ctx(ctx).
			Where(team.PriId, strconv.Itoa(teamId)).
			Data(team.GpuQuota, string(jsonData)).
			Update()
		if err != nil {
			return
		}
		return
	})
	if err != nil {
		return
	}

	// 更新 Kueue 资源
	err = this.updateKueueResourcesByQuatas(ctx, teamId, quotas)
	if err != nil {
		log.L.WithName("sTeam.UpdateGPUQuota").Errorf(ctx, "update kueue resources error: %s", err.Error())
		return err
	}

	return
}

func (this *sTeam) DeleteGPUQuota(ctx context.Context, teamId int, gpuType string) (err error) {

	quotas := make([]*dto.GpuQuota, 0)
	teamEntity := entity.Team{}
	err = dao.Team.Transaction(ctx, func(ctx context.Context, tx gdb.TX) (err error) {
		err = dao.Team.Ctx(ctx).
			Fields(team.GpuQuota).
			Where(team.PriId, strconv.Itoa(teamId)).
			Scan(&teamEntity)
		if err != nil {
			return
		}
		if len(teamEntity.GpuQuota) > 0 {
			err = json.Unmarshal([]byte(teamEntity.GpuQuota), &quotas)
			if err != nil {
				return
			}
		}

		for i, q := range quotas {
			if q.GpuType == gpuType {
				quotas = append(quotas[:i], quotas[i+1:]...)
				break
			}
		}

		jsonData, err := json.Marshal(quotas)
		if err != nil {
			return
		}
		_, err = dao.Team.Ctx(ctx).
			Where(team.PriId, strconv.Itoa(teamId)).
			Data(team.GpuQuota, string(jsonData)).
			Update()
		if err != nil {
			return
		}
		return
	})
	if err != nil {
		return
	}

	// 更新 Kueue 资源
	err = this.updateKueueResourcesByQuatas(ctx, teamId, quotas)
	if err != nil {
		log.L.WithName("sTeam.UpdateGPUQuota").Errorf(ctx, "update kueue resources error: %s", err.Error())
		return err
	}

	return
}

// updateKueueResourcesByQuatas 通过GPU配额更新 Kueue 资源
func (this *sTeam) updateKueueResourcesByQuatas(ctx context.Context, teamId int, quotas []*dto.GpuQuota) error {
	// 获取集群和命名空间列表
	clusterNamespaces, err := this.ListClusterNamespace(ctx, teamId)
	if err != nil {
		return fmt.Errorf("获取集群命名空间列表失败: %w", err)
	}

	// 循环处理每个集群
	for _, clusterNs := range clusterNamespaces {
		err = kueue.UpdateClusterKueueResources(ctx, teamId, clusterNs.Cluster, clusterNs.Namespaces, quotas)
		if err != nil {
			log.L.WithName("updateKueueResources").Errorf(ctx, "update cluster %s kueue resources error: %s", clusterNs.Cluster, err.Error())
			return err
		}
	}

	return nil
}

func (this *sTeam) ListGPUQuota(ctx context.Context, teamId int) (list []*dto.GpuQuota, err error) {

	list = make([]*dto.GpuQuota, 0)
	teamEntity := entity.Team{}
	err = dao.Team.Ctx(ctx).
		Fields(team.GpuQuota).
		Where(team.PriId, strconv.Itoa(teamId)).
		Scan(&teamEntity)
	if errors.Is(err, sql.ErrNoRows) {
		log.L.WithName("team ListGPUQuota").Infof(ctx, "teamId %d, sql.ErrNoRows", teamId)
		return list, nil
	}
	if err != nil {
		return nil, err
	}

	if len(teamEntity.GpuQuota) > 0 {
		err = json.Unmarshal([]byte(teamEntity.GpuQuota), &list)
	}

	return
}

func (this *sTeam) CreateClusterNamespace(ctx context.Context, teamId int, clusterNamespaces *dto.ClusterNamespace) (err error) {

	list := make([]*dto.ClusterNamespace, 0)
	teamEntity := entity.Team{}
	err = dao.Team.Transaction(ctx, func(ctx context.Context, tx gdb.TX) (err error) {
		err = dao.Team.Ctx(ctx).
			Fields(team.ClusterNamespace).
			Where(team.PriId, strconv.Itoa(teamId)).
			Scan(&teamEntity)
		if err != nil {
			return
		}
		if len(teamEntity.ClusterNamespace) > 0 {
			err = json.Unmarshal([]byte(teamEntity.ClusterNamespace), &list)
		}

		isExist := false
		for i := 0; i < len(list); i++ {
			if list[i].Cluster == clusterNamespaces.Cluster {
				isExist = true
				for j := 0; j < len(list[i].Namespaces); j++ {
					if list[i].Namespaces[j] == clusterNamespaces.Namespaces[0] {
						return localErrors.NewError(localErrors.CodeDuplicate, "重复集群命名空间")
					}
				}
				list[i].Namespaces = append(list[i].Namespaces, clusterNamespaces.Namespaces[0])
			}
		}
		if !isExist {
			// 目前配额只支持一个集群
			if len(list) == 0 {
				list = append(list, &dto.ClusterNamespace{Cluster: clusterNamespaces.Cluster, Namespaces: clusterNamespaces.Namespaces})
			} else {
				return localErrors.NewError(localErrors.CodeCorsResource, "为了避免资源碎片化，每个团队只允许绑定一个集群")
			}
		}

		jsonData, err := json.Marshal(list)
		if err != nil {
			return
		}
		_, err = dao.Team.Ctx(ctx).
			Where(team.PriId, strconv.Itoa(teamId)).
			Data(team.ClusterNamespace, string(jsonData)).
			Update()
		if err != nil {
			return
		}
		return
	})
	if err != nil {
		return
	}

	// 更新 Kueue 资源
	err = this.updateKueueResourcesByClusters(ctx, teamId, []*dto.ClusterNamespace{clusterNamespaces})
	if err != nil {
		log.L.WithName("sTeam.UpdateClusterNamespace").Errorf(ctx, "update kueue resources error: %s", err.Error())
		return err
	}

	return
}

func (this *sTeam) DeleteClusterNamespace(ctx context.Context, teamId int, clusterNamespaces *dto.ClusterNamespace) (err error) {

	list := make([]*dto.ClusterNamespace, 0)
	teamEntity := entity.Team{}
	err = dao.Team.Transaction(ctx, func(ctx context.Context, tx gdb.TX) (err error) {
		err = dao.Team.Ctx(ctx).
			Fields(team.ClusterNamespace).
			Where(team.PriId, strconv.Itoa(teamId)).
			Scan(&teamEntity)
		if err != nil {
			return
		}
		if len(teamEntity.ClusterNamespace) > 0 {
			err = json.Unmarshal([]byte(teamEntity.ClusterNamespace), &list)
		}

		for i := 0; i < len(list); i++ {
			if list[i].Cluster == clusterNamespaces.Cluster {
				for j := 0; j < len(list[i].Namespaces); j++ {
					if list[i].Namespaces[j] == clusterNamespaces.Namespaces[0] {
						list[i].Namespaces = append(list[i].Namespaces[:j], list[i].Namespaces[j+1:]...)
					}
				}
				if len(list[i].Namespaces) == 0 {
					list = append(list[:i], list[i+1:]...)
				}
			}
		}

		// 删除 Kueue 资源
		err = deleteKueueResources(ctx, teamId, clusterNamespaces, list)
		if err != nil {
			log.L.WithName("sTeam.DeleteClusterNamespace").Errorf(ctx, "delete kueue resources error: %s", err.Error())
			return
		}

		jsonData, err := json.Marshal(list)
		if err != nil {
			return
		}
		_, err = dao.Team.Ctx(ctx).
			Where(team.PriId, strconv.Itoa(teamId)).
			Data(team.ClusterNamespace, string(jsonData)).
			Update()
		if err != nil {
			return
		}
		return
	})
	if err != nil {
		return
	}

	return
}

func (this *sTeam) ListTeamApp(ctx context.Context, teamId int, page int, size int) (list []*dto.App, err error) {
	list = make([]*dto.App, 0)

	// 首先根据teamId查询团队信息，获取业务teamId
	teamEntity := &entity.Team{}
	err = dao.Team.Ctx(ctx).Where(dao.Team.Columns().Id, teamId).Scan(teamEntity)
	if err != nil {
		return nil, fmt.Errorf("查询团队信息失败: %s", err.Error())
	}

	// 将teamId转换为int，因为cicd接口需要int类型
	businessTeamId, err := strconv.Atoi(teamEntity.TeamId)
	if err != nil {
		return nil, fmt.Errorf("团队ID格式错误: %s", err.Error())
	}

	// 调用cicd接口获取指定项目的app列表
	// 创建ListAppsReq请求参数，包含分页信息
	req := &cicd_app_api_v1.ListAppsReq{
		ProjectId: businessTeamId,
		Page:      page,
		Size:      size,
	}

	apps, _, err := client.CicdAppProject.ListApps(ctx, req)
	if err != nil {
		log.L.WithName("sTeam.ListTeamApp").Errorf(ctx, "获取团队app列表失败: %s", err.Error())
		return nil, fmt.Errorf("获取团队app列表失败: %s", err.Error())
	}

	// 转换为dto格式
	for _, app := range apps {
		list = append(list, &dto.App{
			Id:     app.Id,
			Name:   app.Name,
			CmdbId: app.CmdbId,
		})
	}

	return list, nil
}

// updateKueueResourcesByClusters 通过集群更新 Kueue 资源
func (this *sTeam) updateKueueResourcesByClusters(ctx context.Context, teamId int, clusterNamespaces []*dto.ClusterNamespace) error {
	// 获取 GPU 资源配额
	quotas, err := this.ListGPUQuota(ctx, teamId)
	if err != nil {
		return fmt.Errorf("获取 GPU 资源配额失败: %w", err)
	}

	// 循环处理每个集群
	for _, clusterNs := range clusterNamespaces {
		err = kueue.UpdateClusterKueueResources(ctx, teamId, clusterNs.Cluster, clusterNs.Namespaces, quotas)
		if err != nil {
			log.L.WithName("updateKueueResources").Errorf(ctx, "update cluster %s kueue resources error: %s", clusterNs.Cluster, err.Error())
			return err
		}
	}

	return nil
}

// deleteKueueResources 删除 Kueue 资源
func deleteKueueResources(
	ctx context.Context, teamId int, clusterNamespace *dto.ClusterNamespace, clusterNamespaces []*dto.ClusterNamespace,
) error {
	// 删除 LocalQueue
	for _, namespace := range clusterNamespace.Namespaces {
		// 删除训练任务类型的 LocalQueue
		err := kueue.DeleteLocalQueue(ctx, clusterNamespace.Cluster, namespace, teamId, kueue.TrainTaskIndex)
		if err != nil {
			log.L.WithName("deleteKueueResources").Errorf(ctx, "delete train task localqueue error: %s", err.Error())
			return err
		}

		// 删除在线开发类型的 LocalQueue
		err = kueue.DeleteLocalQueue(ctx, clusterNamespace.Cluster, namespace, teamId, kueue.OnlineDevelopmentIndex)
		if err != nil {
			log.L.WithName("deleteKueueResources").Errorf(ctx, "delete online development localqueue error: %s", err.Error())
			return err
		}

		// 禁用 Namespace 成员标签
		err = kueue.DisabledNamespaceLabel(ctx, clusterNamespace.Cluster, namespace)
		if err != nil {
			log.L.WithName("deleteKueueResources").Errorf(ctx, "disabled namespace label error: %s", err.Error())
			return err
		}
	}

	// 删除 ClusterQueue
	needDelete := true
	for _, clusterNs := range clusterNamespaces {
		if clusterNs.Cluster == clusterNamespace.Cluster {
			needDelete = false
			break
		}
	}

	if needDelete {
		err := client.DeleteClusterQueue(ctx, clusterNamespace.Cluster, kueue.ClusterQueuePrefix+strconv.Itoa(teamId))
		if err != nil {
			log.L.WithName("deleteKueueResources").Errorf(ctx, "delete clusterqueue error: %s", err.Error())
			return err
		}
	}

	return nil
}
