package middleware

import (
	"context"
	"encoding/json"
	"fmt"
	"mlops/internal/model/dto"
	"mlops/internal/service"
	"net/http"
	"strings"

	"github.com/gogf/gf/v2/text/gstr"
	"gitlab.ttyuyin.com/yunwei-infra/tt-cloud-constack-sdk/pkg/log"

	"github.com/gogf/gf/v2/errors/gcode"

	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/net/ghttp"
)

type sMiddleware struct {
}

func init() {
	service.RegisterMiddleware(newsMiddleware())
}

func newsMiddleware() *sMiddleware {
	return &sMiddleware{}
}

func (this *sMiddleware) JwtAuth(r *ghttp.Request) {
	service.JwtAuth().Do().MiddlewareFunc()(r)
	r.Middleware.Next()
}

func (this *sMiddleware) CheckUserIsActive(r *ghttp.Request) {
	ctx := r.GetCtx()
	payload := service.JwtAuth().Do().GetPayload(ctx)

	//log.L.WithName("sMiddleware.CheckUserIsActive").Infof(ctx, "payload:%s", payload)
	ctxUser := &dto.ContextUser{}
	if err := json.Unmarshal([]byte(payload), ctxUser); err != nil {
		r.SetError(fmt.Errorf("CheckUserIsActive json unmarshal error, err: %s", err.Error()))
		r.ExitAll()
		return
	}

	if err := service.User().CheckIsActive(ctx, int(ctxUser.Id)); err != nil {
		r.SetError(fmt.Errorf("CheckUserIsActive error, err: %s", err.Error()))
		r.ExitAll()
		return
	}
	service.BizCtx().Init(r, &dto.Context{
		User: ctxUser,
		Data: make(g.Map),
	})

	this.CheckApiV1GlobalRole(ctx, r, int(ctxUser.Id))

	r.Middleware.Next()
}

func (this *sMiddleware) CheckApiV1GlobalRole(ctx context.Context, r *ghttp.Request, userId int) {
	uri := strings.Split(gstr.TrimLeftStr(r.RequestURI, "/api/v1"), "?")[0]

	if service.PlatFormAuth().GlobalCheck(ctx, userId, uri) {
		r.Middleware.Next()
		return
	}
	r.SetError(gerror.NewCode(gcode.CodeNotAuthorized, fmt.Sprintf("没有使用该api的权限")))
	r.ExitAll()

}

func errorLog(err error) {
	switch {
	case strings.Contains(err.Error(), "websocket: close"):
		log.L.WithName("sMiddleware.errorLog").Warningf(context.TODO(), `%s`, err.Error())
		return
	case strings.Contains(err.Error(), "closed network connection"):
		log.L.WithName("sMiddleware.errorLog").Warningf(context.TODO(), `%s`, err.Error())
	case strings.Contains(err.Error(), "no permission"):
		log.L.WithName("sMiddleware.errorLog").Warningf(context.TODO(), "%s", err.Error())

	default:
		log.L.WithName("errorLog").Warning(context.TODO(), err.Error())
	}
}

func (this *sMiddleware) ErrorRespHandler(r *ghttp.Request) {
	r.Middleware.Next()
	if r.Response.Status >= http.StatusInternalServerError {
		err := r.GetError()
		res := ghttp.DefaultHandlerResponse{
			Code:    gerror.Code(err).Code(),
			Message: err.Error(),
			Data:    nil,
		}
		r.Response.ClearBuffer()
		r.Response.WriteJson(res)
	}
}

func (this *sMiddleware) MiddlewareHandlerResponse(r *ghttp.Request) {
	r.Middleware.Next()

	// There's custom buffer content, it then exits current handler.
	if r.Response.BufferLength() > 0 {
		return
	}

	var (
		msg  string
		err  = r.GetError()
		res  = r.GetHandlerResponse()
		code = gerror.Code(err)
	)
	if !(r.Response.Status > 0 && r.Response.Status != http.StatusOK) {
		code = gcode.CodeOK
	}
	if err != nil {
		msg = err.Error()
		errorLog(err)

		switch gerror.Code(err) {
		case gcode.CodeValidationFailed:
			// 400 Bad Request
			code = gcode.CodeValidationFailed
			r.Response.Status = http.StatusBadRequest

		case gcode.CodeNotAuthorized:
			// 403 Forbidden
			code = gcode.CodeNotAuthorized
			r.Response.Status = http.StatusForbidden

		case gcode.CodeNotFound:
			// 404 Not Found
			code = gcode.CodeNotFound
			r.Response.Status = http.StatusNotFound

		default:
			// 500 Internal Server Error
			code = gcode.CodeInternalError
			r.Response.Status = http.StatusInternalServerError
		}
	}

	r.Response.WriteJson(ghttp.DefaultHandlerResponse{
		Code:    code.Code(),
		Message: msg,
		Data:    res,
	})

}

func (this *sMiddleware) CheckOpenAPIToken(r *ghttp.Request) {

	if bearer := r.Header.Get("Authorization"); bearer != "" {
		service.JwtAuth().Do().MiddlewareFunc()(r)
	} else {
		token := r.Header.Get("X-TOKEN")
		if token == "" {
			r.SetError(gerror.NewCode(gcode.CodeNotAuthorized, fmt.Sprintf("CheckOpenAPIToken error, err: not found token.")))
			r.Response.Status = http.StatusForbidden
			r.ExitAll()
			return
		}
		if _, err := service.OpenApiToken().CheckAuth(r.GetCtx(), token); err != nil {
			r.SetError(gerror.NewCode(gcode.CodeNotAuthorized, fmt.Sprintf("CheckOpenAPIToken error, err: not available token.")))
			r.Response.Status = http.StatusForbidden
			r.ExitAll()
			return
		}
	}

	r.Middleware.Next()
}
