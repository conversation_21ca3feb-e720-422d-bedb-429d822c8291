package common

import (
	"context"
	"encoding/json"
	"fmt"
	"github.com/gogf/gf/v2/frame/g"
	"mlops/internal/consts"
	"mlops/internal/dao"
	"mlops/internal/model/entity"
	"mlops/internal/service"
	"mlops/tools"
	"strconv"
	"strings"
)

type sSetting struct {
}

func init() {
	service.RegisterSetting(newsSetting())
}

func newsSetting() *sSetting {
	return &sSetting{}
}

func (this *sSetting) Set(ctx context.Context, k string, v interface{}, desc string, category ...string) (err error) {
	cate := tools.DefaultKeyWordParams[string](consts.CateSystem, category...)
	_, err = dao.Setting.Ctx(ctx).Data(g.Map{
		"key":      k,
		"value":    v,
		"category": cate,
		"desc":     desc,
	}).Save()
	return err
}

func (this *sSetting) ListWithCategory(ctx context.Context, category string) (res []entity.Setting, err error) {
	res = make([]entity.Setting, 0)
	err = dao.Setting.Ctx(ctx).Where(g.Map{"category": category}).Scan(&res)
	return
}

func (this *sSetting) Get(ctx context.Context, k string, category ...string) (setting *entity.Setting, err error) {
	cate := tools.DefaultKeyWordParams[string](consts.CateSystem, category...)
	setting = &entity.Setting{}
	err = dao.Setting.Ctx(ctx).Where(g.Map{"key": k, "category": cate}).Scan(setting)
	if err != nil {
		err = fmt.Errorf("settting config get error, err: %s", err.Error())
	}
	return setting, nil
}

func (this *sSetting) GetVal(ctx context.Context, k string, category ...string) (val string, err error) {
	s, err := this.Get(ctx, k, category...)
	if err != nil {
		return "", fmt.Errorf("get setting key:%s . %s", k, err.Error())
	}
	return s.Value, nil
}

func (this *sSetting) GetValMap(ctx context.Context, k string, category ...string) (map[string]interface{}, error) {
	s, err := this.Get(ctx, k, category...)
	if err != nil {
		return nil, err
	}
	m := make(map[string]interface{}, 0)
	err = json.Unmarshal([]byte(s.Value), &m)
	return m, err
}

func (this *sSetting) GetValSliceString(ctx context.Context, k string, category ...string) ([]string, error) {
	slice := make([]string, 0)
	s, err := this.Get(ctx, k, category...)
	if err != nil {
		return nil, err
	}
	err = json.Unmarshal([]byte(s.Value), &slice)
	return slice, err
}

func (this *sSetting) GetValSliceInt(ctx context.Context, k string, category ...string) ([]int, error) {
	slice := make([]int, 0)
	s, err := this.Get(ctx, k, category...)
	if err != nil {
		return nil, err
	}
	err = json.Unmarshal([]byte(s.Value), &slice)
	return slice, err
}

func (this *sSetting) GetValInt(ctx context.Context, k string, category ...string) (int, error) {
	s, err := this.Get(ctx, k, category...)
	if err != nil {
		return 0, err
	}
	i, err := strconv.Atoi(s.Value)
	return i, err
}

func (this *sSetting) GetValFloat(ctx context.Context, k string, category ...string) (float64, error) {
	s, err := this.Get(ctx, k, category...)
	if err != nil {
		return 0, err
	}
	f, err := strconv.ParseFloat(s.Value, 64)
	return f, err
}

func (this *sSetting) GetValSliceByString(ctx context.Context, k string, category ...string) ([]string, error) {
	s, err := this.Get(ctx, k, category...)
	if err != nil {
		return nil, fmt.Errorf("get setting key:%s . %s", k, err.Error())
	}

	return strings.Split(s.Value, ","), nil
}
