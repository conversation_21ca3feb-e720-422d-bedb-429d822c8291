package team

import (
	"context"
	"github.com/gogf/gf/v2/frame/g"
	"mlops/internal/service"
	"strconv"

	"mlops/api/team/v1"
)

func (c *ControllerV1) DeleteGpuQuota(ctx context.Context, req *v1.DeleteGpuQuotaReq) (res *v1.DeleteGpuQuotaRes, err error) {
	r := g.RequestFromCtx(ctx).GetRouterMap()["teamId"]
	teamId, err := strconv.Atoi(r)
	if err != nil {
		return nil, err
	}

	err = service.Team().DeleteGPUQuota(ctx, teamId, req.GpuType)

	return
}
