package team

import (
	"context"
	"github.com/gogf/gf/v2/frame/g"
	"mlops/api/team/v1"
	"mlops/internal/model/dto"
	"mlops/internal/service"
	"strconv"
)

func (c *ControllerV1) DeleteClusterNamespace(ctx context.Context, req *v1.DeleteClusterNamespaceReq) (res *v1.DeleteClusterNamespaceRes, err error) {
	r := g.RequestFromCtx(ctx).GetRouterMap()["teamId"]
	teamId, err := strconv.Atoi(r)
	if err != nil {
		return nil, err
	}

	err = service.Team().DeleteClusterNamespace(ctx, teamId, &dto.ClusterNamespace{
		Cluster:    req.Cluster,
		Namespaces: []string{req.Namespace},
	})

	return
}
