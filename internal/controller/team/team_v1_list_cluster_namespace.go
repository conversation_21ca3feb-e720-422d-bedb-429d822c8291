package team

import (
	"context"
	"mlops/api/team/v1"
	"mlops/internal/model/dto"
	"mlops/internal/service"
)

func (c *ControllerV1) ListClusterNamespace(ctx context.Context, req *v1.ListClusterNamespaceReq) (res *v1.ListClusterNamespaceRes, err error) {
	res = &v1.ListClusterNamespaceRes{
		ClusterNamespaces: make([]*dto.ClusterNamespace, 0),
	}
	res.ClusterNamespaces, err = service.Team().ListClusterNamespace(ctx, req.TeamId)

	return
}
