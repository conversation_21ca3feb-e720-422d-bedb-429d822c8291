package team

import (
	"context"
	"github.com/gogf/gf/v2/frame/g"
	"mlops/internal/service"

	"mlops/api/team/v1"
)

func (c *ControllerV1) ListTeamUser(ctx context.Context, req *v1.ListTeamUserReq) (res *v1.ListTeamUserRes, err error) {
	res = &v1.ListTeamUserRes{}
	result, err := service.Team().ListTeamUser(ctx, g.RequestFromCtx(ctx).Get("pid").Int())
	if err != nil {
		return nil, err
	}

	res.Users = result

	return
}
