package team

import (
	"context"
	"mlops/internal/model/dto"
	"mlops/internal/service"

	"mlops/api/team/v1"
)

func (c *ControllerV1) ListTeam(ctx context.Context, req *v1.ListTeamReq) (res *v1.ListTeamRes, err error) {
	res = &v1.ListTeamRes{}

	list, err := service.Team().List(ctx)
	if err != nil {
		return nil, err
	}

	res.Teams = make([]*dto.Team, 0, len(list))
	for _, team := range list {
		res.Teams = append(res.Teams, &dto.Team{
			Id:       team.Id,
			Name:     team.Name,
			TeamId:   team.TeamId,
			Category: team.Category,
		})
	}

	return res, nil
}
