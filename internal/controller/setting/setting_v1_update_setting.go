package setting

import (
	"context"
	"github.com/gogf/gf/v2/frame/g"
	"mlops/internal/dao"
	"mlops/tools/logic"

	"mlops/api/setting/v1"
)

func (c *ControllerV1) UpdateSetting(ctx context.Context, req *v1.UpdateSettingReq) (res *v1.UpdateSettingRes, err error) {
	res = &v1.UpdateSettingRes{}
	r := g.RequestFromCtx(ctx)
	_, err = logic.NewSimpleUpdate(dao.Setting.Table()).Update(ctx, r.Get("pid").Int(), req)
	if err != nil {
		return nil, err
	}

	return
}
