package setting

import (
	"context"
	"github.com/gogf/gf/v2/frame/g"
	"mlops/internal/dao"
	"mlops/tools/logic"

	"mlops/api/setting/v1"
)

func (c *ControllerV1) DeleteSetting(ctx context.Context, req *v1.DeleteSettingReq) (res *v1.DeleteSettingRes, err error) {
	res = &v1.DeleteSettingRes{}
	_, err = logic.NewSimpleDelete(dao.Setting.Table()).Delete(ctx, g.RequestFromCtx(ctx).Get("pid").Int())
	if err != nil {
		return nil, err
	}

	return
}
