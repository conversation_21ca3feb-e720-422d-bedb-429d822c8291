package setting

import (
	"context"
	"github.com/gogf/gf/v2/frame/g"
	"mlops/internal/dao"
	"mlops/internal/model/entity"
	"mlops/tools/logic"

	"mlops/api/setting/v1"
)

func (c *ControllerV1) GetSetting(ctx context.Context, req *v1.GetSettingReq) (res *v1.GetSettingRes, err error) {
	r := g.RequestFromCtx(ctx)
	s := &entity.Setting{}
	get, err := logic.NewSimpleGet(dao.Setting.Table()).Get(ctx, r.Get("pid").Int(), s)
	if err != nil {
		return nil, err
	}
	res = &v1.GetSettingRes{
		Data: get.Data,
	}

	return
}
