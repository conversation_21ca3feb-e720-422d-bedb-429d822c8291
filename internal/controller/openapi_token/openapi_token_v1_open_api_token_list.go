package openapi_token

import (
	"context"
	"mlops/api/openapi_token/v1"
	"mlops/internal/dao"
	"mlops/tools/logic"
)

func (c *ControllerV1) OpenApiTokenList(ctx context.Context, req *v1.OpenApiTokenListReq) (res *v1.OpenApiTokenListRes, err error) {
	res = &v1.OpenApiTokenListRes{}
	listRes, err := logic.NewSimpleList(dao.OpenapiToken.Table()).List(ctx, req.ListReq)
	if err != nil {
		return nil, err
	}

	res.List = listRes.List
	res.Total = listRes.Total
	res.Page = listRes.Page
	res.Size = listRes.Size
	return
}
