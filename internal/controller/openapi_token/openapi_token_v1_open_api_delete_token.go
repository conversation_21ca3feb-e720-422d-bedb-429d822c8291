package openapi_token

import (
	"context"
	"github.com/gogf/gf/v2/frame/g"
	"mlops/internal/dao"
	"mlops/tools/logic"

	"mlops/api/openapi_token/v1"
)

func (c *ControllerV1) OpenApiDeleteToken(ctx context.Context, req *v1.OpenApiDeleteTokenReq) (res *v1.OpenApiDeleteTokenRes, err error) {
	_, err = logic.NewSimpleDelete(dao.OpenapiToken.Table()).Delete(ctx, g.RequestFromCtx(ctx).Get("pid").Int())
	if err != nil {
		return nil, err
	}

	return &v1.OpenApiDeleteTokenRes{}, nil
}
