package sso

import (
	"context"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gcfg"
	"github.com/gogf/gf/v2/util/grand"
	"github.com/pkg/errors"
	"gitlab.ttyuyin.com/yunwei-infra/tt-cloud-constack-sdk/pkg/log"
	"gitlab.ttyuyin.com/yunwei-infra/tt-cloud-constack-sdk/pkg/utils/constack/tools"
	"mlops/internal/consts"
	constsErr "mlops/internal/consts/errors"
	"mlops/internal/model/dto"
	"mlops/internal/model/entity"
	"mlops/internal/service"
	"mlops/tools/client"
	toolsErr "mlops/tools/errors"
	"net/http"
	"time"

	"mlops/api/sso/v1"
)

func (c *ControllerV1) SsoLogin(ctx context.Context, req *v1.SsoLoginReq) (res *v1.SsoLoginRes, err error) {
	r := g.RequestFromCtx(ctx)

	config := g.Cfg("sso-config").GetAdapter().(*gcfg.AdapterFile)
	config.SetFileName("sso-config.yaml")
	privateKey, err := config.Get(ctx, "data.private_key")

	originTicket := r.GetParam("ticket", req.Ticket).String()
	log.L.WithName("cSso.Login").Infof(ctx, "origin ticket:%s", originTicket)

	username, err := client.Opssso.Validate(ctx, originTicket, privateKey.(string))
	if err != nil {
		return nil, err
	}

	if username == "" {
		return nil, toolsErr.ValidateError("sso 认证失败")
	}

	// 注册
	cmdbUserInfo, err := client.CmdbApiJsonRpc.GetUserInfoByUsername(ctx, username)
	if err != nil {
		return nil, err
	}

	userInfo, err := service.User().GetUserByUsername(ctx, username)
	if err != nil {
		if !errors.Is(err, constsErr.ErrUserNotFound) {
			return nil, err
		}
		digits := grand.Digits(8)
		encryptPass, err := tools.EncryptData(digits, consts.DefaultCipherKey)
		if err != nil {
			return nil, err
		}

		err = service.User().CreateUser(ctx, &entity.User{
			Uid:        cmdbUserInfo.UserId,
			Username:   cmdbUserInfo.Name,
			NickName:   cmdbUserInfo.RealName,
			Email:      cmdbUserInfo.Email,
			EmployeeNo: cmdbUserInfo.UserNo,
			Password:   encryptPass,
			IsActive:   consts.STrue,
		})
		if err != nil {
			return nil, err
		}

		// reset
		userInfo, err = service.User().GetUserByUsername(ctx, username)
		if err != nil {
			return nil, err
		}
	}

	if userInfo.Id == 0 {
		return nil, constsErr.ErrUserNotFound
	}

	err = service.PlatFormAuth().SyncUserTeam(ctx, int(userInfo.Id))
	if err != nil {
		return nil, err
	}

	cipherPass, err := tools.DecryptCipher(userInfo.Password, consts.DefaultCipherKey)
	if err != nil {
		return nil, err
	}

	r.SetCtxVar(consts.SSO, dto.UserLoginInput{
		Username: userInfo.Username,
		Password: cipherPass,
	})

	token, expire := service.JwtAuth().Do().LoginHandler(ctx)

	r.Cookie.SetCookie(consts.MlopsCookieKey, token, "*", "/", expire.Sub(time.Now()))
	r.Response.RedirectTo("/", http.StatusFound)
	return res, nil
}
