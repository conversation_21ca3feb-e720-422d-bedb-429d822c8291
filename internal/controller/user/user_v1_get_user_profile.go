package user

import (
	"context"
	"github.com/gogf/gf/v2/frame/g"
	"mlops/internal/service"

	"mlops/api/user/v1"
)

func (c *ControllerV1) GetUserProfile(ctx context.Context, req *v1.GetUserProfileReq) (res *v1.GetUserProfileRes, err error) {
	res = &v1.GetUserProfileRes{}
	profile, err := service.User().Profile(ctx, g.RequestFromCtx(ctx).Get("uid").Int())
	if err != nil {
		return nil, err
	}
	res.UserProfile = profile
	return
}
