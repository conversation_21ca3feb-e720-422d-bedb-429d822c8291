package user

import (
	"context"
	"gitlab.ttyuyin.com/yunwei-infra/tt-cloud-constack-sdk/pkg/utils/page"
	"mlops/internal/service"
	apiv1 "mlops/tools/common"

	"mlops/api/user/v1"
)

func (c *ControllerV1) ListUser(ctx context.Context, req *v1.ListUserReq) (res *v1.ListUserRes, err error) {
	res = &v1.ListUserRes{
		ListPageRes: &apiv1.ListPageRes{
			Page: req.Page,
			Size: req.<PERSON>ze,
		},
	}
	objs, err := service.User().ListUserViewObj(ctx, req.Search)
	if err != nil {
		return nil, err
	}

	pager := page.NewPager(objs)
	res.Total = len(objs)
	res.ListPageRes.List = pager.Offset(req.Page).Limit(req.Size).List()

	return
}
