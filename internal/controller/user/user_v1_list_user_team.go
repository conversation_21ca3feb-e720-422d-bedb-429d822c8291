package user

import (
	"context"
	"github.com/gogf/gf/v2/frame/g"
	authConsts "mlops/internal/consts/auth"
	"mlops/internal/model/dto"
	"mlops/internal/service"

	"mlops/api/user/v1"
)

func (c *ControllerV1) ListUserTeam(ctx context.Context, req *v1.ListUserTeamReq) (res *v1.ListUserTeamRes, err error) {
	res = &v1.ListUserTeamRes{}
	targetTeams := make([]dto.TeamRole, 0)
	targetMaps := make(map[string]dto.TeamRole)

	profile, err := service.PlatFormAuth().GetUserAuthProfile(ctx, g.RequestFromCtx(ctx).Get("pid").Int())
	if err != nil {
		return nil, err
	}

	for _, teamRole := range profile.TeamRoles {
		targetMaps[teamRole.TeamId] = teamRole
	}

	lists, err := service.Team().List(ctx)
	if err != nil {
		return nil, err
	}
	for _, list := range lists {
		if _, ok := targetMaps[list.TeamId]; ok {
			targetTeams = append(targetTeams, targetMaps[list.TeamId])
		} else {
			targetTeams = append(targetTeams, dto.TeamRole{
				Id:       int(list.Id),
				TeamId:   list.TeamId,
				TeamName: list.Name,
				TeamType: authConsts.TeamFeature,
				Role:     "",
			})
		}

	}

	res.Teams = targetTeams

	return
}
