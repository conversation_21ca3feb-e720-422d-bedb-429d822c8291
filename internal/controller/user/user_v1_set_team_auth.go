package user

import (
	"context"
	"mlops/internal/consts/auth"
	"mlops/internal/service"
	"mlops/tools/errors"

	"mlops/api/user/v1"
)

func (c *ControllerV1) SetTeamAuth(ctx context.Context, req *v1.SetTeamAuthReq) (res *v1.SetTeamAuthRes, err error) {
	res = &v1.SetTeamAuthRes{}

	// check user is admin
	if !(service.PlatFormAuth().CheckAdmin(ctx, int(service.BizCtx().Get(ctx).User.Id)) ||
		service.PlatFormAuth().CheckTeamAuth(ctx, int(service.BizCtx().Get(ctx).User.Id),
			req.TeamId, []string{string(auth.RoleTeamAdmin)})) {
		return nil, errors.ForbiddenError("没有当前团队权限")
	}

	switch req.TeamRoleKind {
	case auth.TeamRoleAdmin:
		err = service.PlatFormAuth().BindTeamRole(ctx, req.UserId, req.TeamId, auth.TeamRoleAdmin)
		if err != nil {
			return nil, err
		}

	case auth.TeamRoleUser:
		err = service.PlatFormAuth().BindTeamRole(ctx, req.UserId, req.TeamId, auth.TeamRoleUser)
		if err != nil {
			return nil, err
		}
	}
	return
}
