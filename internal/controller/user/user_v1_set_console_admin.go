package user

import (
	"context"
	"mlops/internal/service"

	"mlops/api/user/v1"
)

func (c *ControllerV1) SetConsoleAdmin(ctx context.Context, req *v1.SetConsoleAdminReq) (res *v1.SetConsoleAdminRes, err error) {
	res = &v1.SetConsoleAdminRes{}
	if req.IsConsoleAdmin {
		err = service.PlatFormAuth().BindConsoleAdmin(ctx, req.UserId)
		if err != nil {
			return nil, err
		}
		return res, nil

	}

	err = service.PlatFormAuth().UnBindConsoleAdmin(ctx, req.UserId)
	if err != nil {
		return nil, err
	}
	return
}
