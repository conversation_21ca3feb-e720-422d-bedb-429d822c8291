package user

import (
	"context"
	"gitlab.ttyuyin.com/yunwei-infra/tt-cloud-constack-sdk/pkg/log"
	"mlops/internal/service"

	"mlops/api/user/v1"
)

func (c *ControllerV1) SyncTeam(ctx context.Context, req *v1.SyncTeamReq) (res *v1.SyncTeamRes, err error) {
	res = &v1.SyncTeamRes{}
	err = service.Team().Sync(ctx)
	if err != nil {
		return nil, err
	}

	userDtos, err := service.User().ListUserViewObj(ctx, "")
	if err != nil {
		return nil, err
	}

	for _, userInfo := range userDtos {
		err = service.PlatFormAuth().SyncUserTeam(ctx, int(userInfo.Id))
		if err != nil {
			log.L.WithName("cUser.SyncTeam").Errorf(ctx, "sync user team error: %s", err)
		}
	}
	return
}
