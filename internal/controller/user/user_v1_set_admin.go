package user

import (
	"context"
	"mlops/internal/service"

	"mlops/api/user/v1"
)

func (c *ControllerV1) SetAdmin(ctx context.Context, req *v1.SetAdminReq) (res *v1.SetAdminRes, err error) {
	res = &v1.SetAdminRes{}
	if req.IsAdmin {
		err = service.PlatFormAuth().BindAdmin(ctx, req.UserId)
		if err != nil {
			return nil, err
		}
		return res, nil

	}

	err = service.PlatFormAuth().UnBindAdmin(ctx, req.UserId)
	if err != nil {
		return nil, err
	}

	return
}
