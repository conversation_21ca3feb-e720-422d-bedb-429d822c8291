package traintask

import (
	"context"
	"mlops/tools/client"

	"mlops/api/traintask/v1"
)

func (c *ControllerV1) ListPvcWithClusterNamespace(ctx context.Context, req *v1.ListPvcWithClusterNamespaceReq) (res *v1.ListPvcWithClusterNamespaceRes, err error) {
	res = &v1.ListPvcWithClusterNamespaceRes{}
	pvcs, err := client.ListPvc(ctx, req.ClusterName, req.Namespace)
	if err != nil {
		return nil, err
	}

	for _, pvc := range pvcs {
		res.PvcNames = append(res.PvcNames, pvc.GetName())
	}

	return
}
