package traintask

import (
	"context"
	"mlops/tools/client"

	"mlops/api/traintask/v1"
)

func (c *ControllerV1) ListConfigmapWithClusterNamespace(ctx context.Context, req *v1.ListConfigmapWithClusterNamespaceReq) (res *v1.ListConfigmapWithClusterNamespaceRes, err error) {
	res = &v1.ListConfigmapWithClusterNamespaceRes{}
	configMaps, err := client.ListConfigmap(ctx, req.ClusterName, req.Namespace)
	if err != nil {
		return nil, err
	}

	for _, configMap := range configMaps {
		res.ConfigmapNames = append(res.ConfigmapNames, configMap.GetName())
	}

	return
}
