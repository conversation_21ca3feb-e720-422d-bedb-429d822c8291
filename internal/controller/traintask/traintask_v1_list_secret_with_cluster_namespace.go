package traintask

import (
	"context"
	"mlops/tools/client"

	"mlops/api/traintask/v1"
)

func (c *ControllerV1) ListSecretWithClusterNamespace(ctx context.Context, req *v1.ListSecretWithClusterNamespaceReq) (res *v1.ListSecretWithClusterNamespaceRes, err error) {
	res = &v1.ListSecretWithClusterNamespaceRes{}
	secrets, err := client.ListSecret(ctx, req.ClusterName, req.Namespace)
	if err != nil {
		return nil, err
	}

	for _, secret := range secrets {
		res.SecretNames = append(res.SecretNames, secret.GetName())
	}

	return
}
