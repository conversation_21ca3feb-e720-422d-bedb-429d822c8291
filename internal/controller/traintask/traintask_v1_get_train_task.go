package traintask

import (
	"context"
	"strconv"

	"mlops/api/traintask/v1"
	"mlops/internal/service"

	"github.com/gogf/gf/v2/frame/g"
)

func (c *ControllerV1) GetTrainTask(ctx context.Context, req *v1.GetTrainTaskReq) (res *v1.GetTrainTaskRes, err error) {
	res = &v1.GetTrainTaskRes{}
	r := g.RequestFromCtx(ctx).GetRouterMap()["id"]
	idInt, err := strconv.Atoi(r)
	if err != nil {
		return nil, err
	}
	trainTask, err := service.TrainTask().Get(ctx, uint(idInt))
	if err != nil {
		return nil, err
	}
	res.Data = trainTask
	return
}
