package traintask

import (
	"context"

	"mlops/api/traintask/v1"
	"mlops/internal/service"
)

func (c *ControllerV1) ListTrainTask(ctx context.Context, req *v1.ListTrainTaskReq) (res *v1.ListTrainTaskRes, err error) {
	res = &v1.ListTrainTaskRes{}
	list, err := service.TrainTask().ListPage(ctx, req.TrainTaskListInput)
	if err != nil {
		return nil, err
	}
	res.List = list.List
	res.Total = list.Total
	res.CurrentPage = list.CurrentPage
	res.PageSize = list.PageSize
	return
}
