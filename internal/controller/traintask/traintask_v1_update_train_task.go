package traintask

import (
	"context"
	"database/sql"
	"errors"
	"strconv"

	v1 "mlops/api/traintask/v1"
	errors2 "mlops/internal/consts/errors"
	"mlops/internal/dao"
	"mlops/internal/model/dto"
	"mlops/internal/service"

	"github.com/gogf/gf/v2/frame/g"
)

func (c *ControllerV1) UpdateTrainTask(ctx context.Context, req *v1.UpdateTrainTaskReq) (res *v1.UpdateTrainTaskRes, err error) {
	res = &v1.UpdateTrainTaskRes{}
	id := g.RequestFromCtx(ctx).GetRouterMap()["id"]
	idInt, err := strconv.Atoi(id)
	if err != nil {
		return nil, err
	}

	origin := &dto.TrainTask{}
	err = dao.TrainTask.Ctx(ctx).Where(dao.TrainTask.Columns().Id, id).Scan(origin)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, errors2.ErrTaskNotFound
		}
		return nil, err
	}

	trainTask := &dto.TrainTask{
		Id:                  uint(idInt),
		TaskName:            req.TaskName,
		ClusterName:         req.ClusterName,
		ClusterId:           req.ClusterId,
		Namespace:           req.Namespace,
		ImageUrl:            req.ImageUrl,
		StartCmd:            req.StartCmd,
		Priority:            req.Priority,
		TaskType:            req.TaskType,
		TrainingFramework:   origin.TrainingFramework,
		TaskYaml:            req.TaskYaml,
		CreatedByEmployeeNo: req.CreatedByEmployeeNo,
		CreatedByUserName:   req.CreatedByUserName,
		UpdatedByEmployeeNo: req.UpdatedByEmployeeNo,
		UpdatedByUserName:   req.UpdatedByUserName,
		ClusterResource:     req.ClusterResource,
		VolumeMounts:        req.VolumeMounts,
		EnvVarsMap:          req.EnvVarsMap,
		AppName:             req.AppName,
		CmdbId:              req.CmdbId,
	}
	err = service.TrainTask().Update(ctx, trainTask)
	if err != nil {
		return nil, err
	}
	return
}
