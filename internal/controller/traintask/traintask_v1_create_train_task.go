package traintask

import (
	"context"

	v1 "mlops/api/traintask/v1"
	"mlops/internal/model/dto"
	"mlops/internal/service"
)

func (c *ControllerV1) CreateTrainTask(ctx context.Context, req *v1.CreateTrainTaskReq) (res *v1.CreateTrainTaskRes, err error) {
	res = &v1.CreateTrainTaskRes{}

	trainTask := &dto.TrainTask{
		TaskName:            req.TaskName,
		TeamId:              req.TeamId,
		TeamName:            req.TeamName,
		ClusterName:         req.ClusterName,
		ClusterId:           req.ClusterId,
		Namespace:           req.Namespace,
		ImageUrl:            req.ImageUrl,
		StartCmd:            req.StartCmd,
		Priority:            req.Priority,
		TaskType:            req.TaskType,
		TrainingFramework:   req.TrainingFramework,
		TaskYaml:            req.TaskYaml,
		CreatedByEmployeeNo: req.CreatedByEmployeeNo,
		CreatedByUserName:   req.CreatedByUserName,
		UpdatedByEmployeeNo: req.UpdatedByEmployeeNo,
		UpdatedByUserName:   req.UpdatedByUserName,
		ClusterResource:     req.ClusterResource,
		VolumeMounts:        req.VolumeMounts,
		EnvVarsMap:          req.EnvVarsMap,
		AppName:             req.AppName,
		CmdbId:              req.CmdbId,
	}
	err = service.TrainTask().Create(ctx, trainTask)
	if err != nil {
		return nil, err
	}
	return
}
