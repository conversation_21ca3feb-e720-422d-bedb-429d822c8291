package traintaskexecution

import (
	"context"
	"strconv"

	"mlops/api/traintaskexecution/v1"
	"mlops/internal/service"

	"github.com/gogf/gf/v2/frame/g"
)

func (c *ControllerV1) InterruptTrainTaskExecution(ctx context.Context, req *v1.InterruptTrainTaskExecutionReq) (res *v1.InterruptTrainTaskExecutionRes, err error) {
	res = &v1.InterruptTrainTaskExecutionRes{}
	id := g.RequestFromCtx(ctx).GetRouterMap()["id"]
	idInt, err := strconv.Atoi(id)
	if err != nil {
		return nil, err
	}
	err = service.TrainTaskExecution().Interrupt(ctx, uint(idInt))
	if err != nil {
		return nil, err
	}
	return
}
