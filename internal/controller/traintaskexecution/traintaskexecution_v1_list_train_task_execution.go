package traintaskexecution

import (
	"context"
	"strconv"
	"strings"

	v1 "mlops/api/traintaskexecution/v1"
	"mlops/internal/consts"
	"mlops/internal/service"
	"mlops/tools/client"
	"mlops/utility/kueue"

	visibilityv1beta1 "sigs.k8s.io/kueue/apis/visibility/v1beta1"
)

func (c *ControllerV1) ListTrainTaskExecution(ctx context.Context, req *v1.ListTrainTaskExecutionReq) (res *v1.ListTrainTaskExecutionRes, err error) {
	res = &v1.ListTrainTaskExecutionRes{}
	list, err := service.TrainTaskExecution().ListPage(ctx, req.TrainTaskExecutionListInput)
	if err != nil {
		return nil, err
	}
	clusterPendingWorkloads := make(map[string][]visibilityv1beta1.PendingWorkload)
	for _, v := range list.List {
		if v.Status == consts.TrainTaskExecutionStatusPending {
			workloads, ok := clusterPendingWorkloads[v.ClusterName]
			if !ok {
				workloads, err = client.K8sApiProxy.GetPendingWorkloadsItems(ctx, &client.PendingWorkloadsRequest{
					ClusterName:      v.ClusterName,
					ClusterQueueName: kueue.ClusterQueuePrefix + strconv.Itoa(int(v.TeamId)),
				})
				if err != nil {
					return nil, err
				}
				clusterPendingWorkloads[v.ClusterName] = workloads
			}

			// 从 workloads 中查找包含 ExecutionName 的工作负载
			for _, workload := range workloads {
				if strings.Contains(workload.Name, v.ExecutionName) {
					// 找到匹配的工作负载，根据 positionInClusterQueue 计算等待时间
					v.EstimatedWaitTimeSeconds = uint((workload.PositionInClusterQueue + 1) * int32(kueue.FixedPendingTimeSeconds))
					break
				}
			}
		}
	}
	res.CurrentPage = list.CurrentPage
	res.PageSize = list.PageSize
	res.Total = list.Total
	res.List = list.List
	return
}
