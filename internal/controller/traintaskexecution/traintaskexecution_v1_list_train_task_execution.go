package traintaskexecution

import (
	"context"

	v1 "mlops/api/traintaskexecution/v1"
	"mlops/internal/consts"
	"mlops/internal/service"
)

func (c *ControllerV1) ListTrainTaskExecution(ctx context.Context, req *v1.ListTrainTaskExecutionReq) (res *v1.ListTrainTaskExecutionRes, err error) {
	res = &v1.ListTrainTaskExecutionRes{}
	list, err := service.TrainTaskExecution().ListPage(ctx, req.TrainTaskExecutionListInput)
	if err != nil {
		return nil, err
	}
	for _, v := range list.List {
		if v.Status == consts.TrainTaskExecutionStatusPending {
			v.EstimatedWaitTimeSeconds = consts.FixedEstimatedWaitTimeSeconds
		}
	}
	res.CurrentPage = list.CurrentPage
	res.PageSize = list.PageSize
	res.Total = list.Total
	res.List = list.List
	return
}
