package traintaskexecution

import (
	"context"
	"encoding/json"
	"strconv"

	v1 "mlops/api/traintaskexecution/v1"
	"mlops/internal/dao"
	"mlops/internal/model/dto"

	"github.com/gogf/gf/v2/frame/g"
)

func (c *ControllerV1) ListTrainTaskExecutionWorkers(ctx context.Context, req *v1.ListTrainTaskExecutionWorkersReq) (res *v1.ListTrainTaskExecutionWorkersRes, err error) {
	res = &v1.ListTrainTaskExecutionWorkersRes{}
	id := g.RequestFromCtx(ctx).GetRouterMap()["id"]
	idInt, err := strconv.Atoi(id)
	if err != nil {
		return nil, err
	}

	trainTaskExecution := &dto.TrainTaskExecution{}
	err = dao.TrainTaskExecution.Ctx(ctx).Where(dao.TrainTaskExecution.Columns().Id, idInt).Scan(trainTaskExecution)
	if err != nil {
		return nil, err
	}

	if len(trainTaskExecution.Workers) > 0 {
		var workers []string
		err = json.Unmarshal([]byte(trainTaskExecution.Workers), &workers)
		if err != nil {
			return nil, err
		}
		res.Workers = workers
	}

	return
}
