package traintaskexecution

import (
	"context"
	"strconv"

	v1 "mlops/api/traintaskexecution/v1"
	"mlops/internal/consts"
	"mlops/internal/dao"
	"mlops/internal/model/dto"

	"github.com/gogf/gf/v2/frame/g"
)

func (c *ControllerV1) ListTrainTaskExecutionConditions(ctx context.Context, req *v1.ListTrainTaskExecutionConditionsReq) (res *v1.ListTrainTaskExecutionConditionsRes, err error) {
	res = &v1.ListTrainTaskExecutionConditionsRes{}
	id := g.RequestFromCtx(ctx).GetRouterMap()["id"]
	idInt, err := strconv.Atoi(id)
	if err != nil {
		return nil, err
	}

	trainTaskExecution := &dto.TrainTaskExecution{}
	err = dao.TrainTaskExecution.Ctx(ctx).Where(dao.TrainTaskExecution.Columns().Id, idInt).Scan(trainTaskExecution)
	if err != nil {
		return nil, err
	}

	operatorName := trainTaskExecution.TriggeredByUserName
	operatorAccount := trainTaskExecution.TriggeredByEmployeeNo
	switch trainTaskExecution.Status {
	case consts.TrainTaskExecutionStatusSucceeded, consts.TrainTaskExecutionStatusFailed, consts.TrainTaskExecutionStatusCancelled:
		operatorName := trainTaskExecution.TriggeredByUserName
		operatorAccount := trainTaskExecution.TriggeredByEmployeeNo
		if trainTaskExecution.Status == consts.TrainTaskExecutionStatusCancelled {
			operatorName = trainTaskExecution.CancelledByUserName
			operatorAccount = trainTaskExecution.CancelledByEmployeeNo
		}
		res.Conditions = append(res.Conditions, dto.TrainTaskExecutionCondition{
			Status:          trainTaskExecution.Status,
			Timestamp:       trainTaskExecution.EndTime,
			OperatorName:    operatorName,
			OperatorAccount: operatorAccount,
		})
		fallthrough
	case consts.TrainTaskExecutionStatusRunning:
		if trainTaskExecution.StartTime != nil {
			startTime := trainTaskExecution.StartTime
			if startTime.Before(trainTaskExecution.CreatedAt) {
				startTime = trainTaskExecution.CreatedAt
			}
			res.Conditions = append(res.Conditions, dto.TrainTaskExecutionCondition{
				Status:          consts.TrainTaskExecutionStatusRunning,
				Timestamp:       startTime,
				OperatorName:    operatorName,
				OperatorAccount: operatorAccount,
			})
		}
		fallthrough
	case consts.TrainTaskExecutionStatusPending:
		res.Conditions = append(res.Conditions, dto.TrainTaskExecutionCondition{
			Status:          consts.TrainTaskExecutionStatusPending,
			Timestamp:       trainTaskExecution.CreatedAt,
			OperatorName:    operatorName,
			OperatorAccount: operatorAccount,
		})
		fallthrough
	default:
		res.Conditions = append(res.Conditions, dto.TrainTaskExecutionCondition{
			Status:          consts.TrainTaskExecutionStatusSubmitted,
			Timestamp:       trainTaskExecution.TriggerTime,
			OperatorName:    operatorName,
			OperatorAccount: operatorAccount,
		})
	}

	return
}
