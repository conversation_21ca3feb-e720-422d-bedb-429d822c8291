package traintaskexecution

import (
	"context"
	v1 "mlops/api/traintaskexecution/v1"
	"mlops/tools/client"
	"mlops/utility/kueue"
	"strconv"
	"strings"

	"mlops/internal/consts"
	"mlops/internal/dao"
	"mlops/internal/model/dto"

	"github.com/gogf/gf/v2/frame/g"
)

func (c *ControllerV1) GetTrainTaskExecution(ctx context.Context, req *v1.GetTrainTaskExecutionReq) (res *v1.GetTrainTaskExecutionRes, err error) {
	res = &v1.GetTrainTaskExecutionRes{}
	trainTaskExecution := &dto.TrainTaskExecution{}
	r := g.RequestFromCtx(ctx).GetRouterMap()
	id := r["id"]
	idInt, err := strconv.Atoi(id)
	if err != nil {
		return nil, err
	}
	err = dao.TrainTaskExecution.Ctx(ctx).Where(dao.TrainTaskExecution.Columns().Id, idInt).Scan(trainTaskExecution)
	if err != nil {
		return nil, err
	}
	if trainTaskExecution.Status == consts.TrainTaskExecutionStatusPending {
		workloads, err := client.K8sApiProxy.GetPendingWorkloadsItems(ctx, &client.PendingWorkloadsRequest{
			ClusterName:      trainTaskExecution.ClusterName,
			ClusterQueueName: kueue.ClusterQueuePrefix + strconv.Itoa(int(trainTaskExecution.TeamId)),
		})
		if err != nil {
			return nil, err
		}
		// 从 workloads 中查找包含 ExecutionName 的工作负载
		for _, workload := range workloads {
			if strings.Contains(workload.Name, trainTaskExecution.ExecutionName) {
				// 找到匹配的工作负载，根据 positionInClusterQueue 计算等待时间
				trainTaskExecution.EstimatedWaitTimeSeconds = uint((workload.PositionInClusterQueue + 1) * int32(kueue.FixedPendingTimeSeconds))
				break
			}
		}
	}
	res.Data = trainTaskExecution
	return
}
