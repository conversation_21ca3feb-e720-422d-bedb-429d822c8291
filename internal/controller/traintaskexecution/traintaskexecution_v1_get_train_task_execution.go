package traintaskexecution

import (
	"context"
	v1 "mlops/api/traintaskexecution/v1"
	"strconv"

	"mlops/internal/consts"
	"mlops/internal/dao"
	"mlops/internal/model/dto"

	"github.com/gogf/gf/v2/frame/g"
)

func (c *ControllerV1) GetTrainTaskExecution(ctx context.Context, req *v1.GetTrainTaskExecutionReq) (res *v1.GetTrainTaskExecutionRes, err error) {
	res = &v1.GetTrainTaskExecutionRes{}
	trainTaskExecution := &dto.TrainTaskExecution{}
	r := g.RequestFromCtx(ctx).GetRouterMap()
	id := r["id"]
	idInt, err := strconv.Atoi(id)
	if err != nil {
		return nil, err
	}
	err = dao.TrainTaskExecution.Ctx(ctx).Where(dao.TrainTaskExecution.Columns().Id, idInt).Scan(trainTaskExecution)
	if err != nil {
		return nil, err
	}
	if trainTaskExecution.Status == consts.TrainTaskExecutionStatusPending {
		trainTaskExecution.EstimatedWaitTimeSeconds = consts.FixedEstimatedWaitTimeSeconds
	}
	res.Data = trainTaskExecution
	return
}
