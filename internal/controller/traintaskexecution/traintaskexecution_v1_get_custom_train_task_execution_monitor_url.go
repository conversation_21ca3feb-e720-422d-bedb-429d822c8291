package traintaskexecution

import (
	"context"
	"github.com/gogf/gf/v2/frame/g"
	"mlops/internal/dao"
	"mlops/internal/model/entity"
	"mlops/internal/service"
	"mlops/tools/client"
	"strconv"

	"mlops/api/traintaskexecution/v1"
)

func (c *ControllerV1) GetCustomTrainTaskExecutionMonitorUrl(ctx context.Context, req *v1.GetCustomTrainTaskExecutionMonitorUrlReq) (res *v1.GetCustomTrainTaskExecutionMonitorUrlRes, err error) {
	res = &v1.GetCustomTrainTaskExecutionMonitorUrlRes{}
	trainTaskExecution := &entity.TrainTaskExecution{}
	r := g.RequestFromCtx(ctx).GetRouterMap()
	id := r["id"]
	idInt, err := strconv.Atoi(id)
	if err != nil {
		return nil, err
	}
	err = dao.TrainTaskExecution.Ctx(ctx).Where(dao.TrainTaskExecution.Columns().Id, idInt).Scan(trainTaskExecution)
	if err != nil {
		return nil, err
	}

	url, err := client.ConstackHttpC.PageJumpPodList(ctx, &client.PageJumpPodListReq{
		ClusterName: trainTaskExecution.ClusterName,
		Namespace:   trainTaskExecution.Namespace,
		PodName:     trainTaskExecution.ExecutionName,
		Username:    service.BizCtx().Get(ctx).User.Username,
	})
	if err != nil {
		return nil, err
	}

	res.Url = url

	return
}
