package traintaskexecution

import (
	"context"
	"strconv"

	"github.com/gogf/gf/v2/frame/g"

	v1 "mlops/api/traintaskexecution/v1"
	"mlops/internal/service"
)

func (c *ControllerV1) UpdateTrainTaskExecutionPriority(ctx context.Context, req *v1.UpdateTrainTaskExecutionPriorityReq) (res *v1.UpdateTrainTaskExecutionPriorityRes, err error) {
	id := g.RequestFromCtx(ctx).GetRouterMap()["id"]
	idInt, err := strconv.Atoi(id)
	if err != nil {
		return nil, err
	}

	err = service.TrainTaskExecution().UpdatePriority(ctx, idInt, req.Priority)
	if err != nil {
		return nil, err
	}
	return
}
