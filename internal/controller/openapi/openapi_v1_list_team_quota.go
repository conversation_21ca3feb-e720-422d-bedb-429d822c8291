package openapi

import (
	"context"
	"encoding/json"
	"mlops/internal/model/dto"
	"mlops/internal/service"

	"mlops/api/openapi/v1"
)

func (c *ControllerV1) ListTeamQuota(ctx context.Context, req *v1.ListTeamQuotaReq) (res *v1.ListTeamQuotaRes, err error) {

	res = &v1.ListTeamQuotaRes{}
	list, err := service.Team().List(ctx)
	for _, v := range list {
		res.TeamQuotas = append(res.TeamQuotas, &dto.TeamGpuQuota{
			TeamName: v.Name,
			TeamId:   v.TeamId,
			GpuQuota: func() []dto.TeamGpuQuotaEntity {
				res := make([]dto.TeamGpuQuotaEntity, 0)
				qs := make([]dto.GpuQuota, 0)
				_ = json.Unmarshal([]byte(v.GpuQuota), &qs)
				for _, q := range qs {
					res = append(res, dto.TeamGpuQuotaEntity{
						GpuType: q.GpuType,
						Nums:    q.Nums,
					})
				}
				return res
			}(),
		})
	}

	return
}
