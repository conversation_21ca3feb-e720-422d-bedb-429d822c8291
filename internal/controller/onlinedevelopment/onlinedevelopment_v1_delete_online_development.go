package onlinedevelopment

import (
	"context"
	"github.com/gogf/gf/v2/frame/g"
	"mlops/internal/service"
	"strconv"

	"mlops/api/onlinedevelopment/v1"
)

func (c *ControllerV1) DeleteOnlineDevelopment(ctx context.Context, req *v1.DeleteOnlineDevelopmentReq) (res *v1.DeleteOnlineDevelopmentRes, err error) {
	res = &v1.DeleteOnlineDevelopmentRes{}
	r := g.RequestFromCtx(ctx).GetRouterMap()["id"]
	idInt, err := strconv.Atoi(r)
	if err != nil {
		return nil, err
	}

	err = service.OnlineDevelopment().Stop(ctx, uint(idInt))
	if err != nil {
		return nil, err
	}

	err = service.OnlineDevelopment().Delete(ctx, uint(idInt))
	if err != nil {
		return nil, err
	}

	return
}
