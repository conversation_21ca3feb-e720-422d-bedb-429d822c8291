package onlinedevelopment

import (
	"context"
	"github.com/gogf/gf/v2/frame/g"
	"mlops/internal/service"
	"strconv"

	"mlops/api/onlinedevelopment/v1"
)

func (c *ControllerV1) GetOnlineDevelopment(ctx context.Context, req *v1.GetOnlineDevelopmentReq) (res *v1.GetOnlineDevelopmentRes, err error) {
	res = &v1.GetOnlineDevelopmentRes{}
	r := g.RequestFromCtx(ctx).GetRouterMap()["id"]
	idInt, err := strconv.Atoi(r)
	if err != nil {
		return nil, err
	}
	trainTask, err := service.OnlineDevelopment().Get(ctx, uint(idInt))
	if err != nil {
		return nil, err
	}
	res.Data = trainTask
	return
}
