package onlinedevelopment

import (
	"context"
	"mlops/internal/service"

	"mlops/api/onlinedevelopment/v1"
)

func (c *ControllerV1) ListOnlineDevelopment(ctx context.Context, req *v1.ListOnlineDevelopmentReq) (res *v1.ListOnlineDevelopmentRes, err error) {
	res = &v1.ListOnlineDevelopmentRes{}
	list, err := service.OnlineDevelopment().ListPage(ctx, req.OnlineDevelopmentListInput)
	if err != nil {
		return nil, err
	}
	res.List = list.List
	res.Total = list.Total
	res.CurrentPage = list.CurrentPage
	res.PageSize = list.PageSize
	return
}
