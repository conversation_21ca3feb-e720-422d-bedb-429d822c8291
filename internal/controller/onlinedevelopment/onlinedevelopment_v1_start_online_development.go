package onlinedevelopment

import (
	"context"
	"github.com/gogf/gf/v2/frame/g"
	"mlops/internal/service"
	"strconv"

	"mlops/api/onlinedevelopment/v1"
)

func (c *ControllerV1) StartOnlineDevelopment(ctx context.Context, req *v1.StartOnlineDevelopmentReq) (res *v1.StartOnlineDevelopmentRes, err error) {
	res = &v1.StartOnlineDevelopmentRes{}
	r := g.RequestFromCtx(ctx).GetRouterMap()["id"]
	idInt, err := strconv.Atoi(r)
	if err != nil {
		return nil, err
	}

	err = service.OnlineDevelopment().Start(ctx, uint(idInt))
	if err != nil {
		return nil, err
	}

	return
}
