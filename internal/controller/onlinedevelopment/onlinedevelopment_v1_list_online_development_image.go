package onlinedevelopment

import (
	"context"
	"gitlab.ttyuyin.com/yunwei-infra/tt-cloud-constack-sdk/pkg/log"
	"mlops/internal/consts"
	"mlops/internal/service"
	"strings"

	"mlops/api/onlinedevelopment/v1"
)

func (c *ControllerV1) ListOnlineDevelopmentImage(ctx context.Context, req *v1.ListOnlineDevelopmentImageReq) (res *v1.ListOnlineDevelopmentImageRes, err error) {
	res = &v1.ListOnlineDevelopmentImageRes{}
	val, err := service.Setting().GetVal(ctx, consts.OnlineDevelopmentImageKey)
	if err != nil {
		return nil, err
	}
	if val == "" {
		log.L.WithName("ControllerV1.ListOnlineDevelopmentImage").Warningf(ctx, "online development image is empty")
		return res, nil
	}

	images := strings.Split(val, ",")
	if req.Type == consts.OnlineDevTypeCodeServer {
		for _, image := range images {
			if strings.Contains(image, "code") {
				res.Images = append(res.Images, image)
			}
		}
	}
	if req.Type == consts.OnlineDevTypeJupyter {
		for _, image := range images {
			if strings.Contains(image, "jupyter") {
				res.Images = append(res.Images, image)
			}
		}
	}

	return
}
