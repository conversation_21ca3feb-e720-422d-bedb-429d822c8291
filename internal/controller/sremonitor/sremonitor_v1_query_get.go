package sremonitor

import (
	"context"
	"mlops/internal/model/dto"
	"mlops/internal/service"

	"mlops/api/sremonitor/v1"
)

func (c *ControllerV1) QueryGet(ctx context.Context, req *v1.QueryGetReq) (res *v1.QueryGetRes, err error) {
	res = &v1.QueryGetRes{}

	// 构建查询参数
	query := &dto.SreMonitorQuery{
		Query:   req.Query,
		Time:    req.Time,
		Timeout: req.Timeout,
	}

	// 调用service
	result, err := service.SreMonitor().QueryGet(ctx, query)
	if err != nil {
		return nil, err
	}

	res.SreMonitorResponse = result
	return res, nil
}
