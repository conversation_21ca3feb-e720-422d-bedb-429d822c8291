package sremonitor

import (
	"context"
	"mlops/internal/model/dto"
	"mlops/internal/service"

	"mlops/api/sremonitor/v1"
)

func (c *ControllerV1) QueryRangePost(ctx context.Context, req *v1.QueryRangePostReq) (res *v1.QueryRangePostRes, err error) {
	res = &v1.QueryRangePostRes{}

	// 构建范围查询参数
	query := &dto.SreMonitorRangeQuery{
		Query:   req.Query,
		Start:   req.Start,
		End:     req.End,
		Step:    req.Step,
		Timeout: req.Timeout,
	}

	// 调用service
	result, err := service.SreMonitor().QueryRangePost(ctx, query)
	if err != nil {
		return nil, err
	}

	res.SreMonitorResponse = result
	return res, nil
}
