package sremonitor

import (
	"context"
	"mlops/internal/model/dto"
	"mlops/internal/service"

	"mlops/api/sremonitor/v1"
)

func (c *ControllerV1) QueryRangeGet(ctx context.Context, req *v1.QueryRangeGetReq) (res *v1.QueryRangeGetRes, err error) {
	res = &v1.QueryRangeGetRes{}

	// 构建范围查询参数
	query := &dto.SreMonitorRangeQuery{
		Query:   req.Query,
		Start:   req.Start,
		End:     req.End,
		Step:    req.Step,
		Timeout: req.Timeout,
	}

	// 调用service
	result, err := service.SreMonitor().QueryRangeGet(ctx, query)
	if err != nil {
		return nil, err
	}

	res.SreMonitorResponse = result
	return res, nil
}
