package sremonitor

import (
	"context"
	"mlops/internal/model/dto"
	"mlops/internal/service"

	"mlops/api/sremonitor/v1"
)

func (c *ControllerV1) QueryPost(ctx context.Context, req *v1.QueryPostReq) (res *v1.QueryPostRes, err error) {
	res = &v1.QueryPostRes{}

	// 构建查询参数
	query := &dto.SreMonitorQuery{
		Query:   req.Query,
		Time:    req.Time,
		Timeout: req.Timeout,
	}

	// 调用service
	result, err := service.SreMonitor().QueryPost(ctx, query)
	if err != nil {
		return nil, err
	}

	res.SreMonitorResponse = result
	return res, nil
}
