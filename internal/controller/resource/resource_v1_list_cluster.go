package resource

import (
	"context"
	"mlops/tools/client"

	v1 "mlops/api/resource/v1"
)

func (c *ControllerV1) ListCluster(ctx context.Context, req *v1.ListClusterReq) (res *v1.ListClusterRes, err error) {

	res = &v1.ListClusterRes{}
	clusters, err := client.ConstackHttpC.ListCluster(ctx)
	if clusters == nil {
		return
	}

	for _, cluster := range clusters {
		if _, err = client.GetNamespace(ctx, cluster, "kueue-system"); err == nil {
			res.ClusterList = append(res.ClusterList, cluster)
		}
	}
	return res, nil
}
