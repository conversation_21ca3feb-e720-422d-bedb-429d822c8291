package resource

import (
	"context"
	"mlops/api/resource/v1"
	"mlops/tools/client"
)

func (c *ControllerV1) ListNamespace(ctx context.Context, req *v1.ListNamespaceReq) (res *v1.ListNamespaceRes, err error) {

	res = &v1.ListNamespaceRes{}
	res.NamespaceList, err = client.ConstackHttpC.ListNamespace(ctx, req.Cluster)
	if res.NamespaceList == nil {
		res.NamespaceList = make([]string, 0)
	}

	return
}
