package resource

import (
	"context"
	"mlops/internal/service"
	"strconv"

	"mlops/api/resource/v1"
)

func (c *ControllerV1) ListTeamOverview(ctx context.Context, req *v1.ListTeamOverviewReq) (res *v1.ListTeamOverviewRes, err error) {

	teamId := -1
	if len(req.TeamId) > 0 {
		teamId, err = strconv.Atoi(req.TeamId)
		if err != nil {
			return
		}
	}

	res = &v1.ListTeamOverviewRes{}
	res.TeamOverview, err = service.Resource().GetTeamOverview(ctx, teamId)

	return
}
