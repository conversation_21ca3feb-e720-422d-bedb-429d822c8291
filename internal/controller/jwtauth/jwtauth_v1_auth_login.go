package jwtauth

import (
	"context"
	"mlops/internal/consts"
	"mlops/internal/service"
	"mlops/tools/errors"

	"mlops/api/jwtauth/v1"
)

func (c *ControllerV1) AuthLogin(ctx context.Context, req *v1.AuthLoginReq) (res *v1.<PERSON>th<PERSON><PERSON><PERSON><PERSON><PERSON>, err error) {
	res = &v1.AuthLoginRes{}
	userInfo, err := service.User().GetUserWithUserNamePassword(ctx,
		req.Username,
		req.Password)
	if err != nil {
		return nil, err
	}

	if userInfo.IsActive == consts.SFalse {
		return nil, errors.ForbiddenError("Account is disabled.")
	}

	err = service.PlatFormAuth().SyncUserTeam(ctx, int(userInfo.Id))
	if err != nil {
		return nil, err
	}

	res.Token, res.Expire = service.JwtAuth().Do().LoginHandler(ctx)
	return
}
