// ================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// You can delete these comments if you wish manually maintain this interface file.
// ================================================================================

package service

import (
	"context"
	"mlops/internal/model/dto"
	"mlops/internal/model/entity"
)

type (
	ITeam interface {
		Table() string
		Sync(ctx context.Context) (err error)
		// SyncTeamOrganization 根据用户id同步组织架构团队
		SyncTeamOrganization(ctx context.Context, userId int) (pid int64, err error)
		List(ctx context.Context) (list []*entity.Team, err error)
		ListRelatedClusterNamespace(ctx context.Context, teamId int) (res []*dto.ClusterNamespace, err error)
		ListTeamUser(ctx context.Context, teamId int) (res []*dto.UserTeamRole, err error)
		ListClusterNamespace(ctx context.Context, teamId int) (list []*dto.ClusterNamespace, err error)
		CreateGPUQuota(ctx context.Context, teamId int, quota *dto.GpuQuota) (err error)
		UpdateGPUQuota(ctx context.Context, teamId int, quota *dto.GpuQuota) (err error)
		DeleteGPUQuota(ctx context.Context, teamId int, gpuType string) (err error)
		ListGPUQuota(ctx context.Context, teamId int) (list []*dto.GpuQuota, err error)
		CreateClusterNamespace(ctx context.Context, teamId int, clusterNamespaces *dto.ClusterNamespace) (err error)
		DeleteClusterNamespace(ctx context.Context, teamId int, clusterNamespaces *dto.ClusterNamespace) (err error)
		ListTeamApp(ctx context.Context, teamId int, page int, size int) (list []*dto.App, err error)
	}
)

var (
	localTeam ITeam
)

func Team() ITeam {
	if localTeam == nil {
		panic("implement not found for interface ITeam, forgot register?")
	}
	return localTeam
}

func RegisterTeam(i ITeam) {
	localTeam = i
}
