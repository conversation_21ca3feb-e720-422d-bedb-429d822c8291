// ================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// You can delete these comments if you wish manually maintain this interface file.
// ================================================================================

package service

import (
	"context"

	"github.com/gogf/gf/v2/net/ghttp"
)

type (
	IMiddleware interface {
		JwtAuth(r *ghttp.Request)
		CheckUserIsActive(r *ghttp.Request)
		CheckApiV1GlobalRole(ctx context.Context, r *ghttp.Request, userId int)
		ErrorRespHandler(r *ghttp.Request)
		MiddlewareHandlerResponse(r *ghttp.Request)
		CheckOpenAPIToken(r *ghttp.Request)
	}
)

var (
	localMiddleware IMiddleware
)

func Middleware() IMiddleware {
	if localMiddleware == nil {
		panic("implement not found for interface IMiddleware, forgot register?")
	}
	return localMiddleware
}

func RegisterMiddleware(i IMiddleware) {
	localMiddleware = i
}
