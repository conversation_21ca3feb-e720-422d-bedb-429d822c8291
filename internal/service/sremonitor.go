// ================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// You can delete these comments if you wish manually maintain this interface file.
// ================================================================================

package service

import (
	"context"
	"mlops/internal/model/dto"
)

type (
	ISreMonitor interface {
		// QueryGet 使用GET方法查询Prometheus指标
		QueryGet(ctx context.Context, query *dto.SreMonitorQuery) (*dto.SreMonitorResponse, error)
		// QueryPost 使用POST方法查询Prometheus指标
		QueryPost(ctx context.Context, query *dto.SreMonitorQuery) (*dto.SreMonitorResponse, error)
		// QueryRangeGet 使用GET方法进行范围查询
		QueryRangeGet(ctx context.Context, query *dto.SreMonitorRangeQuery) (*dto.SreMonitorResponse, error)
		// QueryRangePost 使用POST方法进行范围查询
		QueryRangePost(ctx context.Context, query *dto.SreMonitorRangeQuery) (*dto.SreMonitorResponse, error)
		// ParseMetrics 解析指标数据为更友好的格式
		ParseMetrics(data *dto.SreMonitorQueryData) ([]*dto.SreMonitorMetric, error)
	}
)

var (
	localSreMonitor ISreMonitor
)

func SreMonitor() ISreMonitor {
	if localSreMonitor == nil {
		panic("implement not found for interface ISreMonitor, forgot register?")
	}
	return localSreMonitor
}

func RegisterSreMonitor(i ISreMonitor) {
	localSreMonitor = i
}
