// ================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// You can delete these comments if you wish manually maintain this interface file.
// ================================================================================

package service

import (
	"context"
)

type (
	ICronJob interface {
		//	========  seconds ========
		//
		// gcron 内部的 func 所有panic错误都回被忽略
		Test(ctx context.Context) (err error)
		SyncCicdTeam(ctx context.Context) error
		SyncTrainTaskExecution(ctx context.Context) error
		SyncOnlineDevelopmentStatus(ctx context.Context) error
	}
)

var (
	localCronJob ICronJob
)

func CronJob() ICronJob {
	if localCronJob == nil {
		panic("implement not found for interface ICronJob, forgot register?")
	}
	return localCronJob
}

func RegisterCronJob(i ICronJob) {
	localCronJob = i
}
