// ================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// You can delete these comments if you wish manually maintain this interface file.
// ================================================================================

package service

import (
	"context"
	"mlops/internal/model/dto"
)

type (
	ITrainTask interface {
		Table() string
		ListPage(ctx context.Context, in dto.TrainTaskListInput) (pageData *dto.TrainTaskListOutput, err error)
		Get(ctx context.Context, id uint) (trainTask *dto.TrainTask, err error)
		Create(ctx context.Context, trainTask *dto.TrainTask) (err error)
		Update(ctx context.Context, trainTask *dto.TrainTask) (err error)
		Delete(ctx context.Context, id uint) (err error)
		Trigger(ctx context.Context, id uint, triggerSource string, triggeredByUserName string, triggeredByEmployeeNo string) (err error)
	}
)

var (
	localTrainTask ITrainTask
)

func TrainTask() ITrainTask {
	if localTrainTask == nil {
		panic("implement not found for interface ITrainTask, forgot register?")
	}
	return localTrainTask
}

func RegisterTrainTask(i ITrainTask) {
	localTrainTask = i
}
