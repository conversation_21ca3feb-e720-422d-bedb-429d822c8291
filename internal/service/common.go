// ================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// You can delete these comments if you wish manually maintain this interface file.
// ================================================================================

package service

import (
	"context"
	"mlops/internal/model/entity"
)

type (
	ISetting interface {
		Set(ctx context.Context, k string, v interface{}, desc string, category ...string) (err error)
		ListWithCategory(ctx context.Context, category string) (res []entity.Setting, err error)
		Get(ctx context.Context, k string, category ...string) (setting *entity.Setting, err error)
		GetVal(ctx context.Context, k string, category ...string) (val string, err error)
		GetValMap(ctx context.Context, k string, category ...string) (map[string]interface{}, error)
		GetValSliceString(ctx context.Context, k string, category ...string) ([]string, error)
		GetValSliceInt(ctx context.Context, k string, category ...string) ([]int, error)
		GetValInt(ctx context.Context, k string, category ...string) (int, error)
		GetValFloat(ctx context.Context, k string, category ...string) (float64, error)
		GetValSliceByString(ctx context.Context, k string, category ...string) ([]string, error)
	}
)

var (
	localSetting ISetting
)

func Setting() ISetting {
	if localSetting == nil {
		panic("implement not found for interface ISetting, forgot register?")
	}
	return localSetting
}

func RegisterSetting(i ISetting) {
	localSetting = i
}
