// ================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// You can delete these comments if you wish manually maintain this interface file.
// ================================================================================

package service

import (
	"context"
	"mlops/internal/model/dto"
	"mlops/internal/model/entity"

	"github.com/gogf/gf/v2/frame/g"
)

type (
	IUser interface {
		Table() string
		GetUserById(ctx context.Context, id int) (user *entity.User, err error)
		CreateUser(ctx context.Context, user *entity.User) (err error)
		GetUserByUsername(ctx context.Context, username string) (user *entity.User, err error)
		CheckIsActive(ctx context.Context, uid int) error
		GetUserWithCondition(ctx context.Context, condition g.Map) (user *entity.User, err error)
		GetUserWithUserNamePassword(ctx context.Context, username string, password string) (user *entity.User, err error)
		Profile(ctx context.Context, uid int) (profile *dto.UserProfile, err error)
		ListUserViewObj(ctx context.Context, search string) (userViewObjs []*dto.UserViewObj, err error)
	}
)

var (
	localUser IUser
)

func User() IUser {
	if localUser == nil {
		panic("implement not found for interface IUser, forgot register?")
	}
	return localUser
}

func RegisterUser(i IUser) {
	localUser = i
}
