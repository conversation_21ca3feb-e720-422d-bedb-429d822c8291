// ================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// You can delete these comments if you wish manually maintain this interface file.
// ================================================================================

package service

import (
	"context"
	"mlops/internal/model/dto"
)

type (
	IResource interface {
		GetGpuList(ctx context.Context) (res []*dto.GpuDetail, err error)
		CreateGpuList(ctx context.Context, data *dto.GpuDetail) (err error)
	}
)

var (
	localResource IResource
)

func Resource() IResource {
	if localResource == nil {
		panic("implement not found for interface IResource, forgot register?")
	}
	return localResource
}

func RegisterResource(i IResource) {
	localResource = i
}
