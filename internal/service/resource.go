// ================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// You can delete these comments if you wish manually maintain this interface file.
// ================================================================================

package service

import (
	"context"
	v1 "mlops/api/resource/v1"
	"mlops/internal/model/dto"
)

type (
	IResource interface {
		GetGpuList(ctx context.Context) (res []*dto.GpuDetail, err error)
		CreateGpuList(ctx context.Context, data *dto.GpuDetail) (err error)
		GetGpuOverview(ctx context.Context) (res []*dto.GpuOverview, err error)
		GetTeamOverview(ctx context.Context, teamId int) (res []*dto.TeamOverview, err error)
		ListGpuMonitor(ctx context.Context, teamId int) (res *v1.ListGpuMonitorRes, err error)
	}
)

var (
	localResource IResource
)

func Resource() IResource {
	if localResource == nil {
		panic("implement not found for interface IResource, forgot register?")
	}
	return localResource
}

func RegisterResource(i IResource) {
	localResource = i
}
