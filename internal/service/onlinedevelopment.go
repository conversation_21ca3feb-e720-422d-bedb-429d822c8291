// ================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// You can delete these comments if you wish manually maintain this interface file.
// ================================================================================

package service

import (
	"context"
	"mlops/internal/model/dto"
)

type (
	IOnlineDevelopment interface {
		Table() string
		ListPage(ctx context.Context, in dto.OnlineDevelopmentListInput) (pageData *dto.OnlineDevelopmentListOutput, err error)
		Get(ctx context.Context, id uint) (onlineDevelopment *dto.OnlineDevelopment, err error)
		Create(ctx context.Context, onlineDevelopment *dto.OnlineDevelopment) (err error)
		Update(ctx context.Context, onlineDevelopment *dto.OnlineDevelopment) (err error)
		Delete(ctx context.Context, id uint) (err error)
		Start(ctx context.Context, id uint) (err error)
		Stop(ctx context.Context, id uint) (err error)
		SyncStatus(ctx context.Context) (err error)
	}
)

var (
	localOnlineDevelopment IOnlineDevelopment
)

func OnlineDevelopment() IOnlineDevelopment {
	if localOnlineDevelopment == nil {
		panic("implement not found for interface IOnlineDevelopment, forgot register?")
	}
	return localOnlineDevelopment
}

func RegisterOnlineDevelopment(i IOnlineDevelopment) {
	localOnlineDevelopment = i
}
