// ================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// You can delete these comments if you wish manually maintain this interface file.
// ================================================================================

package service

import (
	"context"
	"mlops/internal/model/dto"
)

type (
	ITrainTaskExecution interface {
		Table() string
		ListPage(ctx context.Context, in dto.TrainTaskExecutionListInput) (pageData *dto.TrainTaskExecutionListOutput, err error)
		List(ctx context.Context, in dto.TrainTaskExecutionListInput) (list []*dto.TrainTaskExecution, err error)
		Get(ctx context.Context, id uint) (trainTaskExecution *dto.TrainTaskExecution, err error)
		Interrupt(ctx context.Context, id uint) (err error)
		SyncTaskExecutionStatus(ctx context.Context) (err error)
		UpdatePriority(ctx context.Context, id int, priority string) (err error)
	}
)

var (
	localTrainTaskExecution ITrainTaskExecution
)

func TrainTaskExecution() ITrainTaskExecution {
	if localTrainTaskExecution == nil {
		panic("implement not found for interface ITrainTaskExecution, forgot register?")
	}
	return localTrainTaskExecution
}

func RegisterTrainTaskExecution(i ITrainTaskExecution) {
	localTrainTaskExecution = i
}
