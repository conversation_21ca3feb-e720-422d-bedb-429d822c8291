package dto

// SreMonitorQuery SRE监控查询DTO
type SreMonitorQuery struct {
	Query   string `json:"query"`
	Time    string `json:"time,omitempty"`
	Timeout string `json:"timeout,omitempty"`
}

// SreMonitorResponse SRE监控响应DTO
type SreMonitorResponse struct {
	Status    string      `json:"status"`
	Data      interface{} `json:"data,omitempty"`
	ErrorType string      `json:"errorType,omitempty"`
	Error     string      `json:"error,omitempty"`
	Warnings  []string    `json:"warnings,omitempty"`
}

// SreMonitorQueryData 查询数据
type SreMonitorQueryData struct {
	ResultType string                    `json:"resultType"`
	Result     []*SreMonitorQueryResult  `json:"result"`
}

// SreMonitorQueryResult 查询结果
type SreMonitorQueryResult struct {
	Metric map[string]string `json:"metric"`
	Value  []interface{}     `json:"value"`
}

// SreMonitorMetric 指标信息
type SreMonitorMetric struct {
	Name      string            `json:"name"`
	Labels    map[string]string `json:"labels"`
	Value     string            `json:"value"`
	Timestamp int64             `json:"timestamp"`
}

// SreMonitorRangeQuery 范围查询DTO
type SreMonitorRangeQuery struct {
	Query   string `json:"query"`
	Start   string `json:"start"`
	End     string `json:"end"`
	Step    string `json:"step"`
	Timeout string `json:"timeout,omitempty"`
}

// SreMonitorRangeQueryResult 范围查询结果
type SreMonitorRangeQueryResult struct {
	Metric map[string]string `json:"metric"`
	Values [][]interface{}   `json:"values"`
}

// SreMonitorRangeQueryData 范围查询数据
type SreMonitorRangeQueryData struct {
	ResultType string                           `json:"resultType"`
	Result     []*SreMonitorRangeQueryResult    `json:"result"`
}
