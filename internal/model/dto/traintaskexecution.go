package dto

import "github.com/gogf/gf/v2/os/gtime"

type TrainTaskExecutionListInput struct {
	Page                  int `v:"required"`
	PageSize              int `v:"required"`
	ExecutionId           uint
	TaskId                uint
	TeamId                uint
	TaskName              string
	Statuses              []string
	TriggeredByUserName   string
	TriggeredByEmployeeNo string
	TriggerSources        []string
	TriggerTime           []*gtime.Time
	StartTime             []*gtime.Time
	EndTime               []*gtime.Time
}

type TrainTaskExecutionListOutput struct {
	List        []*TrainTaskExecution `json:"list"`
	Total       int                   `json:"total"`
	CurrentPage int                   `json:"currentPage"`
	PageSize    int                   `json:"pageSize"`
}

type TrainTaskExecution struct {
	Id                       uint        `json:"id"`
	TaskId                   uint        `json:"taskId"`
	TaskName                 string      `json:"taskName"`
	TeamId                   uint        `json:"teamId"`
	TeamName                 string      `json:"teamName"`
	ExecutionName            string      `json:"executionName"`
	TrainingFramework        string      `json:"trainingFramework"`
	ClusterName              string      `json:"clusterName"`
	ClusterId                uint        `json:"clusterId"`
	Namespace                string      `json:"namespace"`
	ImageUrl                 string      `json:"imageUrl"`
	TriggerTime              *gtime.Time `json:"triggerTime"`
	StartTime                *gtime.Time `json:"startTime"`
	EndTime                  *gtime.Time `json:"endTime"`
	Duration                 uint        `json:"duration"`
	DashboardUrl             string      `json:"dashboardUrl"`
	MonitorUrl               string      `json:"monitorUrl"`
	Status                   string      `json:"status"`
	TriggerSource            string      `json:"triggerSource"`
	TriggeredByUserName      string      `json:"triggeredByUserName"`
	TriggeredByEmployeeNo    string      `json:"triggeredByEmployeeNo"`
	Priority                 string      `json:"priority"`
	EstimatedWaitTimeSeconds uint        `json:"estimatedWaitTimeSeconds"`
}
