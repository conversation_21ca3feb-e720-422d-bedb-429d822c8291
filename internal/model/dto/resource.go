package dto

type GpuDetail struct {
	GpuType   string `json:"gpuType"`
	GpuCore   string `json:"gpuCore"`
	GpuMemory string `json:"gpuMemory"`
}

type GpuOverview struct {
	GpuType    string `json:"gpuType"`
	GpuCore    string `json:"gpuCore"`
	GpuMemory  string `json:"gpuMemory"`
	Budget     uint   `json:"budget"`
	Allocation uint   `json:"allocation"`
}

type TeamOverview struct {
	Id                int64               `json:"id" `
	Name              string              `json:"name" `
	TeamId            string              `json:"teamId"`
	ClusterNamespaces []*ClusterNamespace `json:"clusterNamespaces"`
	GpuQuota          []*GpuQuota         `json:"gpuQuotas"`
}
