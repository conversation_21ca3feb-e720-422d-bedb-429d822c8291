package dto

import authConsts "mlops/internal/consts/auth"

type AuthProfile struct {
	Roles     []authConsts.RoleKind `json:"roles"`
	TeamRoles []TeamRole            `json:"teamRoles"`
}

type TeamRole struct {
	Id       int                     `json:"id"`
	TeamId   string                  `json:"teamId"`
	TeamName string                  `json:"teamName"`
	TeamType authConsts.TeamKind     `json:"teamType"`
	Role     authConsts.TeamRoleKind `json:"role"`
}

type UserTeamRole struct {
	UserId     int                     `json:"userId"`
	Username   string                  `json:"username"`
	NickName   string                  `json:"nickName"`
	Email      string                  `json:"email"`
	EmployeeNo string                  `json:"employeeNo"`
	Id         int                     `json:"id"`
	TeamId     string                  `json:"teamId"`
	TeamName   string                  `json:"teamName"`
	TeamType   authConsts.TeamKind     `json:"teamType"`
	Role       authConsts.TeamRoleKind `json:"role"`
}

type GetUserAuthProfileDbResult struct {
	UserId   int                 `json:"userId"`
	Username string              `json:"username"`
	Email    string              `json:"email"`
	RoleName authConsts.RoleKind `json:"role_name"`
	TeamName string              `json:"team_name"`
	TeamId   string              `json:"team_id"`
	TeamPid  int                 `json:"team_pid"`
	TeamType authConsts.TeamKind `json:"team_type"`
}
