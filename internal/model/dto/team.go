package dto

type Team struct {
	Id       int64  `json:"id"`
	Name     string `json:"name"`
	TeamId   string `json:"teamId"`
	Category string `json:"category"`
}

type ClusterNamespace struct {
	Cluster    string   `json:"cluster"`
	Namespaces []string `json:"namespaces"`
}

type GpuQuota struct {
	GpuType   string `json:"gpuType"`
	GpuCore   string `json:"gpuCore"`
	GpuMemory string `json:"gpuMemory"`
	Nums      uint   `json:"nums"`
}

type GpuQuotaFloat struct {
	GpuType   string  `json:"gpuType"`
	GpuCore   string  `json:"gpuCore"`
	GpuMemory string  `json:"gpuMemory"`
	Nums      float64 `json:"nums"`
}

type App struct {
	Id     int    `json:"id"`
	Name   string `json:"name"`
	CmdbId string `json:"cmdbId"`
}
