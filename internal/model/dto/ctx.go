package dto

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

/* Data Transfer Object，数据传输对象 */

// Context 请求上下文结构
type Context struct {
	User *ContextUser // 上下文用户信息
	Data g.Map        // 自定KV变量，业务模块根据需要设置，不固定
}

// ContextUser 请求上下文中的用户信息
type ContextUser struct {
	Id         uint        `json:"ID"` // 用户ID
	UId        string      `json:"UId"`
	Username   string      `json:"username"`   // 用户账号
	NickName   string      `json:"nickname"`   // 用户名称
	Email      string      `json:"email"`      // 邮箱
	EmployeeNo string      `json:"employeeNo"` // 工号
	CreatedAt  *gtime.Time `json:"createdAt"`
	UpdatedAt  *gtime.Time `json:"updatedAt"`
}
