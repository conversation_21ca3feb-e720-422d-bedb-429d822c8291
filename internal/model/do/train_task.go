// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// TrainTask is the golang structure of table tt_train_task for DAO operations like Where/Data.
type TrainTask struct {
	g.Meta              `orm:"table:tt_train_task, do:true"`
	Id                  interface{} // 主键
	TeamId              interface{} // 团队ID
	TeamName            interface{} // 组名
	AppName             interface{} // cicd应用名
	CmdbId              interface{} //
	TaskName            interface{} // 任务名称
	ClusterName         interface{} // 集群名称
	ClusterId           interface{} // 集群ID
	Namespace           interface{} // 命名空间
	ImageUrl            interface{} // 镜像地址
	EnvVars             interface{} // 环境变量
	StartCmd            interface{} // 启动命令
	Priority            interface{} // 优先级
	TaskType            interface{} // 任务类型
	TrainingFramework   interface{} // 训练框架
	TaskYaml            interface{} // task yaml
	TriggerCount        interface{} // 触发次数
	CompleteCount       interface{} // 完成次数
	LastStatus          interface{} // 最近一次执行状态
	CreatedByUserName   interface{} // 创建用户名
	CreatedByEmployeeNo interface{} // 创建用户工号
	UpdatedByUserName   interface{} // 最后更新用户名
	UpdatedByEmployeeNo interface{} // 最后更新用户工号
	CreatedAt           *gtime.Time // 创建时间
	UpdatedAt           *gtime.Time // 更新时间
	DeletedAt           *gtime.Time // 软删除时间（NULL表示未删除）
}
