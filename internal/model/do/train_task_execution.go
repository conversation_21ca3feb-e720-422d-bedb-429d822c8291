// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// TrainTaskExecution is the golang structure of table tt_train_task_execution for DAO operations like Where/Data.
type TrainTaskExecution struct {
	g.Meta                `orm:"table:tt_train_task_execution, do:true"`
	Id                    interface{} // 主键
	TaskId                interface{} // 关联训练任务ID（外键）
	TeamId                interface{} // 团队ID
	TeamName              interface{} // 组名
	ExecutionName         interface{} // rayjob name
	TrainingFramework     interface{} // 训练框架
	ClusterName           interface{} // 集群名称
	ClusterId             interface{} // 集群ID
	Namespace             interface{} // 命名空间
	ImageUrl              interface{} // 镜像地址
	TriggerTime           *gtime.Time // 触发时间
	StartTime             *gtime.Time // 任务开始时间
	EndTime               *gtime.Time // 任务结束时间（NULL表示未结束）
	Duration              interface{} // 持续时长（分钟）
	DashboardUrl          interface{} // 监控面板URL
	MonitorUrl            interface{} // 历史监控URL
	Status                interface{} // 任务状态
	TriggerSource         interface{} // 触发来源
	TriggeredByUserName   interface{} // 触发用户
	TriggeredByEmployeeNo interface{} // 触发用户工号
	CancelledByUserName   interface{} // 中断用户名字
	CancelledByEmployeeNo interface{} // 中断用户工号
	Workers               interface{} // 工作负载列表
	Priority              interface{} // 优先级
	CreatedAt             *gtime.Time // 创建时间
	UpdatedAt             *gtime.Time // 更新时间
	DeletedAt             *gtime.Time // 软删除时间（NULL表示未删除）
}
