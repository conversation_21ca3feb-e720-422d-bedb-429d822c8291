// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// Team is the golang structure of table tt_team for DAO operations like Where/Data.
type Team struct {
	g.Meta           `orm:"table:tt_team, do:true"`
	Id               interface{} //
	Category         interface{} // 分类
	Name             interface{} // 团队名
	TeamId           interface{} // 团队业务id
	CreatedAt        *gtime.Time //
	UpdatedAt        *gtime.Time //
	DeletedAt        *gtime.Time //
	ClusterNamespace interface{} // 集群命名空间
	GpuQuota         interface{} // gpu配额
}
