// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// TrainTaskVolumeMount is the golang structure of table tt_train_task_volume_mount for DAO operations like Where/Data.
type TrainTaskVolumeMount struct {
	g.Meta     `orm:"table:tt_train_task_volume_mount, do:true"`
	Id         interface{} // 主键（唯一挂载ID）
	TaskId     interface{} // 关联训练任务ID（外键）
	Name       interface{} // 挂载名称（如"data-volume"）
	MountPath  interface{} // 容器内挂载路径（如"/data"）
	SubPath    interface{} // 子路径（可选，如"dataset/2023"）
	VolumeType interface{} // 存储类型（如NFS、HostPath、PVC）
	VolumeName interface{} // 存储卷名称（如"nfs-data-volume"）
	CreatedAt  *gtime.Time // 创建时间
	UpdatedAt  *gtime.Time // 更新时间
	DeletedAt  *gtime.Time // 软删除时间（NULL表示未删除）
}
