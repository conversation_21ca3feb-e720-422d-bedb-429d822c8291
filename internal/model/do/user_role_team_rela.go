// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
)

// UserRoleTeamRela is the golang structure of table tt_user_role_team_rela for DAO operations like Where/Data.
type UserRoleTeamRela struct {
	g.Meta `orm:"table:tt_user_role_team_rela, do:true"`
	Id     interface{} //
	UserId interface{} //
	RoleId interface{} //
	TeamId interface{} // team表业务id
}
