// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// OpenapiToken is the golang structure of table tt_openapi_token for DAO operations like Where/Data.
type OpenapiToken struct {
	g.Meta    `orm:"table:tt_openapi_token, do:true"`
	Id        interface{} //
	Name      interface{} // 唯一标识
	Token     interface{} // token
	Desc      interface{} // 描述
	CreatedAt *gtime.Time //
	UpdatedAt *gtime.Time //
	DeletedAt *gtime.Time //
}
