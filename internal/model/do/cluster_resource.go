// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
)

// ClusterResource is the golang structure of table tt_cluster_resource for DAO operations like Where/Data.
type ClusterResource struct {
	g.Meta           `orm:"table:tt_cluster_resource, do:true"`
	Id               interface{} // 资源主键
	TaskId           interface{} // 关联训练任务ID（外键）
	MinReplicas      interface{} // 最小副本数
	MaxReplicas      interface{} // 最大副本数
	RequestCpu       interface{} // 请求CPU资源（如"2"表示2核）
	RequestMemory    interface{} // 请求内存（如"4Gi"）
	RequestGpuCore   interface{} // 请求GPU核心数
	RequestGpuMemory interface{} // 请求GPU显存
	LimitCpu         interface{} // CPU限制（如"2000m"）
	LimitMemory      interface{} // 内存限制（如"8Gi"）
	LimitGpuCore     interface{} // GPU核心限制
	LimitGpuMemory   interface{} // GPU显存限制
}
