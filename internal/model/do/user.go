// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// User is the golang structure of table tt_user for DAO operations like Where/Data.
type User struct {
	g.Meta     `orm:"table:tt_user, do:true"`
	Id         interface{} // 主键自增id
	Uid        interface{} // 三方系统唯一id
	Username   interface{} // 用户名
	NickName   interface{} // 昵称
	Email      interface{} // 邮箱
	EmployeeNo interface{} // 工号
	CreatedAt  *gtime.Time //
	UpdatedAt  *gtime.Time //
	DeletedAt  *gtime.Time //
	Password   interface{} //
	IsActive   interface{} // 是否激活
}
