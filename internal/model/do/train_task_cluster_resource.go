// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// TrainTaskClusterResource is the golang structure of table tt_train_task_cluster_resource for DAO operations like Where/Data.
type TrainTaskClusterResource struct {
	g.Meta           `orm:"table:tt_train_task_cluster_resource, do:true"`
	Id               interface{} // 资源主键
	TaskId           interface{} // 关联训练任务ID（外键）
	MinReplicas      interface{} // 最小副本数
	MaxReplicas      interface{} // 最大副本数
	GpuType          interface{} // 显卡类型
	RequestCpu       interface{} // 请求CPU资源（如"2"表示2核）
	RequestMemory    interface{} // 请求内存（如"4Gi"）
	RequestGpuCore   interface{} // 请求GPU核心数
	RequestGpuMemory interface{} // 请求GPU显存
	LimitCpu         interface{} // CPU限制（如"2000m"）
	LimitMemory      interface{} // 内存限制（如"8Gi"）
	LimitGpuCore     interface{} // GPU核心限制
	LimitGpuMemory   interface{} // GPU显存限制
	CreatedAt        *gtime.Time // 创建时间
	UpdatedAt        *gtime.Time // 更新时间
	DeletedAt        *gtime.Time // 软删除时间（NULL表示未删除）
}
