// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
)

// GpuList is the golang structure of table tt_gpu_list for DAO operations like Where/Data.
type GpuList struct {
	g.Meta    `orm:"table:tt_gpu_list, do:true"`
	Id        interface{} // 主键自增id
	GpuType   interface{} // gpu类型
	GpuCore   interface{} // gpu核心数
	GpuMemory interface{} // 显存
}
