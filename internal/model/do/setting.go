// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// Setting is the golang structure of table tt_setting for DAO operations like Where/Data.
type Setting struct {
	g.Meta    `orm:"table:tt_setting, do:true"`
	Id        interface{} //
	Key       interface{} //
	Value     interface{} //
	Category  interface{} //
	Desc      interface{} //
	CreatedAt *gtime.Time //
	UpdatedAt *gtime.Time //
	DeletedAt *gtime.Time //
}
