// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

// ClusterResource is the golang structure for table cluster_resource.
type ClusterResource struct {
	Id               uint   `json:"id"               orm:"id"                 description:"资源主键"`
	TaskId           uint   `json:"taskId"           orm:"task_id"            description:"关联训练任务ID（外键）"`
	MinReplicas      uint   `json:"minReplicas"      orm:"min_replicas"       description:"最小副本数"`
	MaxReplicas      uint   `json:"maxReplicas"      orm:"max_replicas"       description:"最大副本数"`
	RequestCpu       string `json:"requestCpu"       orm:"request_cpu"        description:"请求CPU资源（如\"2\"表示2核）"`
	RequestMemory    string `json:"requestMemory"    orm:"request_memory"     description:"请求内存（如\"4Gi\"）"`
	RequestGpuCore   string `json:"requestGpuCore"   orm:"request_gpu_core"   description:"请求GPU核心数"`
	RequestGpuMemory string `json:"requestGpuMemory" orm:"request_gpu_memory" description:"请求GPU显存"`
	LimitCpu         string `json:"limitCpu"         orm:"limit_cpu"          description:"CPU限制（如\"2000m\"）"`
	LimitMemory      string `json:"limitMemory"      orm:"limit_memory"       description:"内存限制（如\"8Gi\"）"`
	LimitGpuCore     string `json:"limitGpuCore"     orm:"limit_gpu_core"     description:"GPU核心限制"`
	LimitGpuMemory   string `json:"limitGpuMemory"   orm:"limit_gpu_memory"   description:"GPU显存限制"`
}
