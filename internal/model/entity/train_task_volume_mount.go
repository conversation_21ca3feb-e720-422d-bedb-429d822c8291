// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
)

// TrainTaskVolumeMount is the golang structure for table train_task_volume_mount.
type TrainTaskVolumeMount struct {
	Id         uint        `json:"id"         orm:"id"          description:"主键（唯一挂载ID）"`
	TaskId     uint        `json:"taskId"     orm:"task_id"     description:"关联训练任务ID（外键）"`
	Name       string      `json:"name"       orm:"name"        description:"挂载名称（如\"data-volume\"）"`
	MountPath  string      `json:"mountPath"  orm:"mount_path"  description:"容器内挂载路径（如\"/data\"）"`
	SubPath    string      `json:"subPath"    orm:"sub_path"    description:"子路径（可选，如\"dataset/2023\"）"`
	VolumeType string      `json:"volumeType" orm:"volume_type" description:"存储类型（如NFS、HostPath、PVC）"`
	VolumeName string      `json:"volumeName" orm:"volume_name" description:"存储卷名称（如\"nfs-data-volume\"）"`
	CreatedAt  *gtime.Time `json:"createdAt"  orm:"created_at"  description:"创建时间"`
	UpdatedAt  *gtime.Time `json:"updatedAt"  orm:"updated_at"  description:"更新时间"`
	DeletedAt  *gtime.Time `json:"deletedAt"  orm:"deleted_at"  description:"软删除时间（NULL表示未删除）"`
}
