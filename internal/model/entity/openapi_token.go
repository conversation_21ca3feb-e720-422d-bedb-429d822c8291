// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
)

// OpenapiToken is the golang structure for table openapi_token.
type OpenapiToken struct {
	Id        uint        `json:"id"        orm:"id"         description:""`
	Name      string      `json:"name"      orm:"name"       description:"唯一标识"`
	Token     string      `json:"token"     orm:"token"      description:"token"`
	Desc      string      `json:"desc"      orm:"desc"       description:"描述"`
	CreatedAt *gtime.Time `json:"createdAt" orm:"created_at" description:""`
	UpdatedAt *gtime.Time `json:"updatedAt" orm:"updated_at" description:""`
	DeletedAt *gtime.Time `json:"deletedAt" orm:"deleted_at" description:""`
}
