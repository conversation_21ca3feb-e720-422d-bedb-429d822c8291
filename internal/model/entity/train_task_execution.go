// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
)

// TrainTaskExecution is the golang structure for table train_task_execution.
type TrainTaskExecution struct {
	Id                    uint        `json:"id"                    orm:"id"                       description:"主键"`
	TaskId                uint        `json:"taskId"                orm:"task_id"                  description:"关联训练任务ID（外键）"`
	TeamId                uint        `json:"teamId"                orm:"team_id"                  description:"团队ID"`
	TeamName              string      `json:"teamName"              orm:"team_name"                description:"组名"`
	ExecutionName         string      `json:"executionName"         orm:"execution_name"           description:"rayjob name"`
	TrainingFramework     string      `json:"trainingFramework"     orm:"training_framework"       description:"训练框架"`
	ClusterName           string      `json:"clusterName"           orm:"cluster_name"             description:"集群名称"`
	ClusterId             uint        `json:"clusterId"             orm:"cluster_id"               description:"集群ID"`
	Namespace             string      `json:"namespace"             orm:"namespace"                description:"命名空间"`
	ImageUrl              string      `json:"imageUrl"              orm:"image_url"                description:"镜像地址"`
	TriggerTime           *gtime.Time `json:"triggerTime"           orm:"trigger_time"             description:"触发时间"`
	StartTime             *gtime.Time `json:"startTime"             orm:"start_time"               description:"任务开始时间"`
	EndTime               *gtime.Time `json:"endTime"               orm:"end_time"                 description:"任务结束时间（NULL表示未结束）"`
	Duration              uint        `json:"duration"              orm:"duration"                 description:"持续时长（分钟）"`
	DashboardUrl          string      `json:"dashboardUrl"          orm:"dashboard_url"            description:"监控面板URL"`
	MonitorUrl            string      `json:"monitorUrl"            orm:"monitor_url"              description:"历史监控URL"`
	Status                string      `json:"status"                orm:"status"                   description:"任务状态"`
	TriggerSource         string      `json:"triggerSource"         orm:"trigger_source"           description:"触发来源"`
	TriggeredByUserName   string      `json:"triggeredByUserName"   orm:"triggered_by_user_name"   description:"触发用户"`
	TriggeredByEmployeeNo string      `json:"triggeredByEmployeeNo" orm:"triggered_by_employee_no" description:"触发用户工号"`
	CancelledByUserName   string      `json:"cancelledByUserName"   orm:"cancelled_by_user_name"   description:"中断用户名字"`
	CancelledByEmployeeNo string      `json:"cancelledByEmployeeNo" orm:"cancelled_by_employee_no" description:"中断用户工号"`
	Workers               string      `json:"workers"               orm:"workers"                  description:"工作负载列表"`
	Priority              string      `json:"priority"              orm:"priority"                 description:"优先级"`
	CreatedAt             *gtime.Time `json:"createdAt"             orm:"created_at"               description:"创建时间"`
	UpdatedAt             *gtime.Time `json:"updatedAt"             orm:"updated_at"               description:"更新时间"`
	DeletedAt             *gtime.Time `json:"deletedAt"             orm:"deleted_at"               description:"软删除时间（NULL表示未删除）"`
}
