// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
)

// Team is the golang structure for table team.
type Team struct {
	Id               int64       `json:"id"               orm:"id"                description:""`
	Category         string      `json:"category"         orm:"category"          description:"分类"`
	Name             string      `json:"name"             orm:"name"              description:"团队名"`
	TeamId           string      `json:"teamId"           orm:"team_id"           description:"团队业务id"`
	CreatedAt        *gtime.Time `json:"createdAt"        orm:"created_at"        description:""`
	UpdatedAt        *gtime.Time `json:"updatedAt"        orm:"updated_at"        description:""`
	DeletedAt        *gtime.Time `json:"deletedAt"        orm:"deleted_at"        description:""`
	ClusterNamespace string      `json:"clusterNamespace" orm:"cluster_namespace" description:"集群命名空间"`
	GpuQuota         string      `json:"gpuQuota"         orm:"gpu_quota"         description:"gpu配额"`
}
