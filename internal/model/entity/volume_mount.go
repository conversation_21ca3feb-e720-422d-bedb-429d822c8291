// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

// VolumeMount is the golang structure for table volume_mount.
type VolumeMount struct {
	Id         uint   `json:"id"         orm:"id"          description:"主键（唯一挂载ID）"`
	TaskId     uint   `json:"taskId"     orm:"task_id"     description:"关联训练任务ID（外键）"`
	Name       string `json:"name"       orm:"name"        description:"挂载名称（如\"data-volume\"）"`
	MountPath  string `json:"mountPath"  orm:"mount_path"  description:"容器内挂载路径（如\"/data\"）"`
	SubPath    string `json:"subPath"    orm:"sub_path"    description:"子路径（可选，如\"dataset/2023\"）"`
	VolumeType string `json:"volumeType" orm:"volume_type" description:"存储类型（如NFS、HostPath、PVC）"`
	VolumeName string `json:"volumeName" orm:"volume_name" description:"存储卷名称（如\"nfs-data-volume\"）"`
}
