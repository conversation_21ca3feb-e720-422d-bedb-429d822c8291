// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
)

// User is the golang structure for table user.
type User struct {
	Id         int64       `json:"id"         orm:"id"          description:"主键自增id"`
	Uid        string      `json:"uid"        orm:"uid"         description:"三方系统唯一id"`
	Username   string      `json:"username"   orm:"username"    description:"用户名"`
	NickName   string      `json:"nickName"   orm:"nick_name"   description:"昵称"`
	Email      string      `json:"email"      orm:"email"       description:"邮箱"`
	EmployeeNo string      `json:"employeeNo" orm:"employee_no" description:"工号"`
	CreatedAt  *gtime.Time `json:"createdAt"  orm:"created_at"  description:""`
	UpdatedAt  *gtime.Time `json:"updatedAt"  orm:"updated_at"  description:""`
	DeletedAt  *gtime.Time `json:"deletedAt"  orm:"deleted_at"  description:""`
	Password   string      `json:"password"   orm:"password"    description:""`
	IsActive   int         `json:"isActive"   orm:"is_active"   description:"是否激活"`
}
