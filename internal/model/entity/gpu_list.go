// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

// GpuList is the golang structure for table gpu_list.
type GpuList struct {
	Id        int    `json:"id"        orm:"id"         description:"主键自增id"`
	GpuType   string `json:"gpuType"   orm:"gpu_type"   description:"gpu类型"`
	GpuAlias  string `json:"gpuAlias"  orm:"gpu_alias"  description:"gpu简称"`
	GpuCore   string `json:"gpuCore"   orm:"gpu_core"   description:"gpu核心数"`
	GpuMemory string `json:"gpuMemory" orm:"gpu_memory" description:"显存"`
}
