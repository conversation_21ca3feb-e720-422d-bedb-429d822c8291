// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

// UserRoleTeamRela is the golang structure for table user_role_team_rela.
type UserRoleTeamRela struct {
	Id     int    `json:"id"     orm:"id"      description:""`
	UserId int    `json:"userId" orm:"user_id" description:""`
	RoleId int    `json:"roleId" orm:"role_id" description:""`
	TeamId string `json:"teamId" orm:"team_id" description:"team表业务id"`
}
