// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
)

// Setting is the golang structure for table setting.
type Setting struct {
	Id        uint        `json:"id"        orm:"id"         description:""`
	Key       string      `json:"key"       orm:"key"        description:""`
	Value     string      `json:"value"     orm:"value"      description:""`
	Category  string      `json:"category"  orm:"category"   description:""`
	Desc      string      `json:"desc"      orm:"desc"       description:""`
	CreatedAt *gtime.Time `json:"createdAt" orm:"created_at" description:""`
	UpdatedAt *gtime.Time `json:"updatedAt" orm:"updated_at" description:""`
	DeletedAt *gtime.Time `json:"deletedAt" orm:"deleted_at" description:""`
}
