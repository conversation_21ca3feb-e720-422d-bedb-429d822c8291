// =================================================================================
// This is auto-generated by GoFrame CLI tool only once. Fill this file as you wish.
// =================================================================================

package dao

import (
	"mlops/internal/dao/internal"
)

// internalTrainTaskDao is internal type for wrapping internal DAO implements.
type internalTrainTaskDao = *internal.TrainTaskDao

// trainTaskDao is the data access object for table tt_train_task.
// You can define custom methods on it to extend its functionality as you wish.
type trainTaskDao struct {
	internalTrainTaskDao
}

var (
	// TrainTask is globally public accessible object for table tt_train_task operations.
	TrainTask = trainTaskDao{
		internal.NewTrainTaskDao(),
	}
)

// Fill with you ideas below.
