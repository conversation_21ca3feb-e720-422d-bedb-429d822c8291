// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"mlops/internal/dao/internal"
)

// teamDao is the data access object for the table tt_team.
// You can define custom methods on it to extend its functionality as needed.
type teamDao struct {
	*internal.TeamDao
}

var (
	// Team is a globally accessible object for table tt_team operations.
	Team = teamDao{internal.NewTeamDao()}
)

// Add your custom methods and functionality below.
