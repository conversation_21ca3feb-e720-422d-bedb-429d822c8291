// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"mlops/internal/dao/internal"
)

// openapiTokenDao is the data access object for the table tt_openapi_token.
// You can define custom methods on it to extend its functionality as needed.
type openapiTokenDao struct {
	*internal.OpenapiTokenDao
}

var (
	// OpenapiToken is a globally accessible object for table tt_openapi_token operations.
	OpenapiToken = openapiTokenDao{internal.NewOpenapiTokenDao()}
)

// Add your custom methods and functionality below.
