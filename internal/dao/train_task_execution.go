// =================================================================================
// This is auto-generated by GoFrame CLI tool only once. Fill this file as you wish.
// =================================================================================

package dao

import (
	"mlops/internal/dao/internal"
)

// internalTrainTaskExecutionDao is internal type for wrapping internal DAO implements.
type internalTrainTaskExecutionDao = *internal.TrainTaskExecutionDao

// trainTaskExecutionDao is the data access object for table tt_train_task_execution.
// You can define custom methods on it to extend its functionality as you wish.
type trainTaskExecutionDao struct {
	internalTrainTaskExecutionDao
}

var (
	// TrainTaskExecution is globally public accessible object for table tt_train_task_execution operations.
	TrainTaskExecution = trainTaskExecutionDao{
		internal.NewTrainTaskExecutionDao(),
	}
)

// Fill with you ideas below.
