// =================================================================================
// This is auto-generated by GoFrame CLI tool only once. Fill this file as you wish.
// =================================================================================

package dao

import (
	"mlops/internal/dao/internal"
)

// internalClusterResourceDao is internal type for wrapping internal DAO implements.
type internalClusterResourceDao = *internal.ClusterResourceDao

// clusterResourceDao is the data access object for table tt_cluster_resource.
// You can define custom methods on it to extend its functionality as you wish.
type clusterResourceDao struct {
	internalClusterResourceDao
}

var (
	// ClusterResource is globally public accessible object for table tt_cluster_resource operations.
	ClusterResource = clusterResourceDao{
		internal.NewClusterResourceDao(),
	}
)

// Fill with you ideas below.
