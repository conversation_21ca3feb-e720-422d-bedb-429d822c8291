// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"mlops/internal/dao/internal"
)

// userRoleTeamRelaDao is the data access object for the table tt_user_role_team_rela.
// You can define custom methods on it to extend its functionality as needed.
type userRoleTeamRelaDao struct {
	*internal.UserRoleTeamRelaDao
}

var (
	// UserRoleTeamRela is a globally accessible object for table tt_user_role_team_rela operations.
	UserRoleTeamRela = userRoleTeamRelaDao{internal.NewUserRoleTeamRelaDao()}
)

// Add your custom methods and functionality below.
