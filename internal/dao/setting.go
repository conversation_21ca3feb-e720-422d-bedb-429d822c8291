// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"mlops/internal/dao/internal"
)

// settingDao is the data access object for the table tt_setting.
// You can define custom methods on it to extend its functionality as needed.
type settingDao struct {
	*internal.SettingDao
}

var (
	// Setting is a globally accessible object for table tt_setting operations.
	Setting = settingDao{internal.NewSettingDao()}
)

// Add your custom methods and functionality below.
