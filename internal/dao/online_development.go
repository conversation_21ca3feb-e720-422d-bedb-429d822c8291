// =================================================================================
// This is auto-generated by GoFrame CLI tool only once. Fill this file as you wish.
// =================================================================================

package dao

import (
	"mlops/internal/dao/internal"
)

// internalOnlineDevelopmentDao is internal type for wrapping internal DAO implements.
type internalOnlineDevelopmentDao = *internal.OnlineDevelopmentDao

// onlineDevelopmentDao is the data access object for table tt_online_development.
// You can define custom methods on it to extend its functionality as you wish.
type onlineDevelopmentDao struct {
	internalOnlineDevelopmentDao
}

var (
	// OnlineDevelopment is globally public accessible object for table tt_online_development operations.
	OnlineDevelopment = onlineDevelopmentDao{
		internal.NewOnlineDevelopmentDao(),
	}
)

// Fill with you ideas below.
