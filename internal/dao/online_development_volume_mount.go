// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"mlops/internal/dao/internal"
)

// onlineDevelopmentVolumeMountDao is the data access object for the table tt_online_development_volume_mount.
// You can define custom methods on it to extend its functionality as needed.
type onlineDevelopmentVolumeMountDao struct {
	*internal.OnlineDevelopmentVolumeMountDao
}

var (
	// OnlineDevelopmentVolumeMount is a globally accessible object for table tt_online_development_volume_mount operations.
	OnlineDevelopmentVolumeMount = onlineDevelopmentVolumeMountDao{internal.NewOnlineDevelopmentVolumeMountDao()}
)

// Add your custom methods and functionality below.
