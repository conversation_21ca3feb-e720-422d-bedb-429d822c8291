// =================================================================================
// This is auto-generated by GoFrame CLI tool only once. Fill this file as you wish.
// =================================================================================

package dao

import (
	"mlops/internal/dao/internal"
)

// internalVolumeMountDao is internal type for wrapping internal DAO implements.
type internalVolumeMountDao = *internal.VolumeMountDao

// volumeMountDao is the data access object for table tt_volume_mount.
// You can define custom methods on it to extend its functionality as you wish.
type volumeMountDao struct {
	internalVolumeMountDao
}

var (
	// VolumeMount is globally public accessible object for table tt_volume_mount operations.
	VolumeMount = volumeMountDao{
		internal.NewVolumeMountDao(),
	}
)

// Fill with you ideas below.
