// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"mlops/internal/dao/internal"
)

// onlineDevelopmentClusterResourceDao is the data access object for the table tt_online_development_cluster_resource.
// You can define custom methods on it to extend its functionality as needed.
type onlineDevelopmentClusterResourceDao struct {
	*internal.OnlineDevelopmentClusterResourceDao
}

var (
	// OnlineDevelopmentClusterResource is a globally accessible object for table tt_online_development_cluster_resource operations.
	OnlineDevelopmentClusterResource = onlineDevelopmentClusterResourceDao{internal.NewOnlineDevelopmentClusterResourceDao()}
)

// Add your custom methods and functionality below.
