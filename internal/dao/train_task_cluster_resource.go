// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"mlops/internal/dao/internal"
)

// trainTaskClusterResourceDao is the data access object for the table tt_train_task_cluster_resource.
// You can define custom methods on it to extend its functionality as needed.
type trainTaskClusterResourceDao struct {
	*internal.TrainTaskClusterResourceDao
}

var (
	// TrainTaskClusterResource is a globally accessible object for table tt_train_task_cluster_resource operations.
	TrainTaskClusterResource = trainTaskClusterResourceDao{internal.NewTrainTaskClusterResourceDao()}
)

// Add your custom methods and functionality below.
