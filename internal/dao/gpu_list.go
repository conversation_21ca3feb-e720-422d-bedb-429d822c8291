// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"mlops/internal/dao/internal"
)

// gpuListDao is the data access object for the table tt_gpu_list.
// You can define custom methods on it to extend its functionality as needed.
type gpuListDao struct {
	*internal.GpuListDao
}

var (
	// GpuList is a globally accessible object for table tt_gpu_list operations.
	GpuList = gpuListDao{internal.NewGpuListDao()}
)

// Add your custom methods and functionality below.
