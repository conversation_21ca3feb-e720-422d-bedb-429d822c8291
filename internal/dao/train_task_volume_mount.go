// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"mlops/internal/dao/internal"
)

// trainTaskVolumeMountDao is the data access object for the table tt_train_task_volume_mount.
// You can define custom methods on it to extend its functionality as needed.
type trainTaskVolumeMountDao struct {
	*internal.TrainTaskVolumeMountDao
}

var (
	// TrainTaskVolumeMount is a globally accessible object for table tt_train_task_volume_mount operations.
	TrainTaskVolumeMount = trainTaskVolumeMountDao{internal.NewTrainTaskVolumeMountDao()}
)

// Add your custom methods and functionality below.
