// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// OpenapiTokenDao is the data access object for the table tt_openapi_token.
type OpenapiTokenDao struct {
	table    string              // table is the underlying table name of the DAO.
	group    string              // group is the database configuration group name of the current DAO.
	columns  OpenapiTokenColumns // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler  // handlers for customized model modification.
}

// OpenapiTokenColumns defines and stores column names for the table tt_openapi_token.
type OpenapiTokenColumns struct {
	Id        string //
	Name      string // 唯一标识
	Token     string // token
	Desc      string // 描述
	CreatedAt string //
	UpdatedAt string //
	DeletedAt string //
}

// openapiTokenColumns holds the columns for the table tt_openapi_token.
var openapiTokenColumns = OpenapiTokenColumns{
	Id:        "id",
	Name:      "name",
	Token:     "token",
	Desc:      "desc",
	CreatedAt: "created_at",
	UpdatedAt: "updated_at",
	DeletedAt: "deleted_at",
}

// NewOpenapiTokenDao creates and returns a new DAO object for table data access.
func NewOpenapiTokenDao(handlers ...gdb.ModelHandler) *OpenapiTokenDao {
	return &OpenapiTokenDao{
		group:    "default",
		table:    "tt_openapi_token",
		columns:  openapiTokenColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *OpenapiTokenDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *OpenapiTokenDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *OpenapiTokenDao) Columns() OpenapiTokenColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *OpenapiTokenDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *OpenapiTokenDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *OpenapiTokenDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
