// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// OnlineDevelopmentDao is the data access object for the table tt_online_development.
type OnlineDevelopmentDao struct {
	table    string                   // table is the underlying table name of the DAO.
	group    string                   // group is the database configuration group name of the current DAO.
	columns  OnlineDevelopmentColumns // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler       // handlers for customized model modification.
}

// OnlineDevelopmentColumns defines and stores column names for the table tt_online_development.
type OnlineDevelopmentColumns struct {
	Id                  string // 主键（自增ID）
	DevName             string // 开发环境名称
	ClusterName         string // 集群名称
	ClusterId           string // 集群ID
	Namespace           string // 命名空间
	ImageUrl            string // 镜像地址
	DevUrl              string // 开发环境URL（唯一访问地址）
	CreatedByUserName   string // 创建人
	CreatedByEmployeeNo string //
	UpdatedByUserName   string // 最后更新人
	UpdatedByEmployeeNo string //
	CreatedAt           string // 创建时间
	UpdatedAt           string // 更新时间
	DeletedAt           string // 软删除时间（NULL表示未删除）
	TeamId              string // 团队id
	TeamName            string // 团队名
	AppName             string // cicd应用名
	CmdbId              string //
	Type                string // 开发环境类型
	Password            string //
	Env                 string //
	Status              string //
}

// onlineDevelopmentColumns holds the columns for the table tt_online_development.
var onlineDevelopmentColumns = OnlineDevelopmentColumns{
	Id:                  "id",
	DevName:             "dev_name",
	ClusterName:         "cluster_name",
	ClusterId:           "cluster_id",
	Namespace:           "namespace",
	ImageUrl:            "image_url",
	DevUrl:              "dev_url",
	CreatedByUserName:   "created_by_user_name",
	CreatedByEmployeeNo: "created_by_employee_no",
	UpdatedByUserName:   "updated_by_user_name",
	UpdatedByEmployeeNo: "updated_by_employee_no",
	CreatedAt:           "created_at",
	UpdatedAt:           "updated_at",
	DeletedAt:           "deleted_at",
	TeamId:              "team_id",
	TeamName:            "team_name",
	AppName:             "app_name",
	CmdbId:              "cmdb_id",
	Type:                "type",
	Password:            "password",
	Env:                 "env",
	Status:              "status",
}

// NewOnlineDevelopmentDao creates and returns a new DAO object for table data access.
func NewOnlineDevelopmentDao(handlers ...gdb.ModelHandler) *OnlineDevelopmentDao {
	return &OnlineDevelopmentDao{
		group:    "default",
		table:    "tt_online_development",
		columns:  onlineDevelopmentColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *OnlineDevelopmentDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *OnlineDevelopmentDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *OnlineDevelopmentDao) Columns() OnlineDevelopmentColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *OnlineDevelopmentDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *OnlineDevelopmentDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *OnlineDevelopmentDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
