// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// OnlineDevelopmentVolumeMountDao is the data access object for the table tt_online_development_volume_mount.
type OnlineDevelopmentVolumeMountDao struct {
	table    string                              // table is the underlying table name of the DAO.
	group    string                              // group is the database configuration group name of the current DAO.
	columns  OnlineDevelopmentVolumeMountColumns // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler                  // handlers for customized model modification.
}

// OnlineDevelopmentVolumeMountColumns defines and stores column names for the table tt_online_development_volume_mount.
type OnlineDevelopmentVolumeMountColumns struct {
	Id         string // 主键（唯一挂载ID）
	DevId      string // 关联训练任务ID（外键）
	Name       string // 挂载名称（如"data-volume"）
	MountPath  string // 容器内挂载路径（如"/data"）
	SubPath    string // 子路径（可选，如"dataset/2023"）
	VolumeType string // 存储类型（如NFS、HostPath、PVC）
	VolumeName string // 存储卷名称（如"nfs-data-volume"）
	CreatedAt  string // 创建时间
	UpdatedAt  string // 更新时间
	DeletedAt  string // 软删除时间（NULL表示未删除）
}

// onlineDevelopmentVolumeMountColumns holds the columns for the table tt_online_development_volume_mount.
var onlineDevelopmentVolumeMountColumns = OnlineDevelopmentVolumeMountColumns{
	Id:         "id",
	DevId:      "dev_id",
	Name:       "name",
	MountPath:  "mount_path",
	SubPath:    "sub_path",
	VolumeType: "volume_type",
	VolumeName: "volume_name",
	CreatedAt:  "created_at",
	UpdatedAt:  "updated_at",
	DeletedAt:  "deleted_at",
}

// NewOnlineDevelopmentVolumeMountDao creates and returns a new DAO object for table data access.
func NewOnlineDevelopmentVolumeMountDao(handlers ...gdb.ModelHandler) *OnlineDevelopmentVolumeMountDao {
	return &OnlineDevelopmentVolumeMountDao{
		group:    "default",
		table:    "tt_online_development_volume_mount",
		columns:  onlineDevelopmentVolumeMountColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *OnlineDevelopmentVolumeMountDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *OnlineDevelopmentVolumeMountDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *OnlineDevelopmentVolumeMountDao) Columns() OnlineDevelopmentVolumeMountColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *OnlineDevelopmentVolumeMountDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *OnlineDevelopmentVolumeMountDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *OnlineDevelopmentVolumeMountDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
