// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// ClusterResourceDao is the data access object for the table tt_cluster_resource.
type ClusterResourceDao struct {
	table    string                 // table is the underlying table name of the DAO.
	group    string                 // group is the database configuration group name of the current DAO.
	columns  ClusterResourceColumns // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler     // handlers for customized model modification.
}

// ClusterResourceColumns defines and stores column names for the table tt_cluster_resource.
type ClusterResourceColumns struct {
	Id               string // 资源主键
	TaskId           string // 关联训练任务ID（外键）
	MinReplicas      string // 最小副本数
	MaxReplicas      string // 最大副本数
	RequestCpu       string // 请求CPU资源（如"2"表示2核）
	RequestMemory    string // 请求内存（如"4Gi"）
	RequestGpuCore   string // 请求GPU核心数
	RequestGpuMemory string // 请求GPU显存
	LimitCpu         string // CPU限制（如"2000m"）
	LimitMemory      string // 内存限制（如"8Gi"）
	LimitGpuCore     string // GPU核心限制
	LimitGpuMemory   string // GPU显存限制
}

// clusterResourceColumns holds the columns for the table tt_cluster_resource.
var clusterResourceColumns = ClusterResourceColumns{
	Id:               "id",
	TaskId:           "task_id",
	MinReplicas:      "min_replicas",
	MaxReplicas:      "max_replicas",
	RequestCpu:       "request_cpu",
	RequestMemory:    "request_memory",
	RequestGpuCore:   "request_gpu_core",
	RequestGpuMemory: "request_gpu_memory",
	LimitCpu:         "limit_cpu",
	LimitMemory:      "limit_memory",
	LimitGpuCore:     "limit_gpu_core",
	LimitGpuMemory:   "limit_gpu_memory",
}

// NewClusterResourceDao creates and returns a new DAO object for table data access.
func NewClusterResourceDao(handlers ...gdb.ModelHandler) *ClusterResourceDao {
	return &ClusterResourceDao{
		group:    "default",
		table:    "tt_cluster_resource",
		columns:  clusterResourceColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *ClusterResourceDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *ClusterResourceDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *ClusterResourceDao) Columns() ClusterResourceColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *ClusterResourceDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *ClusterResourceDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *ClusterResourceDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
