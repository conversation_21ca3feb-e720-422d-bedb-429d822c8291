package team

const (
	PriId            = "id"
	TeamId           = "TeamId"
	Name             = "name"
	GpuQuota         = "gpu_quota"
	ClusterNamespace = "cluster_namespace"
)

var validGpuType map[string]struct{}

func init() {
	validGpuType = map[string]struct{}{
		"NVIDIA L20":            {},
		"NVIDIA H20":            {},
		"Tesla A100 80G":        {},
		"H800":                  {},
		"A800":                  {},
		"NVIDIA Tesla A40":      {},
		"NVIDIA Tesla A30":      {},
		"GP7V":                  {},
		"NVIDIA G49E":           {},
		"NVIDIA Quadro RTX5000": {},
		"GM402":                 {},
		"NVIDIA A800-SXM4-80GB": {},
		"NVIDIA 3080":           {},
		"NVIDIA Tesla P4":       {},
	}
}

func IsValidGpuType(gpuType string) bool {
	_, ok := validGpuType[gpuType]
	return ok
}
