package consts

import "fmt"

const (
	CateSystem = "system" // 系统分类
	CateDoc    = "doc"    // 文档分类
	CateTest   = "test"   // 测试分类
)

const (
	// TeamRelatedClusterNamespaceKey 团队关联的集群和空间数据，尾缀是team表id
	TeamRelatedClusterNamespaceKey = "team-related-clusternamespace-%d"

	// OnlineDevelopmentImageKey 在线开发可以使用的镜像列表
	OnlineDevelopmentImageKey = "online-development-image"
)

func GenerateTeamRelatedClusterNamespaceKey(teamId int) string {
	return fmt.Sprintf(TeamRelatedClusterNamespaceKey, teamId)
}
