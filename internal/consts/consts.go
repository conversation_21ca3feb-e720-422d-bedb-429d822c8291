package consts

const (
	// web system
	Version           = "v1.0.0" // server version
	VersionUpdateTime = "2025-05-20"

	ServerName = "Mlops"

	DefaultCipherKey = "dHQtbWxvcHMtYmFja2VuZAo=" // echo "tt-mlops-backend" |base64
	ContextKey       = "ContextKey"               // 上下文变量存储键名，前后端系统共享
	DateTimeFormat   = "2006-01-02 15:04:05"

	STrue  = 1 // system true
	SFalse = 0 // system false

	OrderAsc  = "asc"
	OrderDesc = "desc"
	WhereAnd  = "and"
	WhereOr   = "or"

	// SSO ctx key
	SSO = "SSO_AUTH"

	MlopsCookieKey = "Authorization"
)

// business
const (
	OnlineDevelopmentStatusRunning  = "running"
	OnlineDevelopmentStatusShutdown = "shutdown"
	OnlineDevelopmentStatusPending  = "pending"
)

// Train Task Execution Status
const (
	TrainTaskExecutionStatusPending   = "PENDING"
	TrainTaskExecutionStatusRunning   = "RUNNING"
	TrainTaskExecutionStatusSucceeded = "SUCCEEDED"
	TrainTaskExecutionStatusFailed    = "FAILED"
	TrainTaskExecutionStatusCancelled = "CANCELLED"
	TrainTaskExecutionStatusStopped   = "STOPPED"
)

// Trigger Source
const (
	TriggerSourceManual    = "MANUAL"
	TriggerSourceScheduled = "SCHEDULED"
	TriggerSourceAPICall   = "API_CALL"
)

// Priority
const (
	PriorityP0 = "P0"
	PriorityP1 = "P1"
	PriorityP2 = "P2"
	PriorityP3 = "P3"
)

// Training Framework
const (
	TrainingFrameworkRay      = "RAY"
	TrainingFrameworkCustom   = "CUSTOM"
	TrainingFrameworkTFJob    = "TFJOB"
	TrainingFrameworkPyTorch  = "PYTORCH"
	TrainingFrameworkMPI      = "MPI"
)

// Task Type
const (
	TaskTypeForm = "FORM"
	TaskTypeYaml = "YAML"
)

// Cluster Type
const (
	ClusterTypeTencent    = "tencent"
	ClusterTypeAliyun     = "aliyun"
	ClusterTypeHuawei     = "huawei"
	ClusterTypeVolcengine = "volcengine"
)

type OnlineDevType string

const (
	OnlineDevTypeCodeServer OnlineDevType = "code-server"
	OnlineDevTypeJupyter    OnlineDevType = "jupyter"
)

func (t OnlineDevType) ToString() string {
	return string(t)
}

const (
	SidecarIstioInjectKey     = "sidecar.istio.io/inject"
	SidecarIstioInjectValue   = "false"
	TeamIdLabelKey            = "ml_team_id"
	TaskExecutionNameLabelKey = "ml_exec_name"
	LabelUuidKey              = "label_uuid"
	EnvLabelKey               = "ml_env"
)
