package auth

// TeamRoleKind 团队角色类型
type TeamRoleKind string

// TeamKind 团队类型
type TeamKind string

// RoleKind 角色类型
type RoleKind string

const (
	TeamRoleAdmin TeamRoleKind = "admin"
	TeamRoleUser  TeamRoleKind = "user"

	TeamOrganization TeamKind = "organization" // 组织架构
	TeamFeature      TeamKind = "feature"      // 特性团队

	RoleAdmin              RoleKind = "admin"
	RoleKnowledgeBaseAdmin RoleKind = "knowledgeBaseAdmin"
	RoleConsoleAdmin       RoleKind = "consoleAdmin"
	RoleTeamAdmin          RoleKind = "teamAdmin"
	RoleUser               RoleKind = "user"

	DefaultTeamId = "default" // 默认团队, 用于授权 控制台、系统管理员时对关联表中teamId字段的默认填充

	CicdDevAdmin = "DEV_ADMIN" // cicd团队中的研发管理者
)
