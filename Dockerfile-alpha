FROM cr.ttyuyin.com/devops/golang:1.23 as builder
ENV CGO_ENABLED 0
ENV GOOS linux
ENV GOPROXY https://goproxy.cn,direct
ENV GOPRIVATE gitlab.ttyuyin.com
ENV GONOPROXY *.ttyuyin.com

WORKDIR /build/intelliforge

RUN git config --global url."**********************:".insteadof https://gitlab.ttyuyin.com/

RUN mkdir -p /root/.ssh
COPY manifest/deploy/id_rsa /root/.ssh/
COPY manifest/deploy/known_hosts /root/.ssh/
RUN chmod 600 /root/.ssh/id_rsa

ADD go.mod .
ADD go.sum .
RUN CGO_ENABLED=0 go mod download
COPY . .
COPY manifest/config/config.yaml /app/config/config.yaml
COPY manifest/config/sso-config-alpha.yaml /app/config/sso-config.yaml

RUN CGO_ENABLED=0 go build -ldflags="-s -w" -o /app/main main.go

FROM cr.ttyuyin.com/public/ubuntu:latest as prod

WORKDIR /app

COPY --from=builder /usr/share/zoneinfo/Asia/Shanghai /usr/share/zoneinfo/Asia/Shanghai
COPY --from=builder /app/main /app/main
COPY --from=builder /app/config /app/config

ENV TZ Asia/Shanghai

EXPOSE 8000

CMD ["./main", "-f", "config/config.yaml"]