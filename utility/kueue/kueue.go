package kueue

import (
	"context"
	"fmt"
	"sort"
	"strconv"
	"strings"

	"mlops/internal/consts"
	"mlops/internal/model/dto"
	"mlops/tools/client"
	"mlops/tools/gpu"

	"gitlab.ttyuyin.com/yunwei-infra/tt-cloud-constack-sdk/pkg/log"
	constack_openapi "gitlab.ttyuyin.com/yunwei-infra/tt-cloud-constack-sdk/pkg/resources/constack/api.v1/openapi"
	v1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/api/resource"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	kueuev1alpha1 "sigs.k8s.io/kueue/apis/kueue/v1alpha1"
	kueuev1beta1 "sigs.k8s.io/kueue/apis/kueue/v1beta1"
)

// Kueue 相关常量
const (
	ClusterQueuePrefix   = "mlops-team-"
	LocalQueuePrefix     = "mlops-team-"
	ResourceFlavorPrefix = "mlops-gpu-"
	ManagedByLabel       = "managed-by"
	ManagedByValue       = "mlops"
	TeamIdLabel          = "mlops.ttyuyin.com/team-id"
	GpuFullNameLabel     = "mlops.ttyuyin.com/gpu-fullname"
	PriorityLabel        = "mlops.ttyuyin.com/priority"
	NodeLabelGpuModel    = "gpu-model"
	TolerationKey        = "pool-type"
	TolerationValue      = "gpu"
	CohortName           = "mlops-shared"
	QueueingStrategy     = "BestEffortFIFO"
	FixedCpuQuota        = "999"
	FixedMemoryQuota     = "2Ti"
	LendingLimitPercent  = 20
	GpuCoreKey           = "52tt.com/gpu-core"
	GpuMemoryKey         = "52tt.com/gpu-memory"
	BizTolerationKey     = "biz"
	BizTolerationValue   = "mlops"

	// Kueue 工作负载标签
	QueueNameLabel     = "kueue.x-k8s.io/queue-name"
	PriorityClassLabel = "kueue.x-k8s.io/priority-class"
	JobUidLabel        = "kueue.x-k8s.io/job-uid"
)

// Kueue Resource Kind
const (
	ClusterQueueKind          = "ClusterQueue"
	LocalQueueKind            = "LocalQueue"
	ResourceFlavorKind        = "ResourceFlavor"
	WorkloadPriorityClassKind = "WorkloadPriorityClass"
	CohortKind                = "Cohort"
	WorkloadKind              = "Workload"
)

// 优先级映射
var PriorityValueMap = map[string]int32{
	"P0": 1900,
	"P1": 1800,
	"P2": 1700,
	"P3": 1600,
}

var GPTTypeMap = map[string]string{
	"NVIDIA L20":            "L20",
	"Tesla A100 80G":        "A100",
	"NVIDIA A800-SXM4-80GB": "A800",
	"NVIDIA H20":            "H20",
}

// GetClusterType 获取集群类型
func GetClusterType(clusterName string) string {
	switch {
	case strings.HasPrefix(clusterName, "k8s-tc"):
		return consts.ClusterTypeTencent
	case strings.HasPrefix(clusterName, "k8s-ali"):
		return consts.ClusterTypeAliyun
	case strings.HasPrefix(clusterName, "k8s-hw"):
		return consts.ClusterTypeHuawei
	case strings.HasPrefix(clusterName, "k8s-hs"):
		return consts.ClusterTypeVolcengine
	default:
		return ""
	}
}

// UpdateClusterKueueResources 更新单个集群的 Kueue 资源
func UpdateClusterKueueResources(ctx context.Context, teamId int, clusterName string, namespaces []string, quotas []*dto.GpuQuota) error {
	// 1. 确认 Cohort 资源
	err := EnsureCohort(ctx, clusterName)
	if err != nil {
		return fmt.Errorf("确认 Cohort 失败: %w", err)
	}

	// 2. 确认 ResourceFlavor 资源
	resourceFlavorMap, err := EnsureResourceFlavors(ctx, clusterName, quotas)
	if err != nil {
		return fmt.Errorf("确认 ResourceFlavor 失败: %w", err)
	}

	// 3. 更新或创建 ClusterQueue
	err = UpdateOrCreateClusterQueue(ctx, teamId, clusterName, resourceFlavorMap)
	if err != nil {
		return fmt.Errorf("更新 ClusterQueue 失败: %w", err)
	}

	// 4. 更新或创建 LocalQueue
	err = UpdateOrCreateLocalQueues(ctx, teamId, clusterName, namespaces)
	if err != nil {
		return fmt.Errorf("更新 LocalQueue 失败: %w", err)
	}

	return nil
}

// EnsureResourceFlavors 确保 ResourceFlavor 存在
func EnsureResourceFlavors(ctx context.Context, clusterName string, quotas []*dto.GpuQuota) (map[string]*dto.GpuQuota, error) {
	resourceFlavorMap := make(map[string]*dto.GpuQuota)

	for _, quota := range quotas {
		// 构造 GPU 全名标签值
		gpuFullName := strings.ReplaceAll(quota.GpuType, " ", "-")

		// 提取 GPU 型号
		gpuModel, ok := GPTTypeMap[quota.GpuType]
		if !ok {
			return resourceFlavorMap, fmt.Errorf("无法提取 GPU 型号: %s", quota.GpuType)
		}

		resourceFlavorName := ResourceFlavorPrefix + strings.ToLower(gpuModel)

		// 尝试获取现有的 ResourceFlavor
		existingRF, err := client.GetResourceFlavor(ctx, clusterName, resourceFlavorName)
		if err != nil || existingRF == nil {
			// 创建新的 ResourceFlavor
			err = CreateResourceFlavor(ctx, clusterName, resourceFlavorName, gpuFullName, gpuModel)
			if err != nil {
				return resourceFlavorMap, fmt.Errorf("创建 ResourceFlavor %s 失败: %w", resourceFlavorName, err)
			}
		}

		resourceFlavorMap[resourceFlavorName] = quota
	}

	return resourceFlavorMap, nil
}

// CreateResourceFlavor 创建 ResourceFlavor
func CreateResourceFlavor(ctx context.Context, clusterName, name, gpuFullName, gpuModel string) error {
	resourceFlavor := &kueuev1beta1.ResourceFlavor{
		TypeMeta: metav1.TypeMeta{
			APIVersion: client.KueueGroup + "/" + client.KueueVersion,
			Kind:       ResourceFlavorKind,
		},
		ObjectMeta: metav1.ObjectMeta{
			Name: name,
			Labels: map[string]string{
				ManagedByLabel:   ManagedByValue,
				GpuFullNameLabel: gpuFullName,
			},
		},
		Spec: kueuev1beta1.ResourceFlavorSpec{
			NodeLabels: map[string]string{
				NodeLabelGpuModel: strings.ToLower(gpuModel),
			},
			Tolerations: []v1.Toleration{
				{
					Key:      TolerationKey,
					Operator: v1.TolerationOpEqual,
					Value:    TolerationValue,
					Effect:   v1.TaintEffectNoSchedule,
				},
				{
					Key:      BizTolerationKey,
					Operator: v1.TolerationOpEqual,
					Value:    BizTolerationValue,
					Effect:   v1.TaintEffectNoSchedule,
				},
			},
		},
	}

	return client.CreateResourceFlavor(ctx, clusterName, resourceFlavor)
}

// UpdateOrCreateClusterQueue 更新或创建 ClusterQueue
func UpdateOrCreateClusterQueue(ctx context.Context, teamId int, clusterName string, resourceFlavorMap map[string]*dto.GpuQuota) error {
	clusterQueueName := ClusterQueuePrefix + strconv.Itoa(teamId)

	// 获取集群类型对应的资源名称
	clusterType := GetClusterType(clusterName)
	converter := gpu.GetStrategy(clusterType)

	// 定义固定的资源顺序
	var coveredResources []v1.ResourceName
	if len(resourceFlavorMap) > 0 {
		// 使用固定的资源顺序，而不是依赖 map 遍历
		baseResources := []v1.ResourceName{
			v1.ResourceName(GpuCoreKey),
			v1.ResourceName(GpuMemoryKey),
			v1.ResourceCPU,
			v1.ResourceMemory,
		}

		// 取第一个 quota 来确定哪些资源会被转换器修改
		var firstQuota *dto.GpuQuota
		for _, quota := range resourceFlavorMap {
			firstQuota = quota
			break
		}

		// 构建标准的资源映射来确定最终的资源类型
		resourcesMap := make(map[v1.ResourceName]resource.Quantity)
		resourcesMap[v1.ResourceName(GpuCoreKey)], _ = resource.ParseQuantity(strconv.Itoa(int(firstQuota.Nums) * 100))
		resourcesMap[v1.ResourceName(GpuMemoryKey)], _ = resource.ParseQuantity(firstQuota.GpuMemory)
		resourcesMap[v1.ResourceCPU] = resource.MustParse(FixedCpuQuota)
		resourcesMap[v1.ResourceMemory] = resource.MustParse(FixedMemoryQuota)
		if converter != nil {
			converter.Convert(resourcesMap)
		}

		// 按照基础资源的固定顺序，检查哪些资源存在于转换后的映射中
		for _, resourceName := range baseResources {
			if _, exists := resourcesMap[resourceName]; exists {
				coveredResources = append(coveredResources, resourceName)
			}
		}

		// 添加转换器可能新增的资源（按字母顺序排序以保证一致性）
		var additionalResources []v1.ResourceName
		for resourceName := range resourcesMap {
			found := false
			for _, baseResource := range baseResources {
				if resourceName == baseResource {
					found = true
					break
				}
			}
			if !found {
				additionalResources = append(additionalResources, resourceName)
			}
		}
		// 对额外资源进行排序以保证一致性
		sort.Slice(additionalResources, func(i, j int) bool {
			return string(additionalResources[i]) < string(additionalResources[j])
		})
		coveredResources = append(coveredResources, additionalResources...)
	}

	var flavors []kueuev1beta1.FlavorQuotas
	for resourceFlavorName, quota := range resourceFlavorMap {
		// 不同集群类型的资源大小转化
		resourcesMap := make(map[v1.ResourceName]resource.Quantity)
		resourcesMap[v1.ResourceName(GpuCoreKey)], _ = resource.ParseQuantity(strconv.Itoa(int(quota.Nums) * 100))
		resourcesMap[v1.ResourceName(GpuMemoryKey)], _ = resource.ParseQuantity(quota.GpuMemory)
		resourcesMap[v1.ResourceCPU] = resource.MustParse(FixedCpuQuota)
		resourcesMap[v1.ResourceMemory] = resource.MustParse(FixedMemoryQuota)
		if converter != nil {
			converter.Convert(resourcesMap)
		}

		// 严格按照 coveredResources 的顺序构建 resourceQuotas
		var resourceQuotas []kueuev1beta1.ResourceQuota
		for _, resourceName := range coveredResources {
			if resourceQuota, exists := resourcesMap[resourceName]; exists {
				// 计算 LendingLimit (NominalQuota 的 20%)，使用格式化函数
				lendingLimit := formatLendingLimit(resourceQuota, LendingLimitPercent)

				resourceQuotas = append(resourceQuotas, kueuev1beta1.ResourceQuota{
					Name:         resourceName,
					NominalQuota: resourceQuota,
					LendingLimit: &lendingLimit,
				})
			}
		}

		flavor := kueuev1beta1.FlavorQuotas{
			Name:      kueuev1beta1.ResourceFlavorReference(resourceFlavorName),
			Resources: resourceQuotas,
		}
		flavors = append(flavors, flavor)
	}

	clusterQueue := &kueuev1beta1.ClusterQueue{
		TypeMeta: metav1.TypeMeta{
			APIVersion: client.KueueGroup + "/" + client.KueueVersion,
			Kind:       ClusterQueueKind,
		},
		ObjectMeta: metav1.ObjectMeta{
			Name: clusterQueueName,
			Labels: map[string]string{
				ManagedByLabel: ManagedByValue,
				TeamIdLabel:    strconv.Itoa(teamId),
			},
		},
		Spec: kueuev1beta1.ClusterQueueSpec{
			NamespaceSelector: &metav1.LabelSelector{},
			Cohort:            CohortName,
			QueueingStrategy:  kueuev1beta1.BestEffortFIFO,
		},
	}

	if len(coveredResources) > 0 {
		clusterQueue.Spec.ResourceGroups = []kueuev1beta1.ResourceGroup{
			{
				CoveredResources: coveredResources,
				Flavors:          flavors,
			},
		}
	}

	// 尝试获取现有的 ClusterQueue
	existingCQ, err := client.GetClusterQueue(ctx, clusterName, clusterQueueName)
	if err != nil || existingCQ == nil {
		// 创建新的 ClusterQueue
		return client.CreateClusterQueue(ctx, clusterName, clusterQueue)
	} else {
		// 更新现有的 ClusterQueue
		existingCQ.Spec = clusterQueue.Spec
		return client.UpdateClusterQueue(ctx, clusterName, existingCQ)
	}
}

// formatLendingLimit 格式化 LendingLimit，使其具有合适的单位
func formatLendingLimit(nominalQuota resource.Quantity, percent int) resource.Quantity {
	// 计算原始值
	originalValue := nominalQuota.Value() * int64(percent) / 100

	// 根据资源类型和大小选择合适的单位
	nominalStr := nominalQuota.String()

	// 内存相关资源
	if strings.Contains(nominalStr, "Gi") || strings.Contains(nominalStr, "Ti") || strings.Contains(nominalStr, "Mi") {
		// 如果原始值大于等于1Ti，使用Ti
		if originalValue >= 1024*1024*1024*1024 {
			tiValue := originalValue / (1024 * 1024 * 1024 * 1024)
			return resource.MustParse(fmt.Sprintf("%dTi", tiValue))
		}
		// 如果原始值大于等于1Gi，使用Gi
		if originalValue >= 1024*1024*1024 {
			giValue := originalValue / (1024 * 1024 * 1024)
			return resource.MustParse(fmt.Sprintf("%dGi", giValue))
		}
		// 否则使用Mi
		miValue := originalValue / (1024 * 1024)
		return resource.MustParse(fmt.Sprintf("%dMi", miValue))
	}

	// CPU 相关资源（通常是毫核）
	if strings.Contains(nominalStr, "m") || nominalQuota.MilliValue() > nominalQuota.Value()*1000 {
		// 如果结果可以整除1000，使用核心数
		if originalValue%1000 == 0 {
			return resource.MustParse(fmt.Sprintf("%d", originalValue/1000))
		}
		// 否则使用毫核
		return resource.MustParse(fmt.Sprintf("%dm", originalValue))
	}

	// GPU 相关资源或其他整数资源
	return resource.MustParse(fmt.Sprintf("%d", originalValue))
}

// UpdateOrCreateLocalQueues 更新或创建 LocalQueue
func UpdateOrCreateLocalQueues(ctx context.Context, teamId int, clusterName string, namespaces []string) error {
	localQueueName := LocalQueuePrefix + strconv.Itoa(teamId)
	clusterQueueName := kueuev1beta1.ClusterQueueReference(ClusterQueuePrefix + strconv.Itoa(teamId))

	for _, namespace := range namespaces {
		localQueue := &kueuev1beta1.LocalQueue{
			TypeMeta: metav1.TypeMeta{
				APIVersion: client.KueueGroup + "/" + client.KueueVersion,
				Kind:       LocalQueueKind,
			},
			ObjectMeta: metav1.ObjectMeta{
				Name:      localQueueName,
				Namespace: namespace,
				Labels: map[string]string{
					ManagedByLabel: ManagedByValue,
					TeamIdLabel:    strconv.Itoa(teamId),
				},
			},
			Spec: kueuev1beta1.LocalQueueSpec{
				ClusterQueue: clusterQueueName,
			},
		}

		// 尝试获取现有的 LocalQueue
		existingLQ, err := client.GetLocalQueue(ctx, clusterName, namespace, localQueueName)
		if err != nil || existingLQ == nil {
			// 创建新的 LocalQueue
			err = client.CreateLocalQueue(ctx, clusterName, localQueue)
			if err != nil {
				log.L.WithName("UpdateOrCreateLocalQueues").Errorf(ctx, "创建 LocalQueue %s/%s 失败: %s", namespace, localQueueName, err.Error())
			}
		} else {
			// 检查并更新 ClusterQueue 引用
			if existingLQ.Spec.ClusterQueue != clusterQueueName {
				existingLQ.Spec.ClusterQueue = clusterQueueName
				err = client.UpdateLocalQueue(ctx, clusterName, existingLQ)
				if err != nil {
					log.L.WithName("UpdateOrCreateLocalQueues").Errorf(ctx, "更新 LocalQueue %s/%s 失败: %s", namespace, localQueueName, err.Error())
				}
			}
		}
	}

	return nil
}

// EnsureWorkloadPriorityClass 确保 WorkloadPriorityClass 存在
func EnsureWorkloadPriorityClass(ctx context.Context, clusterName, priority string) error {
	// 构造名称
	name := CohortName + "-" + strings.ToLower(priority)

	// 尝试获取现有的 WorkloadPriorityClass
	existingWPC, err := client.GetWorkloadPriorityClass(ctx, clusterName, name)
	if err != nil || existingWPC == nil {
		// 创建新的 WorkloadPriorityClass
		return CreateWorkloadPriorityClass(ctx, clusterName, priority)
	}

	return nil
}

// CreateWorkloadPriorityClass 创建 WorkloadPriorityClass
func CreateWorkloadPriorityClass(ctx context.Context, clusterName, priority string) error {
	// 获取优先级对应的值
	value, exists := PriorityValueMap[priority]
	if !exists {
		return fmt.Errorf("不支持的优先级: %s", priority)
	}

	// 构造名称
	name := CohortName + "-" + strings.ToLower(priority)

	workloadPriorityClass := &kueuev1beta1.WorkloadPriorityClass{
		TypeMeta: metav1.TypeMeta{
			APIVersion: client.KueueGroup + "/" + client.KueueVersion,
			Kind:       WorkloadPriorityClassKind,
		},
		ObjectMeta: metav1.ObjectMeta{
			Name: name,
			Labels: map[string]string{
				ManagedByLabel: ManagedByValue,
				PriorityLabel:  priority,
			},
		},
		Value: value,
	}

	return client.CreateWorkloadPriorityClass(ctx, clusterName, workloadPriorityClass)
}

// EnsureCohort 确保 Cohort 存在
func EnsureCohort(ctx context.Context, clusterName string) error {
	// 尝试获取现有的 Cohort
	existingCohort, err := client.GetCohort(ctx, clusterName, CohortName)
	if err != nil || existingCohort == nil {
		// 创建新的 Cohort
		return CreateCohort(ctx, clusterName)
	}
	return nil
}

// CreateCohort 创建 Cohort
func CreateCohort(ctx context.Context, clusterName string) error {
	cohort := &kueuev1alpha1.Cohort{
		TypeMeta: metav1.TypeMeta{
			APIVersion: client.KueueGroup + "/" + client.KueueAlphaVersion,
			Kind:       CohortKind,
		},
		ObjectMeta: metav1.ObjectMeta{
			Name: CohortName,
			Labels: map[string]string{
				ManagedByLabel: ManagedByValue,
			},
		},
	}

	return client.CreateCohort(ctx, clusterName, cohort)
}

// UpdateWorkloadPriority 更新 Workload 的优先级
func UpdateWorkloadPriority(ctx context.Context, clusterName, namespace, jobUid, priority string) error {
	// 获取优先级对应的值
	priorityValue, exists := PriorityValueMap[priority]
	if !exists {
		return fmt.Errorf("不支持的优先级: %s", priority)
	}

	// 通过 job-uid 标签查找对应的 Workload
	workloads, err := client.ListWorkload(
		ctx, clusterName, namespace, []constack_openapi.LabelSelector{
			{Key: JobUidLabel, Op: "=", Value: jobUid}})
	if err != nil {
		return fmt.Errorf("查找 Workload 失败: %w", err)
	}

	if len(workloads) == 0 {
		return fmt.Errorf("未找到对应的 Workload，job-uid: %s", jobUid)
	}

	if len(workloads) > 1 {
		return fmt.Errorf("找到多个 Workload，job-uid: %s", jobUid)
	}

	workload := &workloads[0]

	// // 检查 Workload 是否已经被 admitted
	// for _, condition := range workload.Status.Conditions {
	// 	if condition.Type == "Admitted" && condition.Status == metav1.ConditionTrue {
	// 		return fmt.Errorf("Workload 已被 admitted 正在运行，不允许修改优先级")
	// 	}
	// }

	// 更新优先级
	workload.Spec.Priority = &priorityValue

	// 更新 Workload
	err = client.UpdateWorkload(ctx, clusterName, workload)
	if err != nil {
		return fmt.Errorf("更新 Workload 优先级失败: %w", err)
	}

	return nil
}
