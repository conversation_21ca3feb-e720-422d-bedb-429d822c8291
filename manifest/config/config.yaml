server:
  address:     ":8000"
  openapiPath: "/api.json"
  swaggerPath: "/swagger"
  mode: develop
  dumpRouterMap: false
  accessLogEnabled: true

logger:
  level : "all"
  stdout: true

grpc:
  port: 8108

app:
  # jwtAuthTimeout: hour
  jwtAuthTimeout: 720
  secreteKey: dHQtaW50ZWxsaWZvcmdlCg==
  # echo "intelliforge" |base64
  encryptKey: dHQtaW50ZWxsaWZvcmdlCg==


database:
  logger:
    level:   "all"
    stdout:  true
    ctxKeys: ["RequestId"]
  default:
    link: "mysql:root:YUE4MTYwODA2Ny4K@tcp(*************:6033)/mlops?charset=utf8mb4&loc=Local&parseTime=true"
    debug: true
    charset: utf8mb4
    maxIdel: 10
    maxOpen: 200

redis:
  # 单实例
  default:
    address: *************:6379
    #    address: 127.0.0.1:6379
    db:      1
    pass:    YUE4MTYwODA2Ny4K
    #    pass:
    idleTimeout: 5m
    maxActive: 200
    readTimeout: 0
    minIdle: 10

api:
  cicd:
    authorization: bWxvcHM=.faab7f5cacbd92272c947eef87e5c7280676965ff302925e4c37a0851dca7c3a
  cmdb:
    authorization: iqNOUYbEJnBJFFe2di1n1T87ssTxO1UXRZo5iIHY8Sh119IWQwsgHcQmyo49rWQd
  constack:
    # prod
    authorization: Y29udGludW91c2RlcGxveW1lbnQ=.d79ca23c2e67bc202a1fdbc026673fdb832ddadb5dc4ad6b227d380943fed7875d5c47c9bce35374fc86a98d648ea646
    host: https://cloud.ttyuyin.com

    # local
#    authorization: cG9ydGZvcndhcmQtcHJveHk=.c29b1605a381c1adadbd63f236d62c94d0a24a9f434545626aa07dae29baa3fd079b9800e52091ac7ef281893827e648
#    host: http://127.0.0.1:8000
