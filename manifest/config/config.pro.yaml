server:
  address:     ":8000"
  openapiPath: "/api.json"
  swaggerPath: "/swagger"
  mode: product
  dumpRouterMap: false
  accessLogEnabled: true

logger:
  level : "all"
  stdout: true

grpc:
  port: 8108

app:
  # jwtAuthTimeout: hour
  jwtAuthTimeout: 720
  secreteKey: dHQtaW50ZWxsaWZvcmdlCg==
  # echo "intelliforge" |base64
  encryptKey: dHQtaW50ZWxsaWZvcmdlCg==


database:
  logger:
    level:   "all"
    stdout:  true
    ctxKeys: ["RequestId"]

  default:
    link:   "mysql:tech_ops_work:brLrKlTsI9QIyLjqFSeB@tcp(************:3306)/mlops?charset=utf8mb4&loc=Local&parseTime=true"
    debug:  false
    charset: utf8mb4
    maxIdel: 10
    maxOpen: 500

redis:
  # 单实例
  default:
    address: *************:6379
    #    address: 127.0.0.1:6379
    db:      5
    pass:    YUE4MTYwODA2Ny4K
    #    pass:
    idleTimeout: 5m
    maxActive: 200
    readTimeout: 0
    minIdle: 10

api:
  cicd:
    authorization: c2hlbmpp.abcd8d49882ae33a09a0b1909b597877f7dd7a20042341898dbba79a73700c09
  cmdb:
    authorization: iqNOUYbEJnBJFFe2di1n1T87ssTxO1UXRZo5iIHY8Sh119IWQwsgHcQmyo49rWQd

gitlab:
  url: "https://gitlab.ttyuyin.com"
  sshUrl: "**********************"
  superToken: "********************"
  aiReviewRobotToken: cnDHCBDhW3osJVxih_tF

aiReview:
  gitlabUser: 1312 # 啄木鸟用户id
  webhookUrl: "http://yw-intelliforge.ttyuyin.com/pr-agent/webhook" # ai review webhook 地址

mcp:
  url: "http://**************:8000"