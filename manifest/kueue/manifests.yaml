apiVersion: kueue.x-k8s.io/v1beta1
kind: ClusterQueue
metadata:
  name: clusterqueue-infra
spec:
  cohort: mlops-shared
  namespaceSelector:
    matchLabels:
      kubernetes.io/metadata.name: infra
  resourceGroups:
    - coveredResources: [ "tke.cloud.tencent.com/qgpu-core","tke.cloud.tencent.com/qgpu-memory", "cpu", "memory"]
      flavors:
        - name: l20-gpu-pool
          resources:
            - name: tke.cloud.tencent.com/qgpu-core
              nominalQuota: 20
            - name: tke.cloud.tencent.com/qgpu-memory
              nominalQuota: 20
            - name: cpu
              nominalQuota: 20
            - name: memory
              nominalQuota: 20Gi

---
apiVersion: kueue.x-k8s.io/v1beta1
kind: LocalQueue
metadata:
  name: infra
  namespace: infra
spec:
  clusterQueue: clusterqueue-infra

---

apiVersion: kueue.x-k8s.io/v1beta1
kind: ResourceFlavor
metadata:
  name: l20-gpu-pool
spec:
  nodeLabels:
    gpu-model: l20
  tolerations:
    - key: pool-type
      operator: Equal
      value: gpu
      effect: NoSchedule


---
apiVersion: kueue.x-k8s.io/v1beta1
kind: WorkloadPriorityClass
metadata:
  name: gpu-train-p1
spec:
  value: 1900
---
apiVersion: kueue.x-k8s.io/v1beta1
kind: WorkloadPriorityClass
metadata:
  name: gpu-train-p2
spec:
  value: 1800
---
apiVersion: kueue.x-k8s.io/v1beta1
kind: WorkloadPriorityClass
metadata:
  name: gpu-train-p3
spec:
  value: 1700
---
apiVersion: kueue.x-k8s.io/v1beta1
kind: WorkloadPriorityClass
metadata:
  name: gpu-train-p4
spec:
  value: 1600


