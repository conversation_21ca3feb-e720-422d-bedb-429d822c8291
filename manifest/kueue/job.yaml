apiVersion: batch/v1
kind: Job
metadata:
  name: gpu-train-job
  namespace: infra
  labels:
    kueue.x-k8s.io/queue-name: infra
spec:
  template:
    metadata:
      annotations:
        kueue.x-k8s.io/parent-workload: ""
        sidecar.istio.io/inject: "false"
    spec:
      restartPolicy: Never
      tolerations:
        - key: pool-type
          operator: Equal
          value: gpu
          effect: NoSchedule
      containers:
        - name: train
          image: cr.ttyuyin.com/develop-tools/codercom/cuda-code-server-local:cu124
          command: ["bash", "-c", "ls && sleep 300"]
          resources:
            requests:
              tke.cloud.tencent.com/qgpu-core: 6
              tke.cloud.tencent.com/qgpu-memory: 5
              cpu: "1"
              memory: "1Gi"
            limits:
              tke.cloud.tencent.com/qgpu-core: 6
              tke.cloud.tencent.com/qgpu-memory: 5
              cpu: "1"
              memory: "1Gi"
