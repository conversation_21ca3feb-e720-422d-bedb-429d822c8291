apiVersion: kubeflow.org/v2beta1
kind: MPIJob
metadata:
  name: mpijob-ml-cpu
  namespace: default
  labels:
    ml_team_id: "1"
    ml_exec_name: "mpijob-ml-cpu"
    ml_env: "dev"
    kueue.x-k8s.io/queue-name: "default-queue"
    kueue.x-k8s.io/priority-class: "normal-priority"
spec:
  slotsPerWorker: 1
  runPolicy:
    cleanPodPolicy: Running
    ttlSecondsAfterFinished: 60
  mpiReplicaSpecs:
    Launcher:
      replicas: 1
      template:
        metadata:
          annotations:
            sidecar.istio.io/inject: "false"
          labels:
            ml_team_id: "1"
            ml_exec_name: "mpijob-ml-cpu"
            ml_env: "dev"
        spec:
          restartPolicy: Never
          containers:
          - name: mpi-launcher
            image: python:3.9-slim
            command:
            - /bin/bash
            - -c
            - |
              # 安装必要的包
              pip install mpi4py numpy scikit-learn
              
              # 创建训练脚本
              cat > /tmp/mpi_training.py << 'EOF'
              import numpy as np
              from sklearn.datasets import make_classification
              from sklearn.linear_model import LogisticRegression
              from sklearn.model_selection import train_test_split
              from sklearn.metrics import accuracy_score
              from mpi4py import MPI
              import time
              
              def main():
                  # 初始化MPI
                  comm = MPI.COMM_WORLD
                  rank = comm.Get_rank()
                  size = comm.Get_size()
                  
                  print(f"Process {rank}/{size}: Starting MPI ML training")
                  
                  # 生成数据集
                  if rank == 0:
                      print("Generating dataset...")
                      X, y = make_classification(
                          n_samples=10000,
                          n_features=20,
                          n_informative=10,
                          n_redundant=10,
                          n_clusters_per_class=1,
                          random_state=42
                      )
                      print(f"Dataset shape: {X.shape}")
                  else:
                      X, y = None, None
                  
                  # 广播数据到所有进程
                  X = comm.bcast(X, root=0)
                  y = comm.bcast(y, root=0)
                  
                  # 每个进程处理数据的一部分
                  chunk_size = len(X) // size
                  start_idx = rank * chunk_size
                  end_idx = start_idx + chunk_size if rank < size - 1 else len(X)
                  
                  X_local = X[start_idx:end_idx]
                  y_local = y[start_idx:end_idx]
                  
                  print(f"Process {rank}: Processing {len(X_local)} samples")
                  
                  # 分割训练和测试数据
                  X_train, X_test, y_train, y_test = train_test_split(
                      X_local, y_local, test_size=0.2, random_state=42
                  )
                  
                  # 训练模型
                  print(f"Process {rank}: Training model...")
                  model = LogisticRegression(random_state=42, max_iter=1000)
                  
                  # 模拟训练过程
                  for epoch in range(5):
                      model.fit(X_train, y_train)
                      train_acc = model.score(X_train, y_train)
                      test_acc = model.score(X_test, y_test)
                      
                      print(f"Process {rank}, Epoch {epoch+1}: Train Acc={train_acc:.4f}, Test Acc={test_acc:.4f}")
                      time.sleep(1)  # 模拟训练时间
                  
                  # 收集所有进程的准确率
                  final_acc = model.score(X_test, y_test)
                  all_accuracies = comm.gather(final_acc, root=0)
                  
                  if rank == 0:
                      avg_accuracy = np.mean(all_accuracies)
                      print(f"\n=== Training Results ===")
                      print(f"Individual accuracies: {[f'{acc:.4f}' for acc in all_accuracies]}")
                      print(f"Average accuracy: {avg_accuracy:.4f}")
                      print("MPI ML training completed successfully!")
                  
                  comm.Barrier()
                  print(f"Process {rank}: Finished")
              
              if __name__ == "__main__":
                  main()
              EOF
              
              # 运行MPI训练
              mpirun --allow-run-as-root -np 2 python /tmp/mpi_training.py
            resources:
              requests:
                cpu: "500m"
                memory: "1Gi"
              limits:
                cpu: "1000m"
                memory: "2Gi"
            env:
            - name: OMPI_ALLOW_RUN_AS_ROOT
              value: "1"
            - name: OMPI_ALLOW_RUN_AS_ROOT_CONFIRM
              value: "1"
    Worker:
      replicas: 2
      template:
        metadata:
          annotations:
            sidecar.istio.io/inject: "false"
          labels:
            ml_team_id: "1"
            ml_exec_name: "mpijob-ml-cpu"
            ml_env: "dev"
        spec:
          restartPolicy: Never
          containers:
          - name: mpi-worker
            image: python:3.9-slim
            command:
            - /bin/bash
            - -c
            - |
              # 安装MPI和SSH
              apt-get update && apt-get install -y openssh-server openmpi-bin
              
              # 配置SSH
              mkdir -p /var/run/sshd
              echo 'root:mpiuser' | chpasswd
              sed -i 's/#PermitRootLogin prohibit-password/PermitRootLogin yes/' /etc/ssh/sshd_config
              sed -i 's/#PasswordAuthentication yes/PasswordAuthentication yes/' /etc/ssh/sshd_config
              
              # 启动SSH服务
              /usr/sbin/sshd -D
            resources:
              requests:
                cpu: "200m"
                memory: "512Mi"
              limits:
                cpu: "500m"
                memory: "1Gi"
            ports:
            - containerPort: 22
              name: sshd
            env:
            - name: OMPI_ALLOW_RUN_AS_ROOT
              value: "1"
            - name: OMPI_ALLOW_RUN_AS_ROOT_CONFIRM
              value: "1"
            readinessProbe:
              tcpSocket:
                port: 22
              initialDelaySeconds: 10
              periodSeconds: 10
            livenessProbe:
              tcpSocket:
                port: 22
              initialDelaySeconds: 20
              periodSeconds: 20
