apiVersion: kubeflow.org/v1
kind: MPIJob
metadata:
  name: mpijob-cpu-training
  namespace: default
  labels:
    ml_team_id: "1"
    ml_exec_name: "mpijob-cpu-training"
    ml_env: "dev"
    kueue.x-k8s.io/queue-name: "default-queue"
    kueue.x-k8s.io/priority-class: "normal-priority"
spec:
  slotsPerWorker: 1
  runPolicy:
    cleanPodPolicy: Running
    ttlSecondsAfterFinished: 60
  mpiReplicaSpecs:
    Launcher:
      replicas: 1
      template:
        metadata:
          annotations:
            sidecar.istio.io/inject: "false"
          labels:
            ml_team_id: "1"
            ml_exec_name: "mpijob-cpu-training"
            ml_env: "dev"
        spec:
          restartPolicy: Never
          containers:
          - name: mpi-launcher
            image: mpioperator/mpi-pi:openmpi
            command:
            - mpirun
            args:
            - --allow-run-as-root
            - -np
            - "2"
            - -H
            - mpijob-cpu-training-worker-0:1,mpijob-cpu-training-worker-1:1
            - python3
            - -c
            - |
              import time
              import os
              from mpi4py import MPI
              
              # 初始化MPI
              comm = MPI.COMM_WORLD
              rank = comm.Get_rank()
              size = comm.Get_size()
              
              print(f"Hello from rank {rank} of {size}")
              
              # 模拟CPU密集型训练任务
              print(f"Rank {rank}: Starting CPU training simulation...")
              
              # 简单的矩阵计算模拟训练
              import numpy as np
              
              # 生成随机数据
              data_size = 1000
              X = np.random.randn(data_size, 10)
              y = np.random.randn(data_size, 1)
              
              # 模拟训练循环
              epochs = 10
              for epoch in range(epochs):
                  # 模拟前向传播
                  weights = np.random.randn(10, 1)
                  predictions = np.dot(X, weights)
                  
                  # 模拟损失计算
                  loss = np.mean((predictions - y) ** 2)
                  
                  # MPI通信：收集所有进程的损失
                  all_losses = comm.gather(loss, root=0)
                  
                  if rank == 0:
                      avg_loss = np.mean(all_losses)
                      print(f"Epoch {epoch + 1}/{epochs}, Average Loss: {avg_loss:.4f}")
                  
                  # 同步所有进程
                  comm.Barrier()
                  
                  # 模拟训练延迟
                  time.sleep(1)
              
              if rank == 0:
                  print("Training completed successfully!")
                  print("MPI CPU training job finished.")
            resources:
              requests:
                cpu: "500m"
                memory: "512Mi"
              limits:
                cpu: "1000m"
                memory: "1Gi"
            env:
            - name: OMPI_ALLOW_RUN_AS_ROOT
              value: "1"
            - name: OMPI_ALLOW_RUN_AS_ROOT_CONFIRM
              value: "1"
    Worker:
      replicas: 2
      template:
        metadata:
          annotations:
            sidecar.istio.io/inject: "false"
          labels:
            ml_team_id: "1"
            ml_exec_name: "mpijob-cpu-training"
            ml_env: "dev"
        spec:
          restartPolicy: Never
          containers:
          - name: mpi-worker
            image: mpioperator/mpi-pi:openmpi
            command:
            - /usr/sbin/sshd
            args:
            - -De
            - -f
            - /home/<USER>/.sshd_config
            resources:
              requests:
                cpu: "500m"
                memory: "512Mi"
              limits:
                cpu: "1000m"
                memory: "1Gi"
            ports:
            - containerPort: 2022
              name: sshd
            env:
            - name: OMPI_ALLOW_RUN_AS_ROOT
              value: "1"
            - name: OMPI_ALLOW_RUN_AS_ROOT_CONFIRM
              value: "1"
            readinessProbe:
              tcpSocket:
                port: 2022
              initialDelaySeconds: 5
              periodSeconds: 10
            livenessProbe:
              tcpSocket:
                port: 2022
              initialDelaySeconds: 15
              periodSeconds: 20
