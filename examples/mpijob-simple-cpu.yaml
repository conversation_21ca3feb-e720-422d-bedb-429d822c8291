apiVersion: kubeflow.org/v2beta1
kind: MPIJob
metadata:
  name: mpijob-simple-cpu
  namespace: default
  labels:
    ml_team_id: "1"
    ml_exec_name: "mpijob-simple-cpu"
    ml_env: "dev"
    kueue.x-k8s.io/queue-name: "default-queue"
    kueue.x-k8s.io/priority-class: "normal-priority"
spec:
  slotsPerWorker: 1
  runPolicy:
    cleanPodPolicy: Running
    ttlSecondsAfterFinished: 60
  mpiReplicaSpecs:
    Launcher:
      replicas: 1
      template:
        metadata:
          annotations:
            sidecar.istio.io/inject: "false"
          labels:
            ml_team_id: "1"
            ml_exec_name: "mpijob-simple-cpu"
            ml_env: "dev"
        spec:
          restartPolicy: Never
          containers:
          - name: mpi-launcher
            image: mpioperator/mpi-pi:openmpi
            command:
            - mpirun
            args:
            - --allow-run-as-root
            - -np
            - "2"
            - -H
            - mpijob-simple-cpu-worker-0:1,mpijob-simple-cpu-worker-1:1
            - /bin/bash
            - -c
            - |
              echo "Starting MPI CPU training job..."
              echo "MPI Rank: $OMPI_COMM_WORLD_RANK"
              echo "MPI Size: $OMPI_COMM_WORLD_SIZE"
              
              # 模拟CPU密集型计算
              echo "Performing CPU-intensive calculations..."
              
              # 使用bc进行数学计算模拟训练
              for i in {1..10}; do
                echo "Training epoch $i/10"
                # 计算π的近似值（CPU密集型任务）
                result=$(echo "scale=6; 4*a(1)" | bc -l)
                echo "Epoch $i: Computed π ≈ $result"
                sleep 2
              done
              
              echo "MPI CPU training completed successfully!"
              echo "Final result: Training finished on $(date)"
            resources:
              requests:
                cpu: "200m"
                memory: "256Mi"
              limits:
                cpu: "500m"
                memory: "512Mi"
            env:
            - name: OMPI_ALLOW_RUN_AS_ROOT
              value: "1"
            - name: OMPI_ALLOW_RUN_AS_ROOT_CONFIRM
              value: "1"
    Worker:
      replicas: 2
      template:
        metadata:
          annotations:
            sidecar.istio.io/inject: "false"
          labels:
            ml_team_id: "1"
            ml_exec_name: "mpijob-simple-cpu"
            ml_env: "dev"
        spec:
          restartPolicy: Never
          containers:
          - name: mpi-worker
            image: mpioperator/mpi-pi:openmpi
            command:
            - /usr/sbin/sshd
            args:
            - -De
            - -f
            - /home/<USER>/.sshd_config
            resources:
              requests:
                cpu: "200m"
                memory: "256Mi"
              limits:
                cpu: "500m"
                memory: "512Mi"
            ports:
            - containerPort: 2022
              name: sshd
            env:
            - name: OMPI_ALLOW_RUN_AS_ROOT
              value: "1"
            - name: OMPI_ALLOW_RUN_AS_ROOT_CONFIRM
              value: "1"
            readinessProbe:
              tcpSocket:
                port: 2022
              initialDelaySeconds: 5
              periodSeconds: 10
            livenessProbe:
              tcpSocket:
                port: 2022
              initialDelaySeconds: 15
              periodSeconds: 20
