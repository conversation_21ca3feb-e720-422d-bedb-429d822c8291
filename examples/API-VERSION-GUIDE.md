# Kubeflow Training API版本使用指南

本文档说明了不同训练框架应该使用的API版本。

## API版本总览

### 当前推荐的API版本

| 训练框架 | API版本 | Operator | 状态 |
|---------|---------|----------|------|
| **MPIJob** | `kubeflow.org/v2beta1` | MPI Operator | ✅ 推荐 |
| **TFJob** | `kubeflow.org/v1` | Training Operator | ✅ 稳定 |
| **PyTorchJob** | `kubeflow.org/v1` | Training Operator | ✅ 稳定 |

## 详细说明

### 1. MPIJob - 使用 v2beta1

#### **为什么使用v2beta1？**
- MPI Operator是独立的operator，有自己的版本演进
- v2beta1提供了更好的功能和稳定性
- 官方推荐使用v2beta1版本

#### **YAML示例**：
```yaml
apiVersion: kubeflow.org/v2beta1
kind: MPIJob
metadata:
  name: mpi-training-job
spec:
  slotsPerWorker: 1
  runPolicy:
    cleanPodPolicy: Running
    ttlSecondsAfterFinished: 60
    backoffLimit: 3  # v2beta1新特性
  mpiReplicaSpecs:
    Launcher:
      replicas: 1
      restartPolicy: Never  # v2beta1新特性
      template:
        # Pod模板
    Worker:
      replicas: 2
      restartPolicy: Never  # v2beta1新特性
      template:
        # Pod模板
```

### 2. TFJob - 使用 v1

#### **为什么使用v1？**
- Training Operator目前稳定支持v1版本
- v1版本功能完整，生产环境验证充分
- 与现有代码库兼容

#### **YAML示例**：
```yaml
apiVersion: kubeflow.org/v1
kind: TFJob
metadata:
  name: tensorflow-training-job
spec:
  tfReplicaSpecs:
    Worker:
      replicas: 2
      template:
        # Pod模板
```

### 3. PyTorchJob - 使用 v1

#### **为什么使用v1？**
- Training Operator目前稳定支持v1版本
- v1版本功能完整，生产环境验证充分
- 与现有代码库兼容

#### **YAML示例**：
```yaml
apiVersion: kubeflow.org/v1
kind: PyTorchJob
metadata:
  name: pytorch-training-job
spec:
  pytorchReplicaSpecs:
    Master:
      replicas: 1
      template:
        # Pod模板
    Worker:
      replicas: 2
      template:
        # Pod模板
```

## 代码库配置

### Go模块依赖

```go
// go.mod
require (
    github.com/kubeflow/training-operator v1.8.1  // TFJob, PyTorchJob
    // MPI Operator通过training-operator间接支持
)
```

### Go代码导入

```go
// 统一使用v1 API
import (
    trainingv1 "github.com/kubeflow/training-operator/pkg/apis/kubeflow.org/v1"
)

// 使用示例
tfJob := &trainingv1.TFJob{...}
pyTorchJob := &trainingv1.PyTorchJob{...}
mpiJob := &trainingv1.MPIJob{...}  // 注意：代码中仍使用v1结构
```

### 客户端配置

```go
// tools/client/kubeflow_training.go
const (
    TrainingGroup   = "kubeflow.org"
    TrainingVersion = "v1"  // 客户端统一使用v1
)

// 资源类型
const (
    TFJobKind      = "tfjobs"
    PyTorchJobKind = "pytorchjobs"
    MPIJobKind     = "mpijobs"
)
```

## 部署配置

### 1. 安装Training Operator

```bash
# 安装Training Operator (支持TFJob, PyTorchJob, MPIJob v1)
kubectl apply -k "github.com/kubeflow/training-operator/manifests/overlays/standalone"
```

### 2. 安装MPI Operator (可选)

```bash
# 如果需要MPIJob v2beta1的高级特性，可以额外安装MPI Operator
kubectl apply -f https://raw.githubusercontent.com/kubeflow/mpi-operator/master/deploy/v2beta1/mpi-operator.yaml
```

## 版本兼容性

### 支持的Kubernetes版本
- Kubernetes 1.21+
- Training Operator v1.8.1
- MPI Operator v0.6.0+ (如果使用v2beta1)

### 向前兼容性
- 现有的v1 MPIJob可以继续使用
- 新项目推荐使用v2beta1 MPIJob
- TFJob和PyTorchJob继续使用v1

## 迁移指南

### 从MPIJob v1迁移到v2beta1

#### **主要变化**：
1. **API版本更新**：
   ```yaml
   # 旧版本
   apiVersion: kubeflow.org/v1
   
   # 新版本
   apiVersion: kubeflow.org/v2beta1
   ```

2. **新增字段**：
   ```yaml
   spec:
     runPolicy:
       backoffLimit: 3  # 新增：重试次数限制
     mpiReplicaSpecs:
       Launcher:
         restartPolicy: Never  # 新增：重启策略
       Worker:
         restartPolicy: Never  # 新增：重启策略
   ```

3. **增强的健康检查**：
   ```yaml
   readinessProbe:
     tcpSocket:
       port: 2022
     initialDelaySeconds: 5
     periodSeconds: 10
     timeoutSeconds: 5      # 新增：超时配置
     successThreshold: 1    # 新增：成功阈值
     failureThreshold: 3    # 新增：失败阈值
   ```

### 代码无需修改
- Go代码中继续使用`trainingv1.MPIJob`
- 客户端API调用保持不变
- 只需要更新YAML配置文件

## 最佳实践

### 1. 新项目推荐配置

```yaml
# 推荐的MPIJob配置 (v2beta1)
apiVersion: kubeflow.org/v2beta1
kind: MPIJob
metadata:
  name: my-mpi-job
  labels:
    ml_team_id: "1"
    ml_exec_name: "my-mpi-job"
    ml_env: "prod"
spec:
  slotsPerWorker: 1
  runPolicy:
    cleanPodPolicy: Running
    ttlSecondsAfterFinished: 60
    backoffLimit: 3
  mpiReplicaSpecs:
    Launcher:
      replicas: 1
      restartPolicy: Never
      template:
        metadata:
          annotations:
            sidecar.istio.io/inject: "false"
        spec:
          restartPolicy: Never
          containers:
          - name: mpi-launcher
            # 容器配置
    Worker:
      replicas: 2
      restartPolicy: Never
      template:
        metadata:
          annotations:
            sidecar.istio.io/inject: "false"
        spec:
          restartPolicy: Never
          containers:
          - name: mpi-worker
            # 容器配置
```

### 2. 统一的标签和注解

```yaml
metadata:
  labels:
    ml_team_id: "1"
    ml_exec_name: "job-name"
    ml_env: "prod"
    kueue.x-k8s.io/queue-name: "default-queue"
    kueue.x-k8s.io/priority-class: "normal-priority"
  annotations:
    sidecar.istio.io/inject: "false"
```

## 总结

- ✅ **MPIJob**: 使用`kubeflow.org/v2beta1`获得最新特性
- ✅ **TFJob**: 使用`kubeflow.org/v1`保持稳定性
- ✅ **PyTorchJob**: 使用`kubeflow.org/v1`保持稳定性
- ✅ **代码库**: 继续使用统一的v1 API导入
- ✅ **部署**: 根据需要选择安装对应的operator

这种配置既保证了稳定性，又能利用最新的MPIJob特性。
