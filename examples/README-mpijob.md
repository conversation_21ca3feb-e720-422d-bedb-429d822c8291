# MPIJob CPU训练示例

本目录包含了三个可以运行的MPIJob YAML配置文件，专门用于CPU资源的分布式训练。

## 文件说明

### 1. mpijob-simple-cpu.yaml
**最简单的MPIJob示例**

- **用途**: 基础的MPI任务，适合测试MPI环境
- **特点**: 
  - 使用shell脚本进行简单的数学计算
  - 资源需求最小（200m CPU, 256Mi内存）
  - 运行时间短（约30秒）
- **适用场景**: 验证MPI环境是否正常工作

### 2. mpijob-cpu-training.yaml
**中等复杂度的MPI训练示例**

- **用途**: 使用Python和mpi4py进行分布式计算
- **特点**:
  - 包含真实的MPI通信（gather, barrier）
  - 模拟机器学习训练过程
  - 使用NumPy进行矩阵计算
- **适用场景**: 学习MPI编程模式

### 3. mpijob-ml-cpu.yaml
**完整的机器学习训练示例**

- **用途**: 使用scikit-learn进行分布式机器学习训练
- **特点**:
  - 真实的机器学习工作负载
  - 数据分片和模型训练
  - 结果聚合和评估
- **适用场景**: 生产环境的ML训练任务

## 使用方法

### 前置条件

1. **Kubernetes集群**: 确保有可用的Kubernetes集群
2. **MPI Operator**: 集群中需要安装Kubeflow MPI Operator
3. **资源配额**: 确保有足够的CPU和内存资源

### 部署步骤

#### 1. 部署简单示例
```bash
kubectl apply -f mpijob-simple-cpu.yaml
```

#### 2. 查看Job状态
```bash
kubectl get mpijobs
kubectl describe mpijob mpijob-simple-cpu
```

#### 3. 查看Pod状态
```bash
kubectl get pods -l job-name=mpijob-simple-cpu
```

#### 4. 查看日志
```bash
# 查看Launcher日志
kubectl logs -l job-name=mpijob-simple-cpu,replica-type=launcher

# 查看Worker日志
kubectl logs -l job-name=mpijob-simple-cpu,replica-type=worker
```

#### 5. 清理资源
```bash
kubectl delete mpijob mpijob-simple-cpu
```

## 资源配置

### 最小资源配置（mpijob-simple-cpu.yaml）
```yaml
resources:
  requests:
    cpu: "200m"
    memory: "256Mi"
  limits:
    cpu: "500m"
    memory: "512Mi"
```

### 标准资源配置（mpijob-cpu-training.yaml）
```yaml
resources:
  requests:
    cpu: "500m"
    memory: "512Mi"
  limits:
    cpu: "1000m"
    memory: "1Gi"
```

### 高性能配置（mpijob-ml-cpu.yaml）
```yaml
resources:
  requests:
    cpu: "500m"
    memory: "1Gi"
  limits:
    cpu: "1000m"
    memory: "2Gi"
```

## 关键配置说明

### 1. MPI配置
```yaml
spec:
  slotsPerWorker: 1  # 每个Worker的MPI槽位数
  runPolicy:
    cleanPodPolicy: Running  # Pod清理策略
    ttlSecondsAfterFinished: 60  # 完成后保留时间
```

### 2. 标签配置
```yaml
labels:
  ml_team_id: "1"  # 团队ID
  ml_exec_name: "mpijob-simple-cpu"  # 执行名称
  ml_env: "dev"  # 环境标识
  kueue.x-k8s.io/queue-name: "default-queue"  # Kueue队列
  kueue.x-k8s.io/priority-class: "normal-priority"  # 优先级
```

### 3. Istio配置
```yaml
annotations:
  sidecar.istio.io/inject: "false"  # 禁用Istio sidecar
```

## 故障排除

### 常见问题

1. **Pod启动失败**
   ```bash
   # 检查Pod事件
   kubectl describe pod <pod-name>
   
   # 检查资源配额
   kubectl describe quota
   ```

2. **MPI通信失败**
   ```bash
   # 检查网络连接
   kubectl exec -it <launcher-pod> -- ping <worker-pod-ip>
   
   # 检查SSH连接
   kubectl exec -it <launcher-pod> -- ssh <worker-hostname>
   ```

3. **训练任务失败**
   ```bash
   # 查看详细日志
   kubectl logs <launcher-pod> --previous
   
   # 检查环境变量
   kubectl exec -it <launcher-pod> -- env | grep OMPI
   ```

### 调试命令

```bash
# 进入Launcher Pod进行调试
kubectl exec -it <launcher-pod> -- /bin/bash

# 手动运行MPI命令
mpirun --allow-run-as-root -np 2 hostname

# 检查MPI环境
ompi_info --all
```

## 性能优化

### 1. CPU优化
- 根据实际工作负载调整CPU请求和限制
- 考虑使用CPU亲和性配置
- 监控CPU使用率

### 2. 内存优化
- 根据数据集大小调整内存配置
- 避免内存泄漏
- 使用内存映射文件处理大数据集

### 3. 网络优化
- 确保Pod间网络延迟最小
- 考虑使用高性能网络插件
- 优化MPI通信模式

## 监控和日志

### 1. 监控指标
- CPU使用率
- 内存使用率
- 网络I/O
- MPI通信延迟

### 2. 日志收集
```bash
# 收集所有相关日志
kubectl logs -l job-name=<mpijob-name> --all-containers=true

# 导出日志到文件
kubectl logs <pod-name> > training.log
```

## 扩展示例

### 自定义训练脚本
可以通过ConfigMap挂载自定义训练脚本：

```yaml
volumes:
- name: training-script
  configMap:
    name: mpi-training-script
volumeMounts:
- name: training-script
  mountPath: /scripts
```

### 数据持久化
使用PVC挂载数据和模型：

```yaml
volumes:
- name: data-volume
  persistentVolumeClaim:
    claimName: training-data-pvc
volumeMounts:
- name: data-volume
  mountPath: /data
```

这些示例提供了从简单到复杂的MPIJob使用场景，可以根据实际需求选择合适的配置。
