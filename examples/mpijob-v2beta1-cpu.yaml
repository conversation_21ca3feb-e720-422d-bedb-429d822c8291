apiVersion: kubeflow.org/v2beta1
kind: MPIJob
metadata:
  name: mpijob-v2beta1-cpu
  namespace: default
  labels:
    ml_team_id: "1"
    ml_exec_name: "mpijob-v2beta1-cpu"
    ml_env: "dev"
    kueue.x-k8s.io/queue-name: "default-queue"
    kueue.x-k8s.io/priority-class: "normal-priority"
spec:
  slotsPerWorker: 1
  runPolicy:
    cleanPodPolicy: Running
    ttlSecondsAfterFinished: 60
    backoffLimit: 3
  mpiReplicaSpecs:
    Launcher:
      replicas: 1
      restartPolicy: Never
      template:
        metadata:
          annotations:
            sidecar.istio.io/inject: "false"
          labels:
            ml_team_id: "1"
            ml_exec_name: "mpijob-v2beta1-cpu"
            ml_env: "dev"
        spec:
          restartPolicy: Never
          containers:
          - name: mpi-launcher
            image: mpioperator/mpi-pi:openmpi
            command:
            - mpirun
            args:
            - --allow-run-as-root
            - -np
            - "2"
            - -H
            - mpijob-v2beta1-cpu-worker-0:1,mpijob-v2beta1-cpu-worker-1:1
            - /bin/bash
            - -c
            - |
              echo "=== MPI v2beta1 CPU Training Job ==="
              echo "MPI Rank: $OMPI_COMM_WORLD_RANK"
              echo "MPI Size: $OMPI_COMM_WORLD_SIZE"
              echo "Hostname: $(hostname)"
              echo "Date: $(date)"
              
              echo "Starting distributed CPU training simulation..."
              
              # 模拟分布式训练任务
              for epoch in {1..5}; do
                echo "=== Epoch $epoch/5 ==="
                
                # 模拟数据加载
                echo "Loading training data..."
                sleep 1
                
                # 模拟前向传播
                echo "Forward pass..."
                result=$(echo "scale=4; $epoch * 3.14159 / 5" | bc -l)
                echo "Computed value: $result"
                sleep 1
                
                # 模拟反向传播
                echo "Backward pass..."
                sleep 1
                
                # 模拟参数更新
                echo "Parameter update..."
                sleep 1
                
                echo "Epoch $epoch completed"
                echo "---"
              done
              
              echo "=== Training Summary ==="
              echo "Total epochs: 5"
              echo "Training status: SUCCESS"
              echo "Final timestamp: $(date)"
              echo "MPI v2beta1 CPU training completed successfully!"
            resources:
              requests:
                cpu: "200m"
                memory: "256Mi"
              limits:
                cpu: "500m"
                memory: "512Mi"
            env:
            - name: OMPI_ALLOW_RUN_AS_ROOT
              value: "1"
            - name: OMPI_ALLOW_RUN_AS_ROOT_CONFIRM
              value: "1"
            - name: TRAINING_MODE
              value: "distributed"
            - name: LOG_LEVEL
              value: "INFO"
    Worker:
      replicas: 2
      restartPolicy: Never
      template:
        metadata:
          annotations:
            sidecar.istio.io/inject: "false"
          labels:
            ml_team_id: "1"
            ml_exec_name: "mpijob-v2beta1-cpu"
            ml_env: "dev"
        spec:
          restartPolicy: Never
          containers:
          - name: mpi-worker
            image: mpioperator/mpi-pi:openmpi
            command:
            - /usr/sbin/sshd
            args:
            - -De
            - -f
            - /home/<USER>/.sshd_config
            resources:
              requests:
                cpu: "200m"
                memory: "256Mi"
              limits:
                cpu: "500m"
                memory: "512Mi"
            ports:
            - containerPort: 2022
              name: sshd
              protocol: TCP
            env:
            - name: OMPI_ALLOW_RUN_AS_ROOT
              value: "1"
            - name: OMPI_ALLOW_RUN_AS_ROOT_CONFIRM
              value: "1"
            readinessProbe:
              tcpSocket:
                port: 2022
              initialDelaySeconds: 5
              periodSeconds: 10
              timeoutSeconds: 5
              successThreshold: 1
              failureThreshold: 3
            livenessProbe:
              tcpSocket:
                port: 2022
              initialDelaySeconds: 15
              periodSeconds: 20
              timeoutSeconds: 5
              successThreshold: 1
              failureThreshold: 3
            lifecycle:
              preStop:
                exec:
                  command:
                  - /bin/bash
                  - -c
                  - "echo 'Stopping MPI worker...'; sleep 5"
