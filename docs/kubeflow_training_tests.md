# Kubeflow Training Operator 测试文档

本文档描述了如何测试TFJob、PyTorchJob、MPIJob的CRUD操作和端到端功能。

## 测试概述

我们为Kubeflow Training Operator实现了三层测试：

1. **Client层CRUD测试** - 测试基础的增删改查操作
2. **Logic层集成测试** - 测试转换函数和业务逻辑
3. **端到端测试** - 测试完整的训练任务流程

## 测试文件结构

```
internal/logic/traintask/
├── kubeflow_training_test.go      # Client层CRUD测试
├── kubeflow_integration_test.go   # Logic层集成测试
└── kubeflow_e2e_test.go          # 端到端测试

scripts/
└── test_kubeflow_training.sh     # 测试运行脚本

docs/
└── kubeflow_training_tests.md    # 本文档
```

## 测试内容

### 1. Client层CRUD测试

测试文件：`kubeflow_training_test.go`

#### TFJob测试 (`TestTFJobCRUD`)
- ✅ 创建TFJob
- ✅ 获取TFJob
- ✅ 列出TFJob
- ✅ 等待Job完成
- ✅ 删除TFJob

#### PyTorchJob测试 (`TestPyTorchJobCRUD`)
- ✅ 创建PyTorchJob
- ✅ 获取PyTorchJob
- ✅ 列出PyTorchJob
- ✅ 等待Job完成
- ✅ 删除PyTorchJob

#### MPIJob测试 (`TestMPIJobCRUD`)
- ✅ 创建MPIJob（包含Launcher和Worker）
- ✅ 获取MPIJob
- ✅ 列出MPIJob
- ✅ 等待Job完成
- ✅ 删除MPIJob

### 2. Logic层集成测试

测试文件：`kubeflow_integration_test.go`

#### 转换函数测试
- ✅ `toTFJob` - 验证TFJob转换逻辑
- ✅ `toPyTorchJob` - 验证PyTorchJob转换逻辑
- ✅ `toMPIJob` - 验证MPIJob转换逻辑

#### 验证项目
- ✅ 标签设置（team_id、exec_name、ml_env、label_uuid）
- ✅ Istio sidecar禁用
- ✅ 资源配置
- ✅ 存储卷挂载
- ✅ 环境变量

### 3. 端到端测试

测试文件：`kubeflow_e2e_test.go`

#### 完整流程测试
- ✅ YAML任务类型支持
- ✅ Kueue标签设置
- ✅ Job创建和执行
- ✅ 状态监控
- ✅ 资源清理

## 运行测试

### 前置条件

1. **Kubernetes集群**：需要一个可用的Kubernetes集群
2. **Kubeflow Training Operator**：集群中需要安装Training Operator
3. **权限配置**：确保有足够的权限创建和管理Job资源
4. **镜像可用性**：确保测试镜像可以被拉取

### 快速运行

使用提供的脚本运行所有测试：

```bash
# 给脚本执行权限
chmod +x scripts/test_kubeflow_training.sh

# 运行所有测试
./scripts/test_kubeflow_training.sh
```

### 单独运行测试

#### 1. 运行Client层CRUD测试

```bash
# TFJob CRUD测试
go test -v ./internal/logic/traintask -run TestTFJobCRUD -timeout 10m

# PyTorchJob CRUD测试
go test -v ./internal/logic/traintask -run TestPyTorchJobCRUD -timeout 10m

# MPIJob CRUD测试
go test -v ./internal/logic/traintask -run TestMPIJobCRUD -timeout 10m
```

#### 2. 运行Logic层集成测试

```bash
go test -v ./internal/logic/traintask -run TestKubeflowTrainingIntegration -timeout 5m
```

#### 3. 运行端到端测试

```bash
go test -v ./internal/logic/traintask -run TestKubeflowE2E -timeout 15m
```

#### 4. 运行所有Kubeflow相关测试

```bash
go test -v ./internal/logic/traintask -run ".*Kubeflow.*" -timeout 20m
```

### 生成测试覆盖率报告

```bash
# 生成覆盖率报告
go test -v ./internal/logic/traintask -run ".*Kubeflow.*" -coverprofile=coverage.out -timeout 20m

# 生成HTML报告
go tool cover -html=coverage.out -o coverage.html

# 查看覆盖率
go tool cover -func=coverage.out
```

## 测试配置

### 环境变量

```bash
export ENV=test                    # 设置环境标签
export GO_ENV=test                 # Go测试环境
export TEST_CLUSTER=test-cluster   # 测试集群名称
export TEST_NAMESPACE=default      # 测试命名空间
```

### 测试参数

- **超时时间**：5-15分钟（根据测试类型）
- **集群名称**：`test-cluster`
- **命名空间**：`default`
- **重试间隔**：10秒

## 测试镜像

测试使用以下Docker镜像：

1. **TensorFlow**：`tensorflow/tensorflow:2.13.0`
2. **PyTorch**：`pytorch/pytorch:2.0.1-cuda11.7-cudnn8-runtime`
3. **MPI**：`mpioperator/mpi-pi:openmpi`

确保这些镜像在你的集群中可用。

## 故障排除

### 常见问题

1. **镜像拉取失败**
   - 检查镜像是否存在
   - 验证网络连接
   - 确认镜像拉取策略

2. **权限不足**
   - 检查RBAC配置
   - 验证ServiceAccount权限
   - 确认命名空间访问权限

3. **超时错误**
   - 增加测试超时时间
   - 检查集群资源是否充足
   - 验证调度器是否正常工作

4. **Job状态异常**
   - 查看Pod日志
   - 检查资源限制
   - 验证镜像和命令是否正确

### 调试命令

```bash
# 查看Job状态
kubectl get tfjobs,pytorchjobs,mpijobs -n default

# 查看Pod状态
kubectl get pods -n default -l job-name=<job-name>

# 查看Pod日志
kubectl logs -n default <pod-name>

# 查看Job详情
kubectl describe tfjob <job-name> -n default
```

## 测试结果验证

### 成功标准

1. **Job创建成功**：Job资源被正确创建
2. **Pod运行正常**：Pod状态为Running或Succeeded
3. **任务执行完成**：Job状态为Succeeded
4. **资源清理完成**：Job和Pod被正确删除

### 验证步骤

1. 检查Job状态：`kubectl get <job-type> -n default`
2. 查看Pod日志：确认训练任务执行成功
3. 验证标签：确认所有必需标签都已设置
4. 检查资源：确认CPU、内存、GPU配置正确

## 持续集成

可以将这些测试集成到CI/CD流程中：

```yaml
# .github/workflows/kubeflow-tests.yml
name: Kubeflow Training Tests
on: [push, pull_request]
jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - uses: actions/setup-go@v2
        with:
          go-version: 1.21
      - name: Run Kubeflow Tests
        run: ./scripts/test_kubeflow_training.sh
```

## 总结

这套测试确保了Kubeflow Training Operator的三种Job类型（TFJob、PyTorchJob、MPIJob）能够：

- ✅ 正常创建、获取、列出、删除
- ✅ 正确设置标签和配置
- ✅ 禁用Istio sidecar注入
- ✅ 支持Kueue调度
- ✅ 成功运行训练任务
- ✅ 完成端到端流程

通过这些测试，我们可以确信Kubeflow Training Operator的集成是稳定和可靠的。
